/**
 * Comprehensive Test for Multiple Zoom Effects Implementation
 * 
 * This test verifies that the multiple zoom effects functionality works correctly
 * including timeline interaction, playback, rendering, and conflict resolution.
 */

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:5173',
  testDuration: 10000, // 10 seconds test video
  fps: 30,
  zoomEffects: [
    {
      id: 'test-zoom-1',
      startTime: 1000,  // 1 second
      endTime: 3000,    // 3 seconds
      maxZoomScale: 2.0,
      zoomArea: { x: 0.2, y: 0.2, width: 0.4, height: 0.4 }
    },
    {
      id: 'test-zoom-2', 
      startTime: 4000,  // 4 seconds
      endTime: 6000,    // 6 seconds
      maxZoomScale: 1.5,
      zoomArea: { x: 0.4, y: 0.4, width: 0.3, height: 0.3 }
    },
    {
      id: 'test-zoom-3',
      startTime: 2500,  // 2.5 seconds (overlaps with zoom-1)
      endTime: 4500,    // 4.5 seconds (overlaps with zoom-2)
      maxZoomScale: 2.5,
      zoomArea: { x: 0.1, y: 0.1, width: 0.5, height: 0.5 }
    }
  ]
};

/**
 * Test Suite: Multiple Zoom Effects
 */
class MultipleZoomEffectsTest {
  constructor() {
    this.results = {
      storeOperations: { passed: 0, failed: 0, tests: [] },
      uiInteraction: { passed: 0, failed: 0, tests: [] },
      conflictResolution: { passed: 0, failed: 0, tests: [] },
      playbackRendering: { passed: 0, failed: 0, tests: [] }
    };
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Multiple Zoom Effects Test Suite...');
    
    try {
      await this.testStoreOperations();
      await this.testUIInteraction();
      await this.testConflictResolution();
      await this.testPlaybackRendering();
      
      this.printResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  /**
   * Test 1: Store Operations
   */
  async testStoreOperations() {
    console.log('\n📦 Testing Store Operations...');
    
    // Test adding zoom effects
    await this.test('Add multiple zoom effects', async () => {
      const store = window.useStore?.getState();
      if (!store) throw new Error('Store not available');
      
      // Clear existing effects
      store.zoomEffects.forEach(effect => store.removeZoomEffect(effect.id));
      
      // Add test effects
      const addedIds = [];
      for (const effect of TEST_CONFIG.zoomEffects) {
        const id = store.addZoomEffect({
          startTime: effect.startTime,
          endTime: effect.endTime,
          maxZoomScale: effect.maxZoomScale,
          zoomArea: effect.zoomArea
        });
        addedIds.push(id);
      }
      
      const currentEffects = store.zoomEffects;
      if (currentEffects.length !== TEST_CONFIG.zoomEffects.length) {
        throw new Error(`Expected ${TEST_CONFIG.zoomEffects.length} effects, got ${currentEffects.length}`);
      }
      
      return { addedIds, effectCount: currentEffects.length };
    }, 'storeOperations');

    // Test updating zoom effects
    await this.test('Update zoom effect properties', async () => {
      const store = window.useStore?.getState();
      const firstEffect = store.zoomEffects[0];
      
      const newStartTime = 500;
      store.updateZoomEffect(firstEffect.id, { startTime: newStartTime });
      
      const updatedEffect = store.getZoomEffectById(firstEffect.id);
      if (updatedEffect.startTime !== newStartTime) {
        throw new Error(`Update failed: expected ${newStartTime}, got ${updatedEffect.startTime}`);
      }
      
      return { updatedEffect };
    }, 'storeOperations');

    // Test removing zoom effects
    await this.test('Remove zoom effect', async () => {
      const store = window.useStore?.getState();
      const initialCount = store.zoomEffects.length;
      const effectToRemove = store.zoomEffects[0];
      
      store.removeZoomEffect(effectToRemove.id);
      
      const finalCount = store.zoomEffects.length;
      if (finalCount !== initialCount - 1) {
        throw new Error(`Expected ${initialCount - 1} effects, got ${finalCount}`);
      }
      
      return { removedId: effectToRemove.id, finalCount };
    }, 'storeOperations');
  }

  /**
   * Test 2: UI Interaction
   */
  async testUIInteraction() {
    console.log('\n🖱️ Testing UI Interaction...');
    
    // Test zoom effect overlay visibility
    await this.test('Zoom effect overlay renders correctly', async () => {
      const overlay = document.querySelector('[class*="zoom-effect-overlay"]') || 
                    document.querySelector('[data-testid="zoom-effect-overlay"]');
      
      if (!overlay) {
        // Look for zoom effect bars instead
        const zoomBars = document.querySelectorAll('[class*="zoom-effect"]');
        if (zoomBars.length === 0) {
          throw new Error('No zoom effect overlay or bars found in DOM');
        }
      }
      
      return { overlayFound: !!overlay, barsCount: document.querySelectorAll('[class*="zoom-effect"]').length };
    }, 'uiInteraction');

    // Test add zoom effect button
    await this.test('Add zoom effect button functionality', async () => {
      const addButton = document.querySelector('button:has([data-lucide="plus"])') ||
                       document.querySelector('button[title*="zoom"]') ||
                       document.querySelector('button:contains("Add Zoom")');
      
      if (!addButton) {
        throw new Error('Add zoom effect button not found');
      }
      
      const store = window.useStore?.getState();
      const initialCount = store?.zoomEffects?.length || 0;
      
      // Simulate click
      addButton.click();
      
      // Wait for state update
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const finalCount = store?.zoomEffects?.length || 0;
      
      return { initialCount, finalCount, buttonFound: true };
    }, 'uiInteraction');
  }

  /**
   * Test 3: Conflict Resolution
   */
  async testConflictResolution() {
    console.log('\n⚠️ Testing Conflict Resolution...');
    
    // Test overlapping detection
    await this.test('Detect overlapping zoom effects', async () => {
      const store = window.useStore?.getState();
      if (!store) throw new Error('Store not available');
      
      // Clear and add overlapping effects
      store.zoomEffects.forEach(effect => store.removeZoomEffect(effect.id));
      
      const effect1Id = store.addZoomEffect({ startTime: 1000, endTime: 3000 });
      const effect2Id = store.addZoomEffect({ startTime: 2000, endTime: 4000 }); // Overlaps with effect1
      
      const overlapping = store.getOverlappingZoomEffects(effect1Id);
      
      if (overlapping.length !== 1 || overlapping[0].id !== effect2Id) {
        throw new Error(`Expected 1 overlapping effect, got ${overlapping.length}`);
      }
      
      return { overlappingCount: overlapping.length, overlappingId: overlapping[0].id };
    }, 'conflictResolution');

    // Test conflict resolution priority
    await this.test('Most recent effect takes priority', async () => {
      const store = window.useStore?.getState();
      
      // Get active effects at overlapping time
      const activeEffects = store.getActiveZoomEffectsAtTime(2500); // Time where effects overlap
      
      if (activeEffects.length < 2) {
        throw new Error(`Expected at least 2 active effects at time 2500, got ${activeEffects.length}`);
      }
      
      // The last effect in the array should be the most recent (highest priority)
      const mostRecentEffect = activeEffects[activeEffects.length - 1];
      
      return { activeEffectsCount: activeEffects.length, mostRecentId: mostRecentEffect.id };
    }, 'conflictResolution');
  }

  /**
   * Test 4: Playback and Rendering
   */
  async testPlaybackRendering() {
    console.log('\n🎬 Testing Playback and Rendering...');
    
    // Test zoom scale calculation during playback
    await this.test('Zoom scale calculation at different times', async () => {
      const testTimes = [0, 1500, 2500, 3500, 5000]; // Various points in timeline
      const results = [];
      
      for (const time of testTimes) {
        const store = window.useStore?.getState();
        const activeEffects = store?.getActiveZoomEffectsAtTime(time) || [];
        
        results.push({
          time,
          activeEffectsCount: activeEffects.length,
          hasZoom: activeEffects.length > 0
        });
      }
      
      return { timeTests: results };
    }, 'playbackRendering');

    // Test canvas transform application
    await this.test('Canvas transform applies correctly', async () => {
      const canvasContainer = document.querySelector('.canvas-container') ||
                             document.querySelector('[class*="canvas"]');
      
      if (!canvasContainer) {
        throw new Error('Canvas container not found');
      }
      
      const transform = window.getComputedStyle(canvasContainer).transform;
      const hasTransform = transform && transform !== 'none';
      
      return { hasTransform, transform };
    }, 'playbackRendering');
  }

  /**
   * Helper method to run individual tests
   */
  async test(name, testFn, category) {
    try {
      console.log(`  ✓ ${name}...`);
      const result = await testFn();
      this.results[category].passed++;
      this.results[category].tests.push({ name, status: 'PASSED', result });
      console.log(`    ✅ PASSED`, result);
    } catch (error) {
      this.results[category].failed++;
      this.results[category].tests.push({ name, status: 'FAILED', error: error.message });
      console.log(`    ❌ FAILED: ${error.message}`);
    }
  }

  /**
   * Print test results summary
   */
  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    Object.entries(this.results).forEach(([category, results]) => {
      console.log(`\n${category.toUpperCase()}:`);
      console.log(`  Passed: ${results.passed}`);
      console.log(`  Failed: ${results.failed}`);
      
      totalPassed += results.passed;
      totalFailed += results.failed;
      
      if (results.failed > 0) {
        console.log('  Failed tests:');
        results.tests.filter(t => t.status === 'FAILED').forEach(test => {
          console.log(`    - ${test.name}: ${test.error}`);
        });
      }
    });
    
    console.log('\n========================');
    console.log(`TOTAL: ${totalPassed} passed, ${totalFailed} failed`);
    
    if (totalFailed === 0) {
      console.log('🎉 All tests passed! Multiple zoom effects implementation is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please review the implementation.');
    }
  }
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Wait for the app to load
  window.addEventListener('load', () => {
    setTimeout(() => {
      const tester = new MultipleZoomEffectsTest();
      tester.runAllTests();
    }, 2000); // Wait 2 seconds for app initialization
  });
} else {
  // Node.js environment - export for manual testing
  module.exports = MultipleZoomEffectsTest;
}
