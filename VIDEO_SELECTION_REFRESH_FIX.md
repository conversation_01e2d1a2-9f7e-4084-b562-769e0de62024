# Video Selection Refresh Issue - Investigation & Solution

## Problem Description

When selecting video elements in the React video editor, users experienced unwanted refresh behavior that disrupted the user experience:

- **Timeline Video Selection**: Clicking on video items in the timeline caused video refresh/flicker
- **Canvas Video Selection**: Clicking on video elements in the canvas/preview area caused video refresh/flicker
- **Expected behavior**: Video selection should only highlight the element without causing any refresh or re-rendering

## Root Cause Analysis

The video selection refresh issue was caused by the same pattern as the previously fixed playhead dragging issue. When video selection occurs:

### Investigation Process

1. **Timeline Selection Flow**:
   - Timeline canvas selection triggers `stateManager.updateState()` with `activeIds` changes
   - This flows through `LAYER_SELECTION` events in `use-timeline-events.ts`
   - State updates trigger multiple subscriptions causing expensive operations

2. **Canvas Selection Flow**:
   - Scene interactions selection triggers `stateManager.updateState()` with `activeIds` changes
   - This happens in both `select` and `selectEnd` events
   - State updates cause cascading re-renders and expensive operations

3. **Identified the actual causes**:
   - ✅ **Global state changes** - `activeIds` updates were triggering expensive operations
   - ✅ **Player component re-renders** - Components using entire store were re-rendering when `activeIds` changed
   - Video thumbnail re-rendering operations
   - Timeline canvas `requestRenderAll()` calls
   - Scene interaction updates

## Solution Implementation

Similar to the playhead dragging fix, we implemented a **video selection state flag** to prevent expensive operations during selection:

### 1. Added Video Selection State

**File**: `src/features/editor/store/use-store.ts`
```typescript
// Video selection state for performance optimization
isVideoSelecting: boolean;
setIsVideoSelecting: (isSelecting: boolean) => void;
```

### 2. Updated Video Timeline Item

**File**: `src/features/editor/timeline/items/video.ts`
- Skip expensive thumbnail generation during video selection
- Skip canvas render requests during video selection
- Skip expensive rendering operations during video selection
- Track video selection state changes

### 3. Updated Timeline Events

**File**: `src/features/editor/hooks/use-timeline-events.ts`
- Set video selection state during `LAYER_SELECTION` events
- Skip player seek operations during video selection

### 4. Updated Scene Interactions

**File**: `src/features/editor/scene/interactions.tsx`
- Set video selection state during `select` and `selectEnd` events
- Skip expensive target updates during video selection

### 5. Updated Performance Hooks

**File**: `src/features/editor/hooks/use-current-frame-optimized.tsx`
- Pause frame updates during video selection
- Return cached frames during video selection

### 6. Fixed Player Component Re-renders

**Files**: `src/features/editor/player/*.tsx`
- Use selective store subscriptions to exclude `activeIds` from dependencies
- Prevent Player, Composition, and CanvasContainer from re-rendering on selection
- Use `shallow` comparison for store selectors

### 7. Updated Other Components

- **Timeline ruler**: Skip seeking during video selection
- **Player events**: Skip seeking during video selection
- **Update ancestors hook**: Skip expensive operations during video selection

## Technical Details

### Why Global State Caused Issues

The `activeIds` state changes triggered multiple problems:

1. **Component Re-renders**: Player components using entire store re-rendered when `activeIds` changed
   - `Composition` component re-rendered → Remotion Player refreshed
   - `CanvasContainer` component re-rendered → Canvas refreshed
   - `PlayerOnly` component re-rendered → Video refreshed

2. **Subscription Cascades**: Multiple subscriptions fired simultaneously
   - **Timeline subscriptions**: Caused canvas re-renders and thumbnail updates
   - **Scene subscriptions**: Caused target updates and moveable re-calculations
   - **Player subscriptions**: Caused frame updates and seeking operations

When this state changed, it caused these components to suddenly resume expensive operations, triggering a cascade of updates that refreshed the video canvas.

### Why the Combined Solution Works

**1. Video Selection State Flag**: Pauses expensive operations during selection
- Expensive operations are paused during selection
- Video canvas remains stable during selection interactions
- Selection still works to update the UI state
- Operations resume after selection completes (50ms delay)

**2. Selective Store Subscriptions**: Prevents unnecessary component re-renders
- Player components only subscribe to needed state properties
- `activeIds` changes don't trigger Player component re-renders
- Remotion Player remains stable during selection
- Uses `shallow` comparison for optimal performance

## Files Modified

- `src/features/editor/store/use-store.ts` - Added video selection state
- `src/features/editor/timeline/items/video.ts` - Skip expensive operations during selection
- `src/features/editor/hooks/use-timeline-events.ts` - Handle timeline selection events
- `src/features/editor/scene/interactions.tsx` - Handle canvas selection events
- `src/features/editor/hooks/use-current-frame-optimized.tsx` - Pause frame updates during selection
- `src/features/editor/timeline/timeline.tsx` - Skip ruler seeking during selection
- `src/features/editor/player/composition.tsx` - Use selective store subscription to prevent re-renders
- `src/features/editor/player/canvas-container.tsx` - Use selective store subscription to prevent re-renders
- `src/features/editor/player/player.tsx` - Use selective store subscription to prevent re-renders
- `src/features/editor/player/sequence-item.tsx` - Use selective store subscription to prevent re-renders
- `src/features/editor/hooks/use-update-ansestors.tsx` - Skip expensive operations during selection

## Results

✅ **Fixed timeline video selection**: No video refresh when selecting videos from timeline  
✅ **Fixed canvas video selection**: No video refresh when selecting videos from canvas/preview  
✅ **Maintained functionality**: Video selection still highlights elements correctly  
✅ **Smooth interactions**: Selection works smoothly without refresh artifacts  

## Testing

To test the fix:

1. **Timeline Selection Test**:
   - Add multiple videos to the timeline
   - Click on different video items in the timeline
   - Verify no video refresh/flicker occurs
   - Verify video items are properly highlighted when selected

2. **Canvas Selection Test**:
   - Add videos to the timeline so they appear in the canvas
   - Click on video elements in the canvas/preview area
   - Verify no video refresh/flicker occurs
   - Verify video elements are properly selected and highlighted

3. **Mixed Selection Test**:
   - Select videos from timeline, then from canvas
   - Verify smooth transitions between selections
   - Verify no refresh artifacts during selection changes

## Memory Note

This fix should be remembered as a successful solution for preventing video refresh during selection operations in React video editors.
