/**
 * Main Thread Monitor - Tracks what's blocking the main thread
 * This helps identify CPU usage that's not captured by our other profilers
 */

interface MainThreadSample {
  timestamp: number;
  taskDuration: number;
  isBlocked: boolean;
  blockingDuration: number;
}

interface TaskMetrics {
  totalTasks: number;
  blockedTasks: number;
  averageTaskDuration: number;
  maxTaskDuration: number;
  totalBlockingTime: number;
  blockingPercentage: number;
}

export class MainThreadMonitor {
  private static instance: MainThreadMonitor;
  private isActive = false;
  private samples: MainThreadSample[] = [];
  private sampleInterval: NodeJS.Timeout | null = null;
  private lastSampleTime = 0;

  static getInstance(): MainThreadMonitor {
    if (!MainThreadMonitor.instance) {
      MainThreadMonitor.instance = new MainThreadMonitor();
    }
    return MainThreadMonitor.instance;
  }

  startMonitoring() {
    if (this.isActive) return;
    
    this.isActive = true;
    this.samples = [];
    this.lastSampleTime = performance.now();
    
    console.log('🧵 Main Thread Monitor started - Tracking thread blocking');
    
    // Sample main thread responsiveness every 50ms
    this.sampleInterval = setInterval(() => {
      this.sampleMainThread();
    }, 50);
  }

  stopMonitoring(): TaskMetrics {
    if (!this.isActive) return this.getEmptyMetrics();
    
    this.isActive = false;
    
    if (this.sampleInterval) {
      clearInterval(this.sampleInterval);
      this.sampleInterval = null;
    }
    
    const metrics = this.generateReport();
    console.log('🧵 Main Thread Monitor stopped');
    return metrics;
  }

  private sampleMainThread() {
    if (!this.isActive) return;
    
    const sampleStart = performance.now();
    
    // Use setTimeout(0) to measure main thread responsiveness
    setTimeout(() => {
      const sampleEnd = performance.now();
      const taskDuration = sampleEnd - sampleStart;
      const expectedDuration = 1; // setTimeout should execute ~immediately
      const blockingDuration = Math.max(0, taskDuration - expectedDuration);
      const isBlocked = blockingDuration > 5; // Consider >5ms as blocking
      
      const sample: MainThreadSample = {
        timestamp: sampleStart,
        taskDuration,
        isBlocked,
        blockingDuration
      };
      
      this.samples.push(sample);
      
      // Log severe blocking immediately
      if (blockingDuration > 50) {
        console.warn(`🧵 SEVERE BLOCKING: Main thread blocked for ${blockingDuration.toFixed(2)}ms`);
      }
      
      // Keep only last 200 samples
      if (this.samples.length > 200) {
        this.samples = this.samples.slice(-200);
      }
      
      this.lastSampleTime = sampleStart;
    }, 0);
  }

  private generateReport(): TaskMetrics {
    if (this.samples.length === 0) {
      return this.getEmptyMetrics();
    }

    const totalTasks = this.samples.length;
    const blockedTasks = this.samples.filter(s => s.isBlocked).length;
    const taskDurations = this.samples.map(s => s.taskDuration);
    const averageTaskDuration = taskDurations.reduce((sum, d) => sum + d, 0) / totalTasks;
    const maxTaskDuration = Math.max(...taskDurations);
    const totalBlockingTime = this.samples.reduce((sum, s) => sum + s.blockingDuration, 0);
    const totalDuration = this.samples[this.samples.length - 1].timestamp - this.samples[0].timestamp;
    const blockingPercentage = (totalBlockingTime / totalDuration) * 100;

    const metrics: TaskMetrics = {
      totalTasks,
      blockedTasks,
      averageTaskDuration,
      maxTaskDuration,
      totalBlockingTime,
      blockingPercentage
    };

    console.log('\n🧵 Main Thread Performance Report:');
    console.log(`📊 Total Samples: ${totalTasks}`);
    console.log(`🚫 Blocked Samples: ${blockedTasks} (${((blockedTasks / totalTasks) * 100).toFixed(1)}%)`);
    console.log(`⏱️  Average Task Duration: ${averageTaskDuration.toFixed(2)}ms`);
    console.log(`🔥 Max Task Duration: ${maxTaskDuration.toFixed(2)}ms`);
    console.log(`⏳ Total Blocking Time: ${totalBlockingTime.toFixed(2)}ms`);
    console.log(`📈 Blocking Percentage: ${blockingPercentage.toFixed(1)}%`);

    this.analyzeBlocking(metrics);

    // Export data
    (window as any).mainThreadData = {
      samples: this.samples,
      metrics
    };

    console.log('\n📊 Main thread data exported to window.mainThreadData');
    
    return metrics;
  }

  private analyzeBlocking(metrics: TaskMetrics) {
    console.log('\n🔍 Main Thread Analysis:');

    if (metrics.blockingPercentage > 30) {
      console.log('🚨 CRITICAL: Main thread blocked >30% of the time');
      console.log('   This will cause severe performance issues');
    } else if (metrics.blockingPercentage > 15) {
      console.log('⚠️ WARNING: Main thread blocked >15% of the time');
      console.log('   This may cause noticeable performance issues');
    } else {
      console.log('✅ Main thread blocking within acceptable range');
    }

    if (metrics.maxTaskDuration > 100) {
      console.log('🚨 CRITICAL: Tasks taking >100ms detected');
      console.log('   This will cause UI freezing');
    }

    // Find patterns in blocking
    const recentSamples = this.samples.slice(-20);
    const recentBlocking = recentSamples.filter(s => s.isBlocked).length;
    const recentBlockingPercentage = (recentBlocking / recentSamples.length) * 100;

    console.log(`📈 Recent Blocking: ${recentBlockingPercentage.toFixed(1)}% (last 20 samples)`);

    if (recentBlockingPercentage > metrics.blockingPercentage * 1.5) {
      console.log('⚠️ Blocking is getting worse over time');
    }

    // Identify blocking clusters
    const blockingClusters = this.findBlockingClusters();
    if (blockingClusters.length > 0) {
      console.log(`🔗 Blocking Clusters: ${blockingClusters.length} detected`);
      blockingClusters.slice(0, 3).forEach((cluster, index) => {
        console.log(`Cluster ${index + 1}: ${cluster.duration.toFixed(2)}ms blocking over ${cluster.samples} samples`);
      });
    }

    console.log('\n💡 Main Thread Recommendations:');

    if (metrics.blockingPercentage > 20) {
      console.log('   • Break up long-running tasks with setTimeout/requestIdleCallback');
      console.log('   • Move heavy computations to Web Workers');
      console.log('   • Use React.startTransition for non-urgent updates');
    }

    if (metrics.maxTaskDuration > 50) {
      console.log('   • Profile specific long-running tasks');
      console.log('   • Consider code splitting or lazy loading');
    }

    if (blockingClusters.length > 2) {
      console.log('   • Investigate repeated blocking patterns');
      console.log('   • Check for synchronous operations in render loops');
    }
  }

  private findBlockingClusters(): Array<{duration: number, samples: number}> {
    const clusters: Array<{duration: number, samples: number}> = [];
    let currentCluster: MainThreadSample[] = [];

    for (const sample of this.samples) {
      if (sample.isBlocked) {
        currentCluster.push(sample);
      } else {
        if (currentCluster.length > 2) { // Cluster of 3+ blocked samples
          const clusterDuration = currentCluster.reduce((sum, s) => sum + s.blockingDuration, 0);
          clusters.push({
            duration: clusterDuration,
            samples: currentCluster.length
          });
        }
        currentCluster = [];
      }
    }

    // Handle cluster at end
    if (currentCluster.length > 2) {
      const clusterDuration = currentCluster.reduce((sum, s) => sum + s.blockingDuration, 0);
      clusters.push({
        duration: clusterDuration,
        samples: currentCluster.length
      });
    }

    return clusters.sort((a, b) => b.duration - a.duration);
  }

  private getEmptyMetrics(): TaskMetrics {
    return {
      totalTasks: 0,
      blockedTasks: 0,
      averageTaskDuration: 0,
      maxTaskDuration: 0,
      totalBlockingTime: 0,
      blockingPercentage: 0
    };
  }

  getStatus() {
    const recentSamples = this.samples.slice(-10);
    const recentBlocking = recentSamples.filter(s => s.isBlocked).length;
    const recentBlockingPercentage = recentSamples.length > 0 ? 
      (recentBlocking / recentSamples.length) * 100 : 0;

    return {
      isActive: this.isActive,
      totalSamples: this.samples.length,
      recentBlockingPercentage,
      recentSamples: recentSamples.slice(-5)
    };
  }
}

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).mainThreadMonitor = MainThreadMonitor.getInstance();
}

export default MainThreadMonitor;
