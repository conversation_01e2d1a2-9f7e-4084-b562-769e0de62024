<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Zoom Effects Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            display: flex;
            height: 80vh;
        }
        .test-panel {
            width: 300px;
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 20px;
            overflow-y: auto;
        }
        .app-frame {
            flex: 1;
            border: none;
        }
        .test-controls {
            margin-bottom: 20px;
        }
        .test-button {
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button:disabled {
            background: #94a3b8;
            cursor: not-allowed;
        }
        .test-results {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: 500;
        }
        .status.loading {
            background: #dbeafe;
            color: #1e40af;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
        }
        .status.error {
            background: #fee2e2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Multiple Zoom Effects Test Suite</h1>
            <p>Comprehensive testing for the enhanced zoom functionality</p>
        </div>
        
        <div class="content">
            <div class="test-panel">
                <div class="instructions">
                    <strong>Instructions:</strong><br>
                    1. Make sure the video editor is loaded<br>
                    2. Click "Run All Tests" to start<br>
                    3. Check the console for detailed results<br>
                    4. Test results will appear below
                </div>
                
                <div id="status" class="status loading">
                    Loading video editor...
                </div>
                
                <div class="test-controls">
                    <button id="runAllTests" class="test-button" disabled>
                        Run All Tests
                    </button>
                    <button id="runStoreTests" class="test-button" disabled>
                        Test Store Operations
                    </button>
                    <button id="runUITests" class="test-button" disabled>
                        Test UI Interaction
                    </button>
                    <button id="runConflictTests" class="test-button" disabled>
                        Test Conflict Resolution
                    </button>
                    <button id="runPlaybackTests" class="test-button" disabled>
                        Test Playback & Rendering
                    </button>
                    <button id="clearResults" class="test-button">
                        Clear Results
                    </button>
                </div>
                
                <div class="test-results" id="testResults">
                    Waiting for tests to run...
                </div>
            </div>
            
            <iframe 
                id="appFrame" 
                class="app-frame" 
                src="http://localhost:5173"
                title="Video Editor">
            </iframe>
        </div>
    </div>

    <script>
        let testInstance = null;
        let appLoaded = false;

        // Status management
        function updateStatus(message, type = 'loading') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // Enable/disable test buttons
        function toggleButtons(enabled) {
            const buttons = document.querySelectorAll('.test-button:not(#clearResults)');
            buttons.forEach(btn => btn.disabled = !enabled);
        }

        // Log to test results panel
        function logToResults(message) {
            const results = document.getElementById('testResults');
            results.textContent += message + '\n';
            results.scrollTop = results.scrollHeight;
        }

        // Override console.log to capture test output
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToResults(args.join(' '));
        };

        // Wait for iframe to load
        document.getElementById('appFrame').addEventListener('load', function() {
            setTimeout(() => {
                const iframe = document.getElementById('appFrame');
                try {
                    // Check if we can access the iframe content
                    const iframeWindow = iframe.contentWindow;
                    if (iframeWindow && iframeWindow.useStore) {
                        appLoaded = true;
                        updateStatus('Video editor loaded successfully', 'success');
                        toggleButtons(true);
                        
                        // Load test script into iframe
                        const script = iframe.contentDocument.createElement('script');
                        script.src = '/test-multiple-zoom-effects.js';
                        iframe.contentDocument.head.appendChild(script);
                        
                        // Create test instance
                        script.onload = () => {
                            if (iframeWindow.MultipleZoomEffectsTest) {
                                testInstance = new iframeWindow.MultipleZoomEffectsTest();
                                logToResults('Test suite loaded and ready');
                            }
                        };
                    } else {
                        updateStatus('Waiting for video editor to initialize...', 'loading');
                        setTimeout(arguments.callee, 1000);
                    }
                } catch (error) {
                    updateStatus('Error accessing video editor: ' + error.message, 'error');
                }
            }, 2000);
        });

        // Test button handlers
        document.getElementById('runAllTests').addEventListener('click', async () => {
            if (!testInstance) {
                logToResults('❌ Test instance not available');
                return;
            }
            
            logToResults('🧪 Starting comprehensive test suite...\n');
            updateStatus('Running all tests...', 'loading');
            
            try {
                await testInstance.runAllTests();
                updateStatus('All tests completed', 'success');
            } catch (error) {
                logToResults('❌ Test suite failed: ' + error.message);
                updateStatus('Tests failed', 'error');
            }
        });

        document.getElementById('runStoreTests').addEventListener('click', async () => {
            if (!testInstance) return;
            logToResults('📦 Running store operation tests...\n');
            await testInstance.testStoreOperations();
        });

        document.getElementById('runUITests').addEventListener('click', async () => {
            if (!testInstance) return;
            logToResults('🖱️ Running UI interaction tests...\n');
            await testInstance.testUIInteraction();
        });

        document.getElementById('runConflictTests').addEventListener('click', async () => {
            if (!testInstance) return;
            logToResults('⚠️ Running conflict resolution tests...\n');
            await testInstance.testConflictResolution();
        });

        document.getElementById('runPlaybackTests').addEventListener('click', async () => {
            if (!testInstance) return;
            logToResults('🎬 Running playback and rendering tests...\n');
            await testInstance.testPlaybackRendering();
        });

        document.getElementById('clearResults').addEventListener('click', () => {
            document.getElementById('testResults').textContent = 'Results cleared.\n';
        });

        // Initial status
        updateStatus('Loading video editor...', 'loading');
    </script>
</body>
</html>
