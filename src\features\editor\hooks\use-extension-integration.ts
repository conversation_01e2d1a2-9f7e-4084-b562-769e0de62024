import { useState, useEffect, useCallback } from 'react';
import { extensionApi, ExtensionSessionData, ExtensionCursorData } from '../../../services/extension-api';
import { useLocalVideosStore } from '../store/use-local-videos-store';
import { dispatch } from '@designcombo/events';
import { ADD_VIDEO } from '@designcombo/state';
import { generateId } from '@designcombo/timeline';
import { IVideo } from '@designcombo/types';

export interface ExtensionIntegrationState {
  isLoading: boolean;
  sessionId: string | null;
  sessionData: ExtensionSessionData | null;
  error: string | null;
  hasVideo: boolean;
  hasCursorData: boolean;
  isServerAvailable: boolean;
}

export interface UseExtensionIntegrationReturn {
  state: ExtensionIntegrationState;
  actions: {
    loadSession: (sessionId: string) => Promise<boolean>;
    clearSession: () => void;
    addVideoToTimeline: () => Promise<boolean>;
    getCursorData: () => ExtensionCursorData[] | null;
    checkServerStatus: () => Promise<boolean>;
  };
}

export function useExtensionIntegration(): UseExtensionIntegrationReturn {
  const [state, setState] = useState<ExtensionIntegrationState>({
    isLoading: false,
    sessionId: null,
    sessionData: null,
    error: null,
    hasVideo: false,
    hasCursorData: false,
    isServerAvailable: false,
  });

  const { actions: videoActions } = useLocalVideosStore();

  // Check server status
  const checkServerStatus = useCallback(async (): Promise<boolean> => {
    try {
      const isAvailable = await extensionApi.isServerAvailable();
      setState(prev => ({ ...prev, isServerAvailable: isAvailable }));
      return isAvailable;
    } catch (error) {
      console.error('Error checking server status:', error);
      setState(prev => ({ ...prev, isServerAvailable: false }));
      return false;
    }
  }, []);

  // Load session data
  const loadSession = useCallback(async (sessionId: string): Promise<boolean> => {
    console.log('🔄 [DEBUG] Loading extension session:', sessionId);
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('🔄 [DEBUG] Fetching session data from server...');
      const sessionResponse = await extensionApi.getSessionData(sessionId);

      if (!sessionResponse) {
        console.warn('⚠️ [DEBUG] Session not found or server unavailable');
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Session not found or server unavailable',
        }));
        return false;
      }

      const { data } = sessionResponse;
      console.log('✅ [DEBUG] Session data loaded:', {
        hasVideo: !!data.videoFile,
        hasCursorData: !!data.cursorData && data.cursorData.length > 0,
        cursorDataLength: data.cursorData?.length || 0,
        videoFileName: data.videoFile?.filename
      });

      setState(prev => ({
        ...prev,
        isLoading: false,
        sessionId,
        sessionData: data,
        hasVideo: !!data.videoFile,
        hasCursorData: !!data.cursorData && data.cursorData.length > 0,
        error: null,
      }));

      // Update URL to include session ID
      extensionApi.constructor.updateUrlWithSession(sessionId);

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ [DEBUG] Error loading session:', errorMessage);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return false;
    }
  }, []);

  // Clear session
  const clearSession = useCallback(() => {
    setState({
      isLoading: false,
      sessionId: null,
      sessionData: null,
      error: null,
      hasVideo: false,
      hasCursorData: false,
      isServerAvailable: false,
    });

    // Remove session from URL
    extensionApi.constructor.clearSessionFromUrl();
  }, []);

  // Add video to timeline
  const addVideoToTimeline = useCallback(async (): Promise<boolean> => {
    if (!state.sessionData?.videoFile) {
      console.warn('⚠️ [DEBUG] No video file in session data');
      return false;
    }

    try {
      const { videoFile } = state.sessionData;
      console.log('🎬 [DEBUG] Adding extension video to timeline:', videoFile.filename);

      // Fetch the video file
      console.log('🎬 [DEBUG] Fetching video from:', videoFile.url);
      const response = await fetch(videoFile.url);
      if (!response.ok) {
        throw new Error(`Failed to fetch video file: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      const file = new File([blob], videoFile.originalName, { type: 'video/webm' });

      // Add to local videos store
      const localVideo = await videoActions.addVideo(file);

      // Add to timeline
      const videoData: Partial<IVideo> = {
        id: generateId(),
        details: {
          src: localVideo.objectUrl,
          width: localVideo.width,
          height: localVideo.height,
          blur: 0,
          brightness: 100,
          flipX: false,
          flipY: false,
          rotate: "0",
          visibility: "visible",
        },
        type: "video",
        metadata: {
          previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
          localVideoId: localVideo.id,
          fileName: localVideo.name,
        },
        duration: localVideo.duration,
      };

      dispatch(ADD_VIDEO, {
        payload: videoData,
        options: {
          resourceId: "main",
          scaleMode: "fit",
        },
      });

      return true;
    } catch (error) {
      console.error('Error adding extension video to timeline:', error);
      setState(prev => ({
        ...prev,
        error: `Failed to add video: ${error instanceof Error ? error.message : 'Unknown error'}`,
      }));
      return false;
    }
  }, [state.sessionData, videoActions]);

  // Get cursor data
  const getCursorData = useCallback((): ExtensionCursorData[] | null => {
    return state.sessionData?.cursorData || null;
  }, [state.sessionData]);

  // Auto-load session from URL on mount
  useEffect(() => {
    const sessionId = extensionApi.constructor.getSessionIdFromUrl();
    if (sessionId) {
      loadSession(sessionId);
    }

    // Check server status on mount
    checkServerStatus();
  }, [loadSession, checkServerStatus]);

  // Auto-add video to timeline when session data is loaded
  useEffect(() => {
    if (state.sessionData?.videoFile && state.hasVideo && !state.isLoading) {
      // Small delay to ensure the video editor is fully initialized
      const timer = setTimeout(() => {
        console.log('🎬 Automatically adding extension video to timeline...');
        addVideoToTimeline().then((success) => {
          if (success) {
            console.log('✅ Extension video automatically added to timeline');

            // Show a temporary success notification
            const notification = document.createElement('div');
            notification.innerHTML = `
              <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-size: 14px;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 8px;
                animation: slideIn 0.3s ease-out;
              ">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M20 6L9 17l-5-5"/>
                </svg>
                Extension video added to timeline
              </div>
              <style>
                @keyframes slideIn {
                  from { transform: translateX(100%); opacity: 0; }
                  to { transform: translateX(0); opacity: 1; }
                }
              </style>
            `;

            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
              if (notification.parentNode) {
                notification.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => {
                  document.body.removeChild(notification);
                }, 300);
              }
            }, 3000);

          } else {
            console.warn('⚠️ Failed to automatically add extension video to timeline');
          }
        });
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [state.sessionData, state.hasVideo, state.isLoading, addVideoToTimeline]);

  return {
    state,
    actions: {
      loadSession,
      clearSession,
      addVideoToTimeline,
      getCursorData,
      checkServerStatus,
    },
  };
}
