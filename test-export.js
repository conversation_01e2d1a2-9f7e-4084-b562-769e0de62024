#!/usr/bin/env node

/**
 * Test script to validate the video export functionality
 * Run this after starting the render server to test the API endpoints
 */

import fetch from 'node-fetch';

const RENDER_SERVER_URL = 'http://localhost:3001';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testHealthCheck() {
  try {
    logInfo('Testing health check endpoint...');
    const response = await fetch(`${RENDER_SERVER_URL}/health`);
    
    if (response.ok) {
      const data = await response.json();
      logSuccess(`Health check passed: ${data.status} at ${data.timestamp}`);
      return true;
    } else {
      logError(`Health check failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logError(`Health check failed: ${error.message}`);
    return false;
  }
}

async function testCompositions() {
  try {
    logInfo('Testing compositions endpoint...');
    const response = await fetch(`${RENDER_SERVER_URL}/compositions`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.compositions.length > 0) {
        logSuccess(`Found ${data.compositions.length} composition(s):`);
        data.compositions.forEach(comp => {
          log(`  - ${comp.id}: ${comp.width}x${comp.height} @ ${comp.fps}fps`);
        });
        return true;
      } else {
        logWarning('No compositions found - this might be expected for a fresh setup');
        return true;
      }
    } else {
      logError(`Compositions test failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logError(`Compositions test failed: ${error.message}`);
    return false;
  }
}

async function testRenderEndpoint() {
  try {
    logInfo('Testing render endpoint with minimal data...');
    
    const renderRequest = {
      compositionId: 'Composition',
      inputProps: {
        trackItemIds: [],
        trackItemsMap: {},
        trackItemDetailsMap: {},
        transitionsMap: {},
        canvasSettings: {
          background: {
            type: 'solid',
            solidColor: '#000000',
            gradient: { type: 'linear', angle: 0, stops: [] },
            imageUrl: null,
            imageFile: null,
            imageObjectUrl: null,
          },
          padding: { value: 70, unit: 'px' },
          blur: { enabled: false, intensity: 0 },
          videoBorderRadius: { value: 0 },
          videoBackgroundShadow: {
            enabled: false,
            x: 0, y: 0, blur: 0, spread: 0,
            color: 'rgba(0, 0, 0, 0)',
          },
        },
        duration: 3000, // 3 seconds
        fps: 30,
        width: 1080,
        height: 1920,
      },
      codec: 'h264',
      imageFormat: 'jpeg',
      quality: 80,
    };

    const response = await fetch(`${RENDER_SERVER_URL}/render`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(renderRequest),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.renderId) {
        logSuccess(`Render started successfully: ${data.renderId}`);
        logInfo('Note: This is just testing the endpoint - the render may fail due to empty timeline');
        return true;
      } else {
        logError(`Render test failed: ${data.error || 'Unknown error'}`);
        return false;
      }
    } else {
      logError(`Render test failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logError(`Render test failed: ${error.message}`);
    return false;
  }
}

async function checkDependencies() {
  logInfo('Checking if render server dependencies are installed...');
  
  try {
    const fs = await import('fs');
    const path = await import('path');
    
    const serverPackageJsonPath = path.join(process.cwd(), 'remotion-render-server', 'package.json');
    const nodeModulesPath = path.join(process.cwd(), 'remotion-render-server', 'node_modules');
    
    if (!fs.existsSync(serverPackageJsonPath)) {
      logWarning('Render server package.json not found');
      return false;
    }
    
    if (!fs.existsSync(nodeModulesPath)) {
      logWarning('Render server dependencies not installed. Run: npm run render-server:install');
      return false;
    }
    
    logSuccess('Render server dependencies appear to be installed');
    return true;
  } catch (error) {
    logWarning(`Could not check dependencies: ${error.message}`);
    return true; // Don't fail the test for this
  }
}

async function runTests() {
  log(`${colors.bold}🧪 Video Export Test Suite${colors.reset}\n`);
  
  const tests = [
    { name: 'Dependencies Check', fn: checkDependencies },
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Compositions', fn: testCompositions },
    { name: 'Render Endpoint', fn: testRenderEndpoint },
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      logError(`Test "${test.name}" threw an error: ${error.message}`);
      failed++;
    }
    console.log(); // Empty line for readability
  }

  // Summary
  log(`${colors.bold}📊 Test Results:${colors.reset}`);
  logSuccess(`Passed: ${passed}`);
  if (failed > 0) {
    logError(`Failed: ${failed}`);
  }

  if (failed === 0) {
    logSuccess('🎉 All tests passed! Your export setup is working correctly.');
    logInfo('You can now use the export functionality in the React Video Editor.');
  } else {
    logWarning('⚠️  Some tests failed. Please check the setup:');
    log('1. Make sure the render server is running: npm run render-server:dev');
    log('2. Verify dependencies are installed: npm run render-server:install');
    log('3. Check that port 3001 is available');
    log('4. Review the error messages above for specific issues');
  }

  process.exit(failed > 0 ? 1 : 0);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run the tests
runTests().catch(error => {
  logError(`Test suite failed: ${error.message}`);
  process.exit(1);
});
