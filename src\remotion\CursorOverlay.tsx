import React from 'react';
import { AbsoluteFill, useCurrentFrame, interpolate, Easing } from 'remotion';

export interface CursorPoint {
  id: string;
  frame: number;
  time: number;
  x: number;
  y: number;
  action: 'move' | 'click';
  originalTimestamp: number;
}

export interface CursorOverlayData {
  frameData: Record<number, CursorPoint[]>;
  totalFrames: number;
  canvasWidth: number;
  canvasHeight: number;
  metadata: {
    totalPoints: number;
    clicks: number;
    duration: number;
  };
}

interface CursorOverlayProps {
  cursorData: CursorOverlayData;
  showTrail?: boolean;
  trailLength?: number;
  cursorSize?: number;
  cursorColor?: string;
  clickColor?: string;
  trailOpacity?: number;
}

export const CursorOverlay: React.FC<CursorOverlayProps> = ({
  cursorData,
  showTrail = true,
  trailLength = 30,
  cursorSize = 20,
  cursorColor = '#3b82f6',
  clickColor = '#ef4444',
  trailOpacity = 0.6,
}) => {
  const frame = useCurrentFrame();

  if (!cursorData || !cursorData.frameData) {
    return null;
  }

  // Get current cursor position
  const getCurrentCursorPosition = () => {
    // Find the most recent cursor position at or before current frame
    let currentPosition: CursorPoint | null = null;
    
    for (let f = frame; f >= 0; f--) {
      const framePoints = cursorData.frameData[f];
      if (framePoints && framePoints.length > 0) {
        currentPosition = framePoints[framePoints.length - 1];
        break;
      }
    }

    return currentPosition;
  };

  // Get trail points for smooth cursor movement
  const getTrailPoints = () => {
    if (!showTrail) return [];

    const trailPoints: CursorPoint[] = [];
    const startFrame = Math.max(0, frame - trailLength);

    for (let f = startFrame; f <= frame; f++) {
      const framePoints = cursorData.frameData[f];
      if (framePoints) {
        trailPoints.push(...framePoints);
      }
    }

    return trailPoints.slice(-trailLength);
  };

  // Get recent clicks for click animation
  const getRecentClicks = () => {
    const clickAnimationDuration = 15; // frames
    const recentClicks: CursorPoint[] = [];
    const startFrame = Math.max(0, frame - clickAnimationDuration);

    for (let f = startFrame; f <= frame; f++) {
      const framePoints = cursorData.frameData[f];
      if (framePoints) {
        const clicks = framePoints.filter(p => p.action === 'click');
        recentClicks.push(...clicks);
      }
    }

    return recentClicks;
  };

  const currentPosition = getCurrentCursorPosition();
  const trailPoints = getTrailPoints();
  const recentClicks = getRecentClicks();

  if (!currentPosition) {
    return null;
  }

  return (
    <AbsoluteFill>
      {/* Cursor Trail */}
      {showTrail && trailPoints.length > 1 && (
        <svg
          width={cursorData.canvasWidth}
          height={cursorData.canvasHeight}
          style={{ position: 'absolute', top: 0, left: 0 }}
        >
          <path
            d={`M ${trailPoints.map(p => `${p.x},${p.y}`).join(' L ')}`}
            stroke={cursorColor}
            strokeWidth="2"
            fill="none"
            opacity={trailOpacity}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}

      {/* Trail Points */}
      {showTrail && trailPoints.map((point, index) => {
        const opacity = interpolate(
          index,
          [0, trailPoints.length - 1],
          [0.1, trailOpacity],
          { easing: Easing.out(Easing.quad) }
        );

        const size = interpolate(
          index,
          [0, trailPoints.length - 1],
          [2, 6],
          { easing: Easing.out(Easing.quad) }
        );

        return (
          <div
            key={`trail-${point.id}-${index}`}
            style={{
              position: 'absolute',
              left: point.x - size / 2,
              top: point.y - size / 2,
              width: size,
              height: size,
              backgroundColor: cursorColor,
              borderRadius: '50%',
              opacity,
              pointerEvents: 'none',
            }}
          />
        );
      })}

      {/* Current Cursor */}
      <div
        style={{
          position: 'absolute',
          left: currentPosition.x - cursorSize / 2,
          top: currentPosition.y - cursorSize / 2,
          width: cursorSize,
          height: cursorSize,
          pointerEvents: 'none',
          zIndex: 10,
        }}
      >
        {/* Cursor Icon */}
        <svg
          width={cursorSize}
          height={cursorSize}
          viewBox="0 0 24 24"
          fill="none"
          style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))' }}
        >
          <path
            d="M8 2L8 14L11.5 10.5L14 16L16 15L13.5 9.5L18 9.5L8 2Z"
            fill={cursorColor}
            stroke="white"
            strokeWidth="1"
          />
        </svg>
      </div>

      {/* Click Animations */}
      {recentClicks.map((click) => {
        const clickAge = frame - click.frame;
        const animationProgress = clickAge / 15; // 15 frames animation

        if (animationProgress > 1) return null;

        const scale = interpolate(
          animationProgress,
          [0, 0.3, 1],
          [0, 1.5, 0],
          { easing: Easing.out(Easing.quad) }
        );

        const opacity = interpolate(
          animationProgress,
          [0, 0.3, 1],
          [1, 0.8, 0],
          { easing: Easing.out(Easing.quad) }
        );

        return (
          <div
            key={`click-${click.id}`}
            style={{
              position: 'absolute',
              left: click.x - 15,
              top: click.y - 15,
              width: 30,
              height: 30,
              borderRadius: '50%',
              border: `3px solid ${clickColor}`,
              backgroundColor: `${clickColor}20`,
              transform: `scale(${scale})`,
              opacity,
              pointerEvents: 'none',
              zIndex: 5,
            }}
          />
        );
      })}

      {/* Debug Info (optional) */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'absolute',
            top: 10,
            right: 10,
            backgroundColor: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: 'monospace',
            zIndex: 20,
          }}
        >
          <div>Frame: {frame}</div>
          <div>Cursor: {currentPosition.x}, {currentPosition.y}</div>
          <div>Trail Points: {trailPoints.length}</div>
          <div>Recent Clicks: {recentClicks.length}</div>
        </div>
      )}
    </AbsoluteFill>
  );
};
