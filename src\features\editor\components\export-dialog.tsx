import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Download, CheckCircle, Loader2, X } from 'lucide-react';
import { useVideoExport, ExportSettings } from '../hooks/use-video-export';
import useStore from '../store/use-store';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ExportDialog({ open, onOpenChange }: ExportDialogProps) {
  const {
    isExporting,
    exportProgress,
    serverStatus,
    exportSettings,
    updateExportSettings,
    startExport,
    cancelExport,
    downloadVideo,
    checkServerStatus,
  } = useVideoExport();

  const { size } = useStore();
  const [localSettings, setLocalSettings] = useState<ExportSettings>(exportSettings);

  // Get canvas dimensions from main store
  const canvasWidth = size.width;
  const canvasHeight = size.height;

  const handleSettingChange = (key: keyof ExportSettings, value: any) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleStartExport = async () => {
    try {
      updateExportSettings(localSettings);
      await startExport(localSettings);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleDownload = async () => {
    if (exportProgress?.renderId) {
      try {
        await downloadVideo(exportProgress.renderId, `video-export-${Date.now()}.mp4`);
      } catch (error) {
        console.error('Download failed:', error);
      }
    }
  };

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      case 'rendering': return 'bg-blue-500';
      case 'preparing': return 'bg-purple-500';
      case 'bundling': return 'bg-orange-500';
      default: return 'bg-yellow-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'preparing': return 'Preparing files...';
      case 'bundling': return 'Bundling project...';
      case 'rendering': return 'Rendering video...';
      case 'completed': return 'Completed';
      case 'error': return 'Error';
      default: return status;
    }
  };

  const renderServerStatus = () => {
    switch (serverStatus) {
      case 'checking':
        return (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Checking server status...
          </div>
        );
      case 'offline':
        return (
          <div className="flex items-center gap-2 text-sm text-red-600">
            <AlertCircle className="h-4 w-4" />
            Render server offline
            <Button variant="outline" size="sm" onClick={checkServerStatus}>
              Retry
            </Button>
          </div>
        );
      case 'online':
        return (
          <div className="flex items-center gap-2 text-sm text-green-600">
            <CheckCircle className="h-4 w-4" />
            Render server online
          </div>
        );
    }
  };

  const renderExportProgress = () => {
    if (!exportProgress) return null;

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(exportProgress.status)}>
              {getStatusText(exportProgress.status)}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {exportProgress.progress}%
            </span>
          </div>
          {exportProgress.status === 'completed' && (
            <Button size="sm" onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          )}
        </div>

        <Progress value={exportProgress.progress} className="w-full" />

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Time elapsed:</span>
            <div>{formatTime(exportProgress.timeElapsed)}</div>
          </div>
          {exportProgress.estimatedTimeRemaining && (
            <div>
              <span className="text-muted-foreground">Estimated remaining:</span>
              <div>{formatTime(exportProgress.estimatedTimeRemaining)}</div>
            </div>
          )}
        </div>

        {exportProgress.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Export Error</span>
            </div>
            <p className="text-sm text-red-700 mt-1">{exportProgress.error}</p>
          </div>
        )}
      </div>
    );
  };

  const canStartExport = serverStatus === 'online' && !isExporting;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Export Video</DialogTitle>
          <DialogDescription>
            Configure your export settings and render your video project.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Server Status */}
          <div className="p-3 bg-muted rounded-md">
            {renderServerStatus()}
          </div>

          {/* Export Progress */}
          {(isExporting || exportProgress) && renderExportProgress()}

          {/* Export Settings */}
          {!isExporting && (
            <div className="space-y-4">
              {/* Canvas Resolution Display */}
              <div className="p-3 bg-muted rounded-md">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Canvas Resolution</span>
                  <span className="text-sm text-muted-foreground">
                    {canvasWidth} × {canvasHeight}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Video will be exported at your canvas dimensions
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quality">Quality</Label>
                  <Select
                    value={localSettings.quality}
                    onValueChange={(value) => handleSettingChange('quality', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low (Fast)</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="ultra">Ultra (Slow)</SelectItem>
                      <SelectItem value="maximum">Maximum (Slowest, Best Quality)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fps">Frame Rate</Label>
                  <Select
                    value={localSettings.fps.toString()}
                    onValueChange={(value) => handleSettingChange('fps', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="24">24 FPS</SelectItem>
                      <SelectItem value="30">30 FPS</SelectItem>
                      <SelectItem value="60">60 FPS</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="codec">Codec</Label>
                  <Select
                    value={localSettings.codec}
                    onValueChange={(value) => handleSettingChange('codec', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="h264">H.264 (Recommended)</SelectItem>
                      <SelectItem value="h265">H.265 (HEVC)</SelectItem>
                      <SelectItem value="vp8">VP8</SelectItem>
                      <SelectItem value="vp9">VP9</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          {isExporting ? (
            <Button variant="outline" onClick={cancelExport}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
              <Button 
                onClick={handleStartExport} 
                disabled={!canStartExport}
              >
                {serverStatus === 'checking' && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Start Export
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
