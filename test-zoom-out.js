// Simple test to verify zoom-out functionality
// This can be run in a browser console or Node.js environment

// Mock the zoom configuration with zoom-out enabled
const mockZoomConfig = {
  maxZoomScale: 1.5,
  bezierControlPoints: {
    p1: 0.25,
    p2: 1.5,
    p3: 0.75,
    p4: 1.5,
  },
  defaultTiming: {
    startTime: 0, // No default start time
    endTime: 0,   // No default end time
  },
  zoomOut: {
    duration: 1000,    // 1 second zoom-out
    enabled: true,
    easing: 'ease-out'
  },
};

const mockZoomTiming = {
  startTime: 1000,
  endTime: 4000,
};

// Simplified easing functions
function easeOut(t) {
  return 1 - Math.pow(1 - t, 3);
}

function linear(t) {
  return t;
}

function cubicBezier(t, p1, p2, p3, p4) {
  const u = 1 - t;
  const tt = t * t;
  const uu = u * u;
  const uuu = uu * u;
  const ttt = tt * t;
  return 3 * uu * t * p2 + 3 * u * tt * p3 + ttt;
}

// Simplified zoom calculation function
function calculateZoomScale(currentTime, zoomTiming, zoomConfig) {
  const zoomStartTime = zoomTiming.startTime;
  const zoomEndTime = zoomTiming.endTime;
  const zoomDuration = zoomEndTime - zoomStartTime;
  
  const zoomOutDuration = zoomConfig.zoomOut.enabled ? zoomConfig.zoomOut.duration : 0;
  const zoomOutEndTime = zoomEndTime + zoomOutDuration;

  let zoomScale = 1;
  let isZoomActive = false;
  let progress = 0;
  let phase = 'inactive';

  // Zoom-in phase
  if (zoomDuration > 0 && currentTime >= zoomStartTime && currentTime <= zoomEndTime) {
    isZoomActive = true;
    phase = 'zoom-in';
    
    progress = (currentTime - zoomStartTime) / zoomDuration;
    const { p1, p2, p3, p4 } = zoomConfig.bezierControlPoints;
    const bezierProgress = cubicBezier(progress, p1, p2, p3, p4);
    
    zoomScale = 1 + bezierProgress * zoomConfig.maxZoomScale;
  }
  // Zoom-out phase
  else if (zoomConfig.zoomOut.enabled && zoomOutDuration > 0 && 
           currentTime > zoomEndTime && currentTime <= zoomOutEndTime) {
    isZoomActive = true;
    phase = 'zoom-out';
    
    progress = (currentTime - zoomEndTime) / zoomOutDuration;
    
    let easedProgress;
    switch (zoomConfig.zoomOut.easing) {
      case 'linear':
        easedProgress = linear(progress);
        break;
      case 'ease-out':
        easedProgress = easeOut(progress);
        break;
      case 'bezier':
        const { p1, p2, p3, p4 } = zoomConfig.bezierControlPoints;
        easedProgress = cubicBezier(progress, p1, p2, p3, p4);
        break;
      default:
        easedProgress = easeOut(progress);
    }
    
    const maxZoomScale = 1 + zoomConfig.maxZoomScale;
    zoomScale = maxZoomScale - (easedProgress * zoomConfig.maxZoomScale);
  }

  return {
    zoomScale,
    isZoomActive,
    progress,
    phase,
    currentTime
  };
}

// Test the zoom functionality
console.log('🔍 Testing Zoom-Out Functionality');
console.log('=====================================');

const testTimes = [
  0,     // Before zoom
  1000,  // Zoom start
  2500,  // Middle of zoom-in
  4000,  // End of zoom-in / Start of zoom-out
  4500,  // Middle of zoom-out
  5000,  // End of zoom-out
  6000,  // After zoom
];

testTimes.forEach(time => {
  const result = calculateZoomScale(time, mockZoomTiming, mockZoomConfig);
  console.log(`Time: ${time}ms | Phase: ${result.phase.padEnd(8)} | Scale: ${result.zoomScale.toFixed(3)} | Progress: ${result.progress.toFixed(3)}`);
});

console.log('\n✅ Expected behavior:');
console.log('- Before 1000ms: scale = 1.000 (inactive)');
console.log('- 1000-4000ms: scale increases then decreases (zoom-in)');
console.log('- 4000-5000ms: scale smoothly decreases to 1.000 (zoom-out)');
console.log('- After 5000ms: scale = 1.000 (inactive)');

// Test different easing functions
console.log('\n🎨 Testing Different Easing Functions:');
console.log('=====================================');

const easingTypes = ['linear', 'ease-out', 'bezier'];
const testTime = 4500; // Middle of zoom-out

easingTypes.forEach(easing => {
  const configWithEasing = {
    ...mockZoomConfig,
    zoomOut: { ...mockZoomConfig.zoomOut, easing }
  };
  
  const result = calculateZoomScale(testTime, mockZoomTiming, configWithEasing);
  console.log(`Easing: ${easing.padEnd(8)} | Scale: ${result.zoomScale.toFixed(3)}`);
});

console.log('\n🎯 Test completed! Check the results above.');
