# 🔧 Zoom Positioning - Technical Implementation Guide

This document explains the technical architecture and implementation details of the zoom positioning feature in the React Video Editor.

## 🏗️ **Architecture Overview**

The zoom positioning system consists of several interconnected components that work together to provide precise zoom control:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Timeline      │    │   Zoom Store     │    │  Canvas         │
│   Header        │◄──►│   (Position)     │◄──►│  Container      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   UI Controls   │    │  State Management│    │ Zoom Overlay    │
│   (Move/Reset)  │    │  (Coordinates)   │    │ (Interactive)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📦 **Core Components**

### 1. **Zoom Store (`use-zoom-store.ts`)**

**Purpose**: Centralized state management for all zoom-related configuration.

```typescript
interface IZoomConfig {
  maxZoomScale: number;
  bezierControlPoints: { p1, p2, p3, p4 };
  defaultTiming: { startTime, endTime };
  zoomOut: { duration, enabled, easing };
  zoomArea: { x: number, y: number, width: number, height: number }; // ← NEW: Zoom area rectangle
  calculatedZoomScale: number; // ← NEW: Dynamic zoom scale based on area size
}
```

**Key Methods**:
- `setZoomArea(area)` - Updates zoom area rectangle
- `setZoomAreaFromRect(startX, startY, endX, endY)` - Sets zoom area from drag coordinates
- `resetZoomArea()` - Resets to center area (25%, 25%, 50%, 50%)
- `getZoomCenter()` - Gets center point of current zoom area
- Coordinates are normalized (0-1 range) for resolution independence
- Zoom scale is calculated dynamically: smaller area = higher zoom

### 2. **Zoom Area Overlay (`zoom-position-overlay.tsx`)**

**Purpose**: Interactive overlay that allows users to drag to select zoom area with dynamic zoom scale.

**Key Features**:
```typescript
// Drag handling with coordinate normalization
const handleMouseDown = (event: React.MouseEvent) => {
  const rect = overlayRef.current.getBoundingClientRect();
  const x = (event.clientX - rect.left) / rect.width;
  const y = (event.clientY - rect.top) / rect.height;

  setIsDragging(true);
  setDragStart({ x: clampedX, y: clampedY });
  setDragEnd({ x: clampedX, y: clampedY });
};

const handleMouseUp = () => {
  if (isDragging && width > 0.02 && height > 0.02) {
    setZoomAreaFromRect(dragStart.x, dragStart.y, dragEnd.x, dragEnd.y);
  }
  setIsDragging(false);
};
```

**Visual Elements**:
- Semi-transparent dark overlay (`rgba(0, 0, 0, 0.3)`)
- Blue rectangle showing current zoom area
- Yellow rectangle during drag selection
- Crosshair lines showing area center
- Real-time area size and center coordinate display
- Dynamic zoom scale calculation based on area size

### 3. **Canvas Container (`canvas-container.tsx`)**

**Purpose**: Applies the zoom transform with dynamic transform-origin.

**Transform Origin Calculation**:
```typescript
// Memoized for performance
const transformOrigin = useMemo(() => 
  `${zoomConfig.position.x * 100}% ${zoomConfig.position.y * 100}%`,
  [zoomConfig.position.x, zoomConfig.position.y]
);

// Applied to zoom transform
style={{
  transform: zoomTransformCache.getTransform(getCanvasZoomScale),
  transformOrigin: transformOrigin, // Dynamic based on user selection
}}
```

### 4. **Timeline Header Controls (`header.tsx`)**

**Purpose**: UI controls for activating zoom positioning and resetting position.

**Controls**:
- **Move Button**: Toggles zoom positioning mode
- **Coordinate Display**: Shows current position as percentages
- **Reset Button**: Returns zoom to center position

## 🔄 **State Management Flow**

### **Activation Flow**:
```
User clicks Move button
       ↓
setIsZoomPositioningActive(true)
       ↓
SceneInteractions disabled (prevents selection interference)
       ↓
ZoomPositionOverlay renders with high z-index
       ↓
Video content pointer-events disabled
```

### **Position Setting Flow**:
```
User clicks on overlay
       ↓
Mouse coordinates captured
       ↓
Normalized to 0-1 range
       ↓
setZoomPosition({ x, y })
       ↓
Transform origin updated
       ↓
Zoom effect repositioned
```

## 🎯 **Coordinate System**

### **Normalization**:
- **X-axis**: 0 = left edge, 0.5 = center, 1 = right edge
- **Y-axis**: 0 = top edge, 0.5 = center, 1 = bottom edge
- **Default**: Center position (0.5, 0.5)

### **Transform Origin Mapping**:
```typescript
// User clicks at 30% from left, 70% from top
position = { x: 0.3, y: 0.7 }

// Converted to CSS transform-origin
transformOrigin = "30% 70%"

// Zoom scales from this point instead of center
```

## ⚡ **Performance Optimizations**

### 1. **Memoized Calculations**:
```typescript
// Transform origin only recalculated when position changes
const transformOrigin = useMemo(() => 
  `${zoomConfig.position.x * 100}% ${zoomConfig.position.y * 100}%`,
  [zoomConfig.position.x, zoomConfig.position.y]
);
```

### 2. **Cached Transforms**:
```typescript
// Zoom transform strings cached to avoid repeated calculations
transform: zoomTransformCache.getTransform(getCanvasZoomScale)
```

### 3. **Selective Rendering**:
```typescript
// Overlay only renders when active
if (!isActive) return null;
```

## 🚫 **Interference Prevention**

### **Problem**: Selection overlay was intercepting clicks before they reached zoom overlay.

### **Solution**: Conditional disabling of interfering systems:

```typescript
// SceneInteractions - Skip selection creation when positioning active
useEffect(() => {
  if (isZoomPositioningActive) {
    return; // Don't create selection overlay
  }
  
  const selection = new Selection({...});
}, [isZoomPositioningActive]);

// Disable Moveable targets
target={isZoomPositioningActive ? [] : targets}

// Disable video content pointer events
style={{ 
  pointerEvents: isZoomPositioningActive ? 'none' : 'auto' 
}}
```

## 🎬 **Remotion Integration**

### **Player Context**:
- Uses `useZoomStore` to get current position
- Applies transform-origin to canvas container
- Real-time preview during positioning

### **Export Context**:
- Position data passed through `VideoEditorComposition`
- Same transform-origin calculation applied
- Positioned zoom included in final render

```typescript
// VideoEditorComposition.tsx
style={{
  transform: `scale(${canvasZoomScale})`,
  transformOrigin: `${effectiveZoomConfig.position.x * 100}% ${effectiveZoomConfig.position.y * 100}%`,
}}
```

## 🔧 **Key Technical Decisions**

### 1. **Normalized Coordinates**:
- **Why**: Resolution independence, easier calculations
- **Range**: 0-1 for both X and Y axes
- **Benefits**: Works with any canvas size, simple percentage conversion

### 2. **CSS Transform-Origin**:
- **Why**: Native browser optimization, smooth performance
- **Alternative**: Manual matrix calculations (more complex, slower)
- **Benefits**: Hardware acceleration, simple implementation

### 3. **High Z-Index Overlay**:
- **Why**: Ensure overlay is always on top
- **Value**: 9999 (higher than most UI elements)
- **Benefits**: Reliable click capture, visual clarity

### 4. **Pointer Events Management**:
- **Why**: Prevent click interference from underlying elements
- **Method**: Conditional `pointer-events: none` on video content
- **Benefits**: Clean click handling, no event bubbling issues

## 🐛 **Common Issues & Solutions**

### **Issue**: Overlay not receiving clicks
**Solution**: Check z-index, pointer-events, and interfering overlays

### **Issue**: Position not updating
**Solution**: Verify store connection and coordinate normalization

### **Issue**: Transform not applying
**Solution**: Check transform-origin calculation and CSS syntax

### **Issue**: Performance problems
**Solution**: Ensure memoization and avoid unnecessary re-renders

## 📊 **Testing Considerations**

### **Unit Tests**:
- Coordinate normalization functions
- Transform-origin calculation
- Store state management

### **Integration Tests**:
- Click handling and position setting
- Store updates and UI synchronization
- Remotion export with positioned zoom

### **Performance Tests**:
- Overlay rendering performance
- Transform calculation efficiency
- Memory usage during positioning

---

This technical implementation provides a robust, performant zoom positioning system that integrates seamlessly with the existing video editor architecture while maintaining clean separation of concerns and optimal user experience.
