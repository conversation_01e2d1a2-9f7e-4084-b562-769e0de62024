import * as React from "react";
import { PlusIcon, Upload } from "lucide-react";
import Dropzone, {
  type DropzoneProps,
  type FileRejection,
} from "react-dropzone";
import { cn, formatBytes } from "@/lib/utils";
import { useGlobalFileDrag } from "@/features/editor/hooks/use-global-file-drag";

interface ProgressiveFileUploaderProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Function to be called when files are uploaded.
   */
  onValueChange?: (files: File[]) => void;

  /**
   * Accepted file types for the uploader.
   */
  accept?: DropzoneProps["accept"];

  /**
   * Maximum file size for the uploader.
   */
  maxSize?: DropzoneProps["maxSize"];

  /**
   * Maximum number of files for the uploader.
   */
  maxFileCount?: DropzoneProps["maxFiles"];

  /**
   * Whether the uploader should accept multiple files.
   */
  multiple?: boolean;

  /**
   * Whether the uploader is disabled.
   */
  disabled?: boolean;

  /**
   * Message to show when no files are uploaded.
   */
  emptyMessage?: string;

  /**
   * Whether to show the upload interface when files are present.
   */
  showUploaderWhenHasFiles?: boolean;

  /**
   * Whether files have been uploaded (external state).
   */
  hasFiles?: boolean;
}

export function ProgressiveFileUploader(props: ProgressiveFileUploaderProps) {
  const {
    onValueChange,
    accept = {
      "video/*": [".mp4", ".mov", ".avi", ".mkv", ".webm"],
    },
    maxSize = 500 * 1024 * 1024, // 500MB
    maxFileCount = 10,
    multiple = true,
    disabled = false,
    emptyMessage = "No videos uploaded yet - Upload video files to get started",
    showUploaderWhenHasFiles = false,
    hasFiles = false,
    className,
    children,
    ...dropzoneProps
  } = props;

  const { isDraggingFiles, hasVideoFiles } = useGlobalFileDrag();

  const onDrop = React.useCallback(
    (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
      // Enhanced error handling for rejected files
      if (rejectedFiles.length > 0) {
        rejectedFiles.forEach((rejection) => {
          const { file, errors } = rejection;
          errors.forEach((error) => {
            let errorMessage = `Failed to upload "${file.name}": `;

            switch (error.code) {
              case 'file-too-large':
                errorMessage += `File size (${formatBytes(file.size)}) exceeds maximum allowed size (${formatBytes(maxSize)})`;
                break;
              case 'file-invalid-type':
                errorMessage += `File type "${file.type}" is not supported`;
                break;
              case 'too-many-files':
                errorMessage += `Too many files selected. Maximum allowed: ${maxFileCount}`;
                break;
              default:
                errorMessage += error.message;
            }

            console.error(errorMessage);
          });
        });
      }

      if (!multiple && maxFileCount === 1 && acceptedFiles.length > 1) {
        console.error("Cannot upload more than 1 file at a time");
        return;
      }

      onValueChange?.(acceptedFiles);
    },
    [maxFileCount, multiple, maxSize, onValueChange]
  );

  // Show uploader when:
  // 1. No files have been uploaded yet (initial state)
  // 2. Files are being dragged over the page
  // 3. Explicitly requested to show when files are present
  const shouldShowUploader = !hasFiles || isDraggingFiles || showUploaderWhenHasFiles;

  // Show empty message when no files and not dragging
  const shouldShowEmptyMessage = !hasFiles && !isDraggingFiles;

  // Show drop zone when dragging files over the page
  const shouldShowDropZone = isDraggingFiles && hasVideoFiles;

  if (shouldShowEmptyMessage) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
        <Upload className="h-8 w-8 mb-2" />
        <p className="text-sm">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className="relative flex flex-col gap-6 overflow-hidden">
      <Dropzone
        onDrop={shouldShowDropZone ? () => {} : onDrop} // Disable drop when showing grid item
        accept={accept}
        maxSize={maxSize}
        maxFiles={maxFileCount}
        multiple={maxFileCount > 1 || multiple}
        disabled={disabled || shouldShowDropZone} // Disable entirely when showing grid item
        noClick={true} // Always disable click since we handle it with the plus button
      >
        {({ getRootProps, getInputProps, isDragActive }) => (
          <div
            {...getRootProps()}
            className={cn(
              "w-full",
              disabled && "pointer-events-none opacity-60",
            )}
            {...dropzoneProps}
          >
            <input {...getInputProps()} />
            {children}
          </div>
        )}
      </Dropzone>
    </div>
  );
}
