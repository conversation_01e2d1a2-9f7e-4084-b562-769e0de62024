/**
 * Remotion Player Profiler - Specialized debugging for Remotion performance issues
 * Based on the analysis showing 280 frame updates causing 100% CPU usage
 */

interface FrameUpdateMetrics {
  frame: number;
  timestamp: number;
  duration: number;
  componentRenders: number;
  storeChangeDuration: number;
  isPlaying: boolean;
}

interface RemotionPerformanceData {
  frameUpdates: FrameUpdateMetrics[];
  componentRenderCounts: { [componentName: string]: number };
  averageFrameUpdateDuration: number;
  maxFrameUpdateDuration: number;
  totalCPUTime: number;
  problematicFrames: FrameUpdateMetrics[];
}

export class RemotionProfiler {
  private static instance: RemotionProfiler;
  private isActive = false;
  private frameMetrics: FrameUpdateMetrics[] = [];
  private componentRenders: { [componentName: string]: number } = {};
  private currentFrameStart = 0;
  private frameUpdateCount = 0;

  static getInstance(): RemotionProfiler {
    if (!RemotionProfiler.instance) {
      RemotionProfiler.instance = new RemotionProfiler();
    }
    return RemotionProfiler.instance;
  }

  startProfiling() {
    this.isActive = true;
    this.frameMetrics = [];
    this.componentRenders = {};
    this.frameUpdateCount = 0;
    console.log('🎬 Remotion Profiler started - Tracking frame update performance');
  }

  stopProfiling(): RemotionPerformanceData {
    this.isActive = false;
    const data = this.generateReport();
    console.log('🎬 Remotion Profiler stopped');
    return data;
  }

  // Track the start of a frame update
  startFrameUpdate(frame: number, isPlaying: boolean = false) {
    if (!this.isActive) return;
    
    this.currentFrameStart = performance.now();
    this.frameUpdateCount++;
    
    // Log every frame update with timing
    console.log(`🎬 Frame ${frame} update started (${this.frameUpdateCount})`);
  }

  // Track the end of a frame update
  endFrameUpdate(frame: number, storeChangeDuration: number = 0) {
    if (!this.isActive || this.currentFrameStart === 0) return;
    
    const duration = performance.now() - this.currentFrameStart;
    const componentRenders = this.getRecentComponentRenders();
    
    const metric: FrameUpdateMetrics = {
      frame,
      timestamp: performance.now(),
      duration,
      componentRenders,
      storeChangeDuration,
      isPlaying: false // We'll update this based on player state
    };
    
    this.frameMetrics.push(metric);
    
    // Log slow frame updates immediately
    if (duration > 10) {
      console.warn(`⚠️ Slow frame update: ${duration.toFixed(2)}ms for frame ${frame}`);
      console.warn(`   Component renders: ${componentRenders}`);
      console.warn(`   Store change: ${storeChangeDuration.toFixed(2)}ms`);
    }
    
    // Keep only last 200 metrics to prevent memory issues
    if (this.frameMetrics.length > 200) {
      this.frameMetrics = this.frameMetrics.slice(-200);
    }
    
    this.currentFrameStart = 0;
  }

  // Track component renders
  trackComponentRender(componentName: string) {
    if (!this.isActive) return;
    
    this.componentRenders[componentName] = (this.componentRenders[componentName] || 0) + 1;
  }

  // Get recent component render count (for current frame)
  private getRecentComponentRenders(): number {
    const recent = Object.values(this.componentRenders).reduce((sum, count) => sum + count, 0);
    // Reset for next frame
    this.componentRenders = {};
    return recent;
  }

  // Generate comprehensive performance report
  private generateReport(): RemotionPerformanceData {
    if (this.frameMetrics.length === 0) {
      return {
        frameUpdates: [],
        componentRenderCounts: {},
        averageFrameUpdateDuration: 0,
        maxFrameUpdateDuration: 0,
        totalCPUTime: 0,
        problematicFrames: []
      };
    }

    const durations = this.frameMetrics.map(m => m.duration);
    const averageDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const maxDuration = Math.max(...durations);
    const totalCPUTime = durations.reduce((sum, d) => sum + d, 0);
    
    // Find problematic frames (>10ms duration)
    const problematicFrames = this.frameMetrics.filter(m => m.duration > 10);
    
    const report: RemotionPerformanceData = {
      frameUpdates: this.frameMetrics,
      componentRenderCounts: { ...this.componentRenders },
      averageFrameUpdateDuration: averageDuration,
      maxFrameUpdateDuration: maxDuration,
      totalCPUTime,
      problematicFrames
    };

    // Log detailed analysis
    console.log('\n🎬 Remotion Performance Report:');
    console.log(`📊 Frame Updates: ${this.frameMetrics.length}`);
    console.log(`⏱️  Average Duration: ${averageDuration.toFixed(2)}ms`);
    console.log(`🔥 Max Duration: ${maxDuration.toFixed(2)}ms`);
    console.log(`💻 Total CPU Time: ${totalCPUTime.toFixed(2)}ms`);
    console.log(`⚠️  Problematic Frames: ${problematicFrames.length}`);
    
    if (problematicFrames.length > 0) {
      console.log('\n🚨 Slowest Frame Updates:');
      problematicFrames
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 5)
        .forEach((frame, index) => {
          console.log(`${index + 1}. Frame ${frame.frame}: ${frame.duration.toFixed(2)}ms (${frame.componentRenders} renders)`);
        });
    }

    // Performance analysis
    this.analyzePerformance(report);
    
    // Export to window for further analysis
    (window as any).remotionProfileData = report;
    console.log('\n📊 Full report exported to window.remotionProfileData');
    
    return report;
  }

  private analyzePerformance(data: RemotionPerformanceData) {
    console.log('\n🔍 Remotion Performance Analysis:');
    
    // Frame update frequency analysis
    const avgFrameInterval = data.frameUpdates.length > 1 ? 
      (data.frameUpdates[data.frameUpdates.length - 1].timestamp - data.frameUpdates[0].timestamp) / data.frameUpdates.length : 0;
    
    console.log(`📈 Frame Update Frequency: ${(1000 / avgFrameInterval).toFixed(1)} fps`);
    
    if (data.averageFrameUpdateDuration > 16) {
      console.log('🚨 CRITICAL: Frame updates taking longer than 16ms (60fps threshold)');
      console.log('   This will cause dropped frames and poor performance');
    }
    
    if (data.maxFrameUpdateDuration > 50) {
      console.log('🚨 CRITICAL: Some frame updates taking >50ms');
      console.log('   This will cause noticeable stuttering');
    }
    
    // Component render analysis
    const totalRenders = Object.values(data.componentRenderCounts).reduce((sum, count) => sum + count, 0);
    const avgRendersPerFrame = totalRenders / data.frameUpdates.length;
    
    console.log(`🎭 Component Renders: ${totalRenders} total, ${avgRendersPerFrame.toFixed(1)} per frame`);
    
    if (avgRendersPerFrame > 5) {
      console.log('⚠️ High component render count per frame');
      console.log('   Consider memoization or reducing re-renders');
    }
    
    // CPU usage estimation
    const cpuUsagePercent = (data.totalCPUTime / (data.frameUpdates.length * 16.67)) * 100; // Assuming 60fps target
    console.log(`💻 Estimated CPU Usage: ${cpuUsagePercent.toFixed(1)}%`);
    
    if (cpuUsagePercent > 80) {
      console.log('🚨 CRITICAL: High CPU usage from frame updates');
    }
    
    console.log('\n💡 Recommendations:');
    
    if (data.averageFrameUpdateDuration > 10) {
      console.log('   • Optimize component rendering in video sequences');
      console.log('   • Consider reducing video quality or resolution');
      console.log('   • Check for expensive calculations in useCurrentFrame hooks');
    }
    
    if (data.problematicFrames.length > data.frameUpdates.length * 0.1) {
      console.log('   • >10% of frames are slow - systematic performance issue');
      console.log('   • Profile individual components for bottlenecks');
    }
    
    if (avgRendersPerFrame > 3) {
      console.log('   • Reduce component re-renders with React.memo');
      console.log('   • Optimize useCurrentFrame usage');
    }
  }

  // Get current status for debugging
  getStatus() {
    return {
      isActive: this.isActive,
      frameUpdateCount: this.frameUpdateCount,
      metricsCollected: this.frameMetrics.length,
      recentFrames: this.frameMetrics.slice(-5),
      componentRenders: { ...this.componentRenders }
    };
  }
}

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).remotionProfiler = RemotionProfiler.getInstance();
}

export default RemotionProfiler;
