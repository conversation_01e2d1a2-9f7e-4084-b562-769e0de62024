# Image URL Fix for Remotion Rendering

## Problem

During video rendering, <PERSON><PERSON><PERSON> was trying to load preloaded images from `http://localhost:3000/` instead of the render server at `http://localhost:3001/`, causing 404 errors:

```
http://localhost:3000/image_20250707_012001_0.png Failed to load resource: the server responded with a status of 404 (Not Found)
```

## Root Cause

The preloaded images in `use-local-images-store.ts` were using relative paths starting with `/`, which resolved to the current domain context. During Remotion rendering, this context was different from the development server, causing the images to be requested from the wrong URL.

### Before (Problematic):
```typescript
const PRELOADED_IMAGES = [
  { path: "/image_20250707_012001_0.png", name: "Image 1" }, // ❌ Relative path
  // ... more images
];
```

This resolved to:
- **In browser**: `http://localhost:5174/image_20250707_012001_0.png` ✅
- **In Remotion render**: `http://localhost:3000/image_20250707_012001_0.png` ❌

## Solution

### 1. Serve Images from Render Server

Updated the render server to serve static files from the public directory:

```javascript
// remotion-render-server/server.js
app.use('/', express.static(path.join(__dirname, '../public')));
```

This makes images available at:
- `http://localhost:3001/image_20250707_012001_0.png`

### 2. Use Absolute URLs in Image Store

Updated the image store to use absolute URLs pointing to the render server:

```typescript
// src/features/editor/store/use-local-images-store.ts
const RENDER_SERVER_URL = 'http://localhost:3001';
const PRELOADED_IMAGES = [
  { path: `${RENDER_SERVER_URL}/image_20250707_012001_0.png`, name: "Image 1" }, // ✅ Absolute URL
  // ... more images
];
```

## Benefits

### ✅ **Consistent Image Loading**
- Images load from the same URL in both browser and render contexts
- No more 404 errors during video rendering

### ✅ **Render Server Independence**
- Images are served directly from the render server
- No dependency on the main development server being available

### ✅ **Cross-Context Compatibility**
- Works in browser preview (development)
- Works in Remotion rendering (server-side)
- Works in production builds

## File Changes

### 1. Render Server (`remotion-render-server/server.js`)

**Added static file serving:**
```javascript
// Serve static files from the main application's public directory
app.use('/', express.static(path.join(__dirname, '../public')));
```

### 2. Image Store (`src/features/editor/store/use-local-images-store.ts`)

**Updated image paths:**
```typescript
// Before
{ path: "/image_20250707_012001_0.png", name: "Image 1" }

// After  
{ path: "http://localhost:3001/image_20250707_012001_0.png", name: "Image 1" }
```

## Testing

To verify the fix:

1. **Check image accessibility**:
   ```bash
   curl http://localhost:3001/image_20250707_012001_0.png
   ```

2. **Test in browser**: Images should load in the editor interface

3. **Test in render**: Video export should complete without 404 errors

## Data Flow (Fixed)

```
Public Directory Images
    ↓
Render Server (serves at localhost:3001)
    ↓
Image Store (absolute URLs to render server)
    ↓
Browser Context (loads from render server) ✅
    ↓
Remotion Render Context (loads from render server) ✅
    ↓
Successful Video Rendering ✅
```

## Production Considerations

For production deployment:

1. **Update RENDER_SERVER_URL**: Change to production render server URL
2. **Environment Variables**: Use environment variables for server URLs
3. **CDN Integration**: Consider serving images from a CDN
4. **CORS Configuration**: Ensure proper CORS headers for cross-origin requests

### Example Environment Configuration:

```typescript
const RENDER_SERVER_URL = process.env.RENDER_SERVER_URL || 'http://localhost:3001';
```

## Alternative Solutions Considered

1. **Webpack Public Path**: Tried configuring webpack output.publicPath, but bundle context issues persisted
2. **Proxy Middleware**: Considered proxying requests, but absolute URLs are simpler and more reliable
3. **Base Tag**: HTML base tag wouldn't work in Remotion's rendering context

## Key Takeaway

When working with Remotion server-side rendering, always use absolute URLs for assets that need to be accessible from both browser and render contexts. Relative paths can resolve differently depending on the execution environment.
