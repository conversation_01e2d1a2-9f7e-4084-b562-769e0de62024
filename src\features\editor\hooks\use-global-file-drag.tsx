import { useState, useEffect, useCallback } from "react";

/**
 * Hook to detect when files are being dragged over the webpage
 * Distinguishes between file drags from external sources and internal element drags
 */
export const useGlobalFileDrag = () => {
  const [isDraggingFiles, setIsDraggingFiles] = useState(false);
  const [draggedFileTypes, setDraggedFileTypes] = useState<string[]>([]);

  const checkIfFileDrag = useCallback((dataTransfer: DataTransfer | null): boolean => {
    if (!dataTransfer) return false;

    // Check if the drag contains files
    if (dataTransfer.files && dataTransfer.files.length > 0) {
      return true;
    }

    // Check if the drag contains file types in the types array
    const types = Array.from(dataTransfer.types);
    return types.includes('Files');
  }, []);

  const getFileTypes = useCallback((dataTransfer: DataTransfer | null): string[] => {
    if (!dataTransfer) return [];

    const types: string[] = [];

    // If files are already available, get their types
    if (dataTransfer.files && dataTransfer.files.length > 0) {
      for (let i = 0; i < dataTransfer.files.length; i++) {
        const file = dataTransfer.files[i];
        if (file.type) {
          types.push(file.type);
        }
      }
    }

    // During drag operations, files might not be available yet
    // We'll assume media files are being dragged if Files type is present
    // This will be validated on actual drop
    const dataTypes = Array.from(dataTransfer.types);
    if (dataTypes.includes('Files') && types.length === 0) {
      // Return placeholders to indicate potential media files
      return ['video/*', 'audio/*'];
    }

    return types;
  }, []);

  const isVideoFile = useCallback((types: string[]): boolean => {
    return types.some(type => type.startsWith('video/'));
  }, []);

  const isAudioFile = useCallback((types: string[]): boolean => {
    return types.some(type => type.startsWith('audio/'));
  }, []);

  useEffect(() => {
    let dragCounter = 0; // Track drag enter/leave to handle nested elements

    const handleDragEnter = (e: DragEvent) => {
      e.preventDefault();
      dragCounter++;
      
      if (checkIfFileDrag(e.dataTransfer)) {
        const fileTypes = getFileTypes(e.dataTransfer);
        setDraggedFileTypes(fileTypes);
        setIsDraggingFiles(true);
      }
    };

    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      
      // Ensure we maintain the dragging state during drag over
      if (checkIfFileDrag(e.dataTransfer)) {
        const fileTypes = getFileTypes(e.dataTransfer);
        setDraggedFileTypes(fileTypes);
        setIsDraggingFiles(true);
      }
    };

    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault();
      dragCounter--;
      
      // Only set dragging to false when we've left all nested elements
      if (dragCounter === 0) {
        setIsDraggingFiles(false);
        setDraggedFileTypes([]);
      }
    };

    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      dragCounter = 0;
      setIsDraggingFiles(false);
      setDraggedFileTypes([]);
    };

    // Add event listeners to document to catch all drag events
    document.addEventListener('dragenter', handleDragEnter);
    document.addEventListener('dragover', handleDragOver);
    document.addEventListener('dragleave', handleDragLeave);
    document.addEventListener('drop', handleDrop);

    return () => {
      document.removeEventListener('dragenter', handleDragEnter);
      document.removeEventListener('dragover', handleDragOver);
      document.removeEventListener('dragleave', handleDragLeave);
      document.removeEventListener('drop', handleDrop);
    };
  }, [checkIfFileDrag, getFileTypes]);

  const resetDragState = useCallback(() => {
    setIsDraggingFiles(false);
    setDraggedFileTypes([]);
  }, []);

  return {
    isDraggingFiles,
    draggedFileTypes,
    isVideoFile: isVideoFile(draggedFileTypes),
    hasVideoFiles: draggedFileTypes.some(type => type.startsWith('video/')),
    isAudioFile: isAudioFile(draggedFileTypes),
    hasAudioFiles: draggedFileTypes.some(type => type.startsWith('audio/')),
    resetDragState,
  };
};
