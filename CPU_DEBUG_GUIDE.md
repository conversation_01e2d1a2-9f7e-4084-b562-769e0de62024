# 🔥 CPU Usage Debugging Guide for Remotion Video Player

## 🎯 Overview

This guide helps you debug the CPU usage issue during video playback in your React video editor. We've added comprehensive debugging tools to identify performance bottlenecks in the Remotion player.

## 🛠️ New Debugging Tools Added

### 1. Enhanced Performance Monitor (`performance-monitor.ts`)
- **CPU Usage Tracking**: Estimates CPU load using setTimeout delays
- **Video Playback Metrics**: Tracks seek operations, frame updates, render calls
- **Remotion Event Logging**: Monitors frameupdate, seek, and render events
- **Performance Analysis**: Automatically identifies performance issues and provides recommendations

### 2. CPU Monitor (`cpu-monitor.ts`)
- **Real-time CPU Monitoring**: Samples CPU usage every 100ms
- **Video State Tracking**: Monitors playing/dragging state, frame updates, seek operations
- **Operation Tracking**: Tracks specific operations that might cause CPU spikes
- **Memory Usage**: Monitors JavaScript heap usage
- **Detailed Reporting**: Generates comprehensive performance reports

### 3. CPU Debug Panel (`cpu-debug-panel.tsx`)
- **Visual Interface**: Real-time monitoring dashboard
- **Live Metrics**: CPU usage, frame rate, memory usage
- **Video State Display**: Shows playing/dragging status, current frame, operation counts
- **Export Functionality**: Export metrics data for analysis
- **Easy Controls**: Start/stop monitoring, clear logs

## 🚀 How to Use the Debugging Tools

### Quick Start
1. **Open the CPU Debug Panel**: Press `Ctrl+Shift+C` in the editor
2. **Start Monitoring**: Click "Start Monitor" button
3. **Test Video Playback**: Play video or drag the playhead
4. **Monitor CPU Usage**: Watch real-time CPU metrics
5. **Check Console**: Detailed logs appear in browser console
6. **Stop and Analyze**: Click "Stop Monitor" to see performance report

### Keyboard Shortcuts
- `Ctrl+Shift+C`: Toggle CPU Debug Panel
- `Ctrl+Shift+D`: Toggle existing Debug Panel (if available)

### Console Commands
```javascript
// Manual control via browser console
window.cpuMonitor.startMonitoring()
window.cpuMonitor.stopMonitoring()
window.cpuMonitor.getStatus()

// Access performance monitor
window.perfMonitor.startMonitoring()
window.perfMonitor.stopMonitoring()

// View collected metrics
console.log(window.cpuMetrics)
```

## 🔍 What to Look For

### High CPU Usage Indicators
- **CPU > 80%**: Critical performance issue
- **CPU > 60%**: Warning level, monitor closely
- **Frame Rate < 30fps**: Performance degradation
- **Excessive Seek Operations**: > 10 seeks per second during dragging
- **High Frame Update Frequency**: > 60 updates per second

### Common Performance Issues

#### 1. Excessive Frame Updates
**Symptoms:**
- Frame updates > 1000 during short test
- CPU spikes during playhead dragging
- Console shows "High frequency frame updates"

**Debugging:**
```javascript
// Check frame update throttling
console.log('Frame updates:', window.cpuMonitor.getStatus().videoState.frameUpdates)
```

#### 2. Frequent Seek Operations
**Symptoms:**
- Average seek interval < 16ms
- Seek operations > 100 during dragging
- Console shows "Very frequent seek operations"

**Debugging:**
```javascript
// Monitor seek frequency
console.log('Seeks:', window.cpuMonitor.getStatus().videoState.seekOperations)
```

#### 3. Render Call Overload
**Symptoms:**
- Render calls > 500 during test
- CPU spikes during video rendering
- Console shows "Excessive render calls"

**Debugging:**
```javascript
// Check render frequency
window.perfMonitor.trackFunction('_render')
```

## 📊 Performance Analysis

### Automatic Analysis
The system automatically analyzes performance and provides recommendations:

```
🔍 Performance Analysis:
🚨 HIGH CPU USAGE DETECTED!
   Max CPU: 85.2% (threshold: 80%)
   ⚠️  Excessive frame updates detected
   Frame updates: 1247
   ⚠️  Very frequent seek operations detected
   Avg seek interval: 12.3ms

💡 Recommendations:
   • Check if playhead dragging throttling is working
   • Verify frame update optimization is active
   • Consider reducing video quality during playback
```

### Manual Analysis
1. **Export Metrics**: Click "Export Data" in debug panel
2. **Analyze JSON**: Review exported performance data
3. **Identify Patterns**: Look for correlations between operations and CPU spikes
4. **Compare States**: Analyze differences between normal playback and dragging

## 🎯 Specific Debugging Scenarios

### Scenario 1: CPU Spikes During Playhead Dragging
```javascript
// 1. Start monitoring
window.cpuMonitor.startMonitoring()

// 2. Start dragging playhead
// 3. Watch for these console messages:
// "🚨 HIGH CPU: 85.2% - Operations: playhead-dragging"
// "⚠️ High frequency frame updates in recent activity"

// 4. Check throttling effectiveness
console.log('Dragging state:', window.cpuMonitor.getStatus().videoState.isDragging)
```

### Scenario 2: Video Rendering Performance
```javascript
// Monitor video rendering specifically
window.cpuMonitor.startOperation('video-render-test')

// Play video and watch for:
// "🔧 Operation started: video-render-test"
// CPU usage during playback
// Frame rate drops

window.cpuMonitor.endOperation('video-render-test')
```

### Scenario 3: Memory Leak Detection
```javascript
// Monitor memory usage over time
setInterval(() => {
  const status = window.cpuMonitor.getStatus()
  console.log('Memory:', status.memoryUsage, 'MB')
}, 5000)
```

## 🔧 Integration Points

### Where Debugging is Active
1. **Frame Updates**: `use-current-frame.tsx` - Tracks every frame update
2. **Seek Operations**: `playhead.tsx` - Monitors seek frequency and timing
3. **Video Rendering**: `video.ts` - Tracks render calls and performance
4. **Player State**: Monitors Remotion player events and state changes

### Data Collection Points
- **CPU Sampling**: Every 100ms during monitoring
- **Frame Rate**: Calculated every second
- **Memory Usage**: Sampled with CPU usage
- **Video Events**: Real-time tracking of Remotion events

## 🚨 Troubleshooting

### Debug Panel Not Showing
1. Check console for errors
2. Verify `Ctrl+Shift+C` keyboard shortcut
3. Try clicking the small "🔥 CPU Debug" button in top-right corner

### No CPU Data
1. Ensure monitoring is started
2. Check browser console for errors
3. Verify performance.now() is available

### High CPU but No Clear Cause
1. Export metrics data for detailed analysis
2. Check for background operations
3. Monitor during different video operations (play, pause, seek)
4. Compare with/without video content

## 📈 Expected Results

### Normal Performance
- CPU usage: 20-40% during playback
- Frame rate: 50-60 fps
- Seek interval: 16-33ms during dragging
- Memory usage: Stable, no significant growth

### Performance Issues
- CPU usage: >80% sustained
- Frame rate: <30 fps
- Seek interval: <10ms (too frequent)
- Memory usage: Continuously growing

## 🎉 Next Steps

1. **Start with the Debug Panel**: Use `Ctrl+Shift+C` to open
2. **Test Different Scenarios**: Normal playback, dragging, seeking
3. **Analyze Console Output**: Look for specific performance warnings
4. **Export and Share Data**: Use exported metrics for further analysis
5. **Iterate on Fixes**: Use the debugging tools to verify improvements

The debugging system will help you identify exactly where the CPU bottleneck is occurring in your Remotion player setup!
