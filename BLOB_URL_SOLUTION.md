# Blob URL Solution for Video Export

## Problem

The original implementation had two critical issues:

1. **Remotion API Change**: The `quality` parameter was renamed to `jpegQuality` in newer versions
2. **Blob URL Access**: Server-side rendering cannot access blob URLs created in the browser context

## Error Messages

```
"The "quality" option has been renamed. Please use "jpegQuality" instead."
Could not extract frame from compositor Error: Can only download URLs starting with http:// or https://, got "blob:http://localhost:5173/cc274104-88f9-4a70-a746-791914d80a36"
```

## Solution Overview

### 1. Fixed Remotion API Parameter

**File**: `remotion-render-server/server.js`

```javascript
// Before (causing error)
await renderMedia({
  // ...
  quality: quality,
});

// After (fixed)
await renderMedia({
  // ...
  jpegQuality: quality, // Updated parameter name
});
```

### 2. Blob URL Conversion System

Created a file upload system that converts blob URLs to accessible server URLs before rendering.

#### Architecture

```
Browser (Blob URLs) → Upload to Server → Server URLs → Remotion Rendering
```

#### Components Added

**A. Server-side Upload Endpoint**
- Added multer middleware for file uploads
- Created `/upload` endpoint to receive blob files
- Serves uploaded files via `/uploads/` static route

**B. Frontend Blob Processing**
- Added `uploadFile()` method to upload blobs to server
- Added `processBlobUrls()` to recursively find and replace blob URLs
- Integrated into render pipeline before sending to server

**C. Progress Tracking**
- Added "Preparing files..." status for blob upload phase
- Enhanced progress indicators with better status messages

## Implementation Details

### Server Changes (`remotion-render-server/server.js`)

1. **Added Dependencies**:
   ```javascript
   import multer from 'multer';
   ```

2. **File Upload Configuration**:
   ```javascript
   const storage = multer.diskStorage({
     destination: async (req, file, cb) => {
       const uploadsDir = path.join(__dirname, 'uploads');
       await fs.mkdir(uploadsDir, { recursive: true });
       cb(null, uploadsDir);
     },
     filename: (req, file, cb) => {
       const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
       const ext = path.extname(file.originalname);
       cb(null, file.fieldname + '-' + uniqueSuffix + ext);
     }
   });
   ```

3. **Upload Endpoint**:
   ```javascript
   app.post('/upload', upload.single('file'), (req, res) => {
     // Handle file upload and return server URL
   });
   ```

4. **Static File Serving**:
   ```javascript
   app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
   ```

### Frontend Changes (`src/services/render-api.ts`)

1. **File Upload Method**:
   ```typescript
   async uploadFile(blob: Blob, filename: string): Promise<string> {
     const formData = new FormData();
     formData.append('file', blob, filename);
     // Upload and return server URL
   }
   ```

2. **Blob URL Processing**:
   ```typescript
   async processBlobUrls(inputProps: any): Promise<any> {
     // Recursively find blob URLs and replace with server URLs
   }
   ```

3. **Integration in Render Pipeline**:
   ```typescript
   async startRender(request: RenderRequest): Promise<RenderResponse> {
     // Process blob URLs before sending to server
     const processedRequest = {
       ...request,
       inputProps: await this.processBlobUrls(request.inputProps)
     };
     // Send to server
   }
   ```

## File Structure

```
remotion-render-server/
├── server.js              # Updated with upload endpoint
├── package.json           # Added multer dependency
├── uploads/               # Directory for uploaded files (auto-created)
└── renders/               # Directory for rendered videos

src/services/
└── render-api.ts          # Updated with blob processing

src/features/editor/
├── hooks/
│   └── use-video-export.ts # Enhanced progress tracking
└── components/
    └── export-dialog.tsx   # Better status messages
```

## How It Works

1. **User initiates export** with timeline containing local video files (blob URLs)
2. **Frontend processes input props**:
   - Finds all blob URLs in the data structure
   - Fetches each blob and uploads to server
   - Replaces blob URLs with server URLs
3. **Server receives render request** with accessible URLs
4. **Remotion renders video** using server-accessible files
5. **User downloads** completed video

## Benefits

- ✅ **Server Compatibility**: All URLs are accessible from server context
- ✅ **No Data Loss**: Original blob data is preserved through upload
- ✅ **Automatic Processing**: Blob conversion happens transparently
- ✅ **Progress Tracking**: User sees "Preparing files..." status
- ✅ **Error Handling**: Clear error messages for upload failures
- ✅ **Caching**: Uploaded files are cached to avoid re-uploads

## Testing

The solution can be tested by:

1. Adding local video files to the timeline
2. Initiating an export
3. Observing the "Preparing files..." status
4. Checking server logs for upload confirmations
5. Verifying successful video rendering

## Performance Considerations

- **Upload Time**: Large video files may take time to upload
- **Storage**: Server needs sufficient disk space for uploads
- **Cleanup**: Consider implementing cleanup for old uploaded files
- **Concurrent Uploads**: Multiple users may upload simultaneously

## Future Improvements

1. **Progress Tracking**: Show upload progress for large files
2. **File Cleanup**: Automatic cleanup of old uploaded files
3. **Compression**: Compress files before upload if needed
4. **Caching**: More sophisticated caching strategies
5. **Streaming**: Stream large files instead of full upload
