@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;

    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    --scene: 240 4.8% 93.9%;
    --sidebar: 240 4.8% 95.9%;
  }


}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

html {
  font-family: "Geist Variable";
}

.player-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-container {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.video-container {
  overflow: hidden;
}

.selecto-selection {
  background: rgba(0, 216, 214, 0.1) !important;
  border: 1px solid rgba(0, 216, 214, 1) !important;
}

@layer utilities {
  @variants responsive {
    .masonry {
      column-gap: 1.5em;
      column-count: 1;
    }
    .masonry-sm {
      gap: 0.5rem;
      column-count: 2;
    }
  }
}

.designcombo-scene-moveable .moveable-control {
  background: #e2e8f0 !important;
  box-sizing: border-box !important;
  display: block !important;
  z-index: 10000 !important;
  border-radius: 6px !important;
  cursor: crosshair !important;

  border: 1px solid #64748b !important;
  /* box-shadow: 0 0 2px 0 rgb(86, 90, 98, 0.2) !important; */
  width: 12px !important;
  height: 12px !important;
  margin-top: -6px !important;
  margin-left: -6px !important;
}

.designcombo-scene-moveable .moveable-control.moveable-n,
.designcombo-scene-moveable .moveable-control.moveable-s {
  width: 32px !important;
  height: 8px !important;
  margin-top: -4px !important;
  margin-left: -16px !important;
  border-radius: 8px !important;
  z-index: 9999 !important;
  cursor: crosshair !important;
}

.designcombo-scene-moveable .moveable-control.moveable-e,
.designcombo-scene-moveable .moveable-control.moveable-w {
  width: 8px !important;
  height: 32px !important;
  margin-left: -4px !important;
  margin-top: -16px !important;
  border-radius: 8px !important;
  z-index: 10 !important;
  cursor: crosshair !important;
}

.designcombo-scene-moveable .moveable-rotation-control {
  border: none !important;
  background-image: url("data:image/svg+xml,%3Csvg%20width%3D%2724%27%20height%3D%2724%27%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20fill%3D%27%23757575%27%3E%3Cg%20fill%3D%27none%27%20fill-rule%3D%27evenodd%27%3E%3Ccircle%20stroke%3D%27%23CCD1DA%27%20fill%3D%27%23FFF%27%20cx%3D%2712%27%20cy%3D%2712%27%20r%3D%2711.5%27%2F%3E%3Cpath%20d%3D%27M16.242%2012.012a4.25%204.25%200%2000-5.944-4.158L9.696%206.48a5.75%205.75%200%20018.048%205.532h1.263l-2.01%203.002-2.008-3.002h1.253zm-8.484-.004a4.25%204.25%200%20005.943%203.638l.6%201.375a5.75%205.75%200%2001-8.046-5.013H5.023L7.02%209.004l1.997%203.004h-1.26z%27%20fill%3D%27%23000%27%20fill-rule%3D%27nonzero%27%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E") !important;
  width: 24px !important;
  height: 24px !important;
  background-size: 100% 100% !important;
  display: block !important;
  margin-left: -11px !important;
  background-color: transparent !important;
  cursor: pointer !important;
}

.designcombo-scene-moveable .moveable-rotation .moveable-rotation-line {
  display: none !important;
}

.moveable-line {
  pointer-events: none !important;
  height: 1px !important;
  background: transparent !important;
}

/* CSS */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.ScrollAreaRootH {
  border-radius: 4px;
  overflow: hidden;
  position: absolute;
  bottom: 4px;
  left: 0;
  background: transparent;
}

.ScrollAreaRootV {
  border-radius: 4px;
  overflow: hidden;
  position: absolute;
  top: 0;
  right: 4px;
  background: transparent;
}

.ScrollAreaViewport {
  width: 100%;
  height: 100%;
  border-radius: inherit;
  opacity: 0;
}

.ScrollAreaScrollbar {
  display: flex;
  /* ensures no selection */
  user-select: none;
  /* disable browser handling of all panning and zooming gestures on touch devices */
  touch-action: none;
  padding: 2px;
  background: transparent;
  transition: background 160ms ease-out;
}

.ScrollAreaScrollbar:hover {
  background: var(--zinc-200);
}
.ScrollAreaScrollbar[data-orientation="vertical"] {
  width: 10px;
}
.ScrollAreaScrollbar[data-orientation="horizontal"] {
  flex-direction: column;
  height: 10px;
}

.ScrollAreaThumb {
  flex: 1;
  background: var(--zinc-400);
  border-radius: 10px;
  position: relative;
}
/* increase target size for touch devices https://www.w3.org/WAI/WCAG21/Understanding/target-size.html */
.ScrollAreaThumb::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  min-width: 44px;
  min-height: 44px;
}

/* CROP STYLES */

.crop {
  position: relative;
  font-size: 0;
}

.options {
  margin-bottom: 0.25rem;
}

.videoPreview {
  /* box-shadow: 0 0 20px 5px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  width: 100%; */
}

.box {
  border: 1px solid #64748b;
  position: absolute;
  inset: 0;
  touch-action: none;
}

.box svg {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  cursor: move;
  touch-action: manipulation;
}

.box svg line {
  stroke: #64748b;
  stroke-width: 1px;
}

.dimensions {
  position: absolute;
  top: -2rem;
  right: 0;
  z-index: 9999;
  color: white;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.25rem;
}

.handles > div {
  position: absolute;
  width: 1.5rem;
  height: 1.5rem;
  border-color: #64748b;
  touch-action: manipulation;
  border-radius: 3px;
}

/* nw n ne */
.handle-nw,
.handle-n,
.handle-ne {
  top: -3px;
  border-top-style: solid;
}

.handle-n {
  border-top: 3px solid;
}
/* nw w sw */
.handle-nw,
.handle-w,
.handle-sw {
  left: -3px;
  border-left: 3px solid;
}
.handle-nw {
  border-top: 3px solid;
}

/* ne e se */
.handle-ne,
.handle-e,
.handle-se {
  right: -3px;
  border-right: 3px solid;
}

.handle-ne {
  border-top: 3px solid;
}
.handle-se {
  border-bottom: 3px solid;
}

.handle-e {
  border-top: none;
  border-bottom: none;
  border-left: none;
}

/* sw s se */
.handle-sw,
.handle-s,
.handle-se {
  bottom: -3px;
  border-bottom: 3px solid;
}
.handle-sw {
  border-left: 3px solid;
}

.handle-n,
.handle-s {
  left: 50%;
  transform: translate(-50%, 0);
}

.handle-e,
.handle-w {
  top: 50%;
  transform: translate(0, -50%);
}
