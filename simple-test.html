<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Mouse Tracker Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-area {
            background: rgba(255, 255, 255, 0.2);
            padding: 40px;
            margin: 20px 0;
            text-align: center;
            border-radius: 10px;
            border: 2px dashed rgba(255, 255, 255, 0.5);
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
        
        .status {
            text-align: center;
            font-size: 18px;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖱️ Simple Mouse Tracker Test</h1>
        
        <div class="instructions">
            <h3>How to Test:</h3>
            <ol>
                <li>Keep this tab open</li>
                <li>Click the Mouse Tracker extension icon</li>
                <li>In the control page, make sure "Current Tab" is selected</li>
                <li>Enable "Enable mouse tracking on selected tab"</li>
                <li>Click "Test Mouse Tracking" or "Start Recording"</li>
                <li>Come back to this tab and move your mouse around</li>
                <li>Click some buttons below</li>
                <li>Go back to the control page to see results</li>
            </ol>
        </div>
        
        <div class="test-area">
            <h2>Move your mouse in this area</h2>
            <p>The extension should track all mouse movements and clicks</p>
            
            <div>
                <button class="button" onclick="buttonClicked(1)">Button 1</button>
                <button class="button" onclick="buttonClicked(2)">Button 2</button>
                <button class="button" onclick="buttonClicked(3)">Button 3</button>
            </div>
        </div>
        
        <div class="status" id="status">
            Ready for testing - Click count: <span id="clickCount">0</span>
        </div>
    </div>

    <script>
        let clickCount = 0;
        
        function buttonClicked(buttonNum) {
            clickCount++;
            document.getElementById('clickCount').textContent = clickCount;
            
            const status = document.getElementById('status');
            status.textContent = `Button ${buttonNum} clicked! Total clicks: ${clickCount}`;
            
            setTimeout(() => {
                status.innerHTML = `Ready for testing - Click count: <span id="clickCount">${clickCount}</span>`;
            }, 2000);
        }
        
        // Add some visual feedback for mouse movements
        document.addEventListener('mousemove', function(e) {
            // Create a small visual indicator
            const indicator = document.createElement('div');
            indicator.style.position = 'fixed';
            indicator.style.left = e.clientX + 'px';
            indicator.style.top = e.clientY + 'px';
            indicator.style.width = '3px';
            indicator.style.height = '3px';
            indicator.style.background = 'rgba(255, 255, 255, 0.8)';
            indicator.style.borderRadius = '50%';
            indicator.style.pointerEvents = 'none';
            indicator.style.zIndex = '9999';
            document.body.appendChild(indicator);
            
            setTimeout(() => {
                indicator.remove();
            }, 500);
        });
        
        console.log('Simple test page loaded and ready for mouse tracking');
    </script>
</body>
</html>
