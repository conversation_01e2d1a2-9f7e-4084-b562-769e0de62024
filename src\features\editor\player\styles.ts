import { IImage, ITrackItem } from "@designcombo/types";

export const calculateCropStyles = (
  details: IImage["details"],
  crop: IImage["details"]["crop"],
) => ({
  width: details.width || "100%",
  height: details.height || "auto",
  top: -crop.y || 0,
  left: -crop.x || 0,
  position: "absolute",
  borderRadius: `${Math.min(crop.width, crop.height) * ((details.borderRadius || 0) / 100)}px`,
});

// Cache for media style calculations
const mediaStyleCache = new Map<string, React.CSSProperties>();

export const calculateMediaStyles = (
  details: ITrackItem["details"],
  crop: ITrackItem["details"]["crop"],
) => {
  // Create a cache key based on the input parameters
  const cacheKey = JSON.stringify({ details, crop });

  // Check if we have a cached result
  if (mediaStyleCache.has(cacheKey)) {
    return mediaStyleCache.get(cacheKey)!;
  }

  const styles = {
    pointerEvents: "none",
    boxShadow: [
      `0 0 0 ${details.borderWidth}px ${details.borderColor}`,
      details.boxShadow
        ? `${details.boxShadow.x}px ${details.boxShadow.y}px ${details.boxShadow.blur}px ${details.boxShadow.color}`
        : "",
    ]
      .filter(Boolean)
      .join(", "),
    ...calculateCropStyles(details, crop),
  } as React.CSSProperties;

  // Cache the result (limit cache size to prevent memory leaks)
  if (mediaStyleCache.size > 100) {
    const firstKey = mediaStyleCache.keys().next().value;
    mediaStyleCache.delete(firstKey);
  }
  mediaStyleCache.set(cacheKey, styles);

  return styles;
};


// Cache for style calculations to avoid repeated computations
const styleCache = new Map<string, React.CSSProperties>();

export const calculateContainerStyles = (
  details: ITrackItem["details"],
  crop: ITrackItem["details"]["crop"] = {},
  overrides: React.CSSProperties = {},
): React.CSSProperties => {
  // Create a cache key based on the input parameters
  const cacheKey = JSON.stringify({ details, crop, overrides });

  // Check if we have a cached result
  if (styleCache.has(cacheKey)) {
    return styleCache.get(cacheKey)!;
  }

  const styles: React.CSSProperties = {
    pointerEvents: "auto" as const,
    top: details.top || 0,
    left: details.left || 0,
    width:  details.width || "100%",
    height:  details.height || "auto",
    transform: details.transform || "none",
    opacity: details.opacity !== undefined ? details.opacity / 100 : 1,
    transformOrigin: details.transformOrigin || "center center",
    filter: `brightness(${details.brightness}%) blur(${details.blur}px)`,
    rotate: details.rotate || "0deg",
    ...overrides, // Merge overrides into the calculated styles
  };

  // Cache the result (limit cache size to prevent memory leaks)
  if (styleCache.size > 100) {
    const firstKey = styleCache.keys().next().value;
    styleCache.delete(firstKey);
  }
  styleCache.set(cacheKey, styles);

  return styles;
};
