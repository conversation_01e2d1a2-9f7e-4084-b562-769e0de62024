import React from 'react';
import { useCurrentTimelineState } from '../utils/export-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export const TimelineDebug: React.FC = () => {
  const timelineState = useCurrentTimelineState();

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Timeline Data Bridge Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium">Timeline Items:</label>
            <Badge variant="secondary">{timelineState.trackItemIds.length}</Badge>
          </div>
          <div>
            <label className="text-sm font-medium">Duration:</label>
            <Badge variant="secondary">{timelineState.duration}ms</Badge>
          </div>
          <div>
            <label className="text-sm font-medium">FPS:</label>
            <Badge variant="secondary">{timelineState.fps}</Badge>
          </div>
          <div>
            <label className="text-sm font-medium">Dimensions:</label>
            <Badge variant="secondary">{timelineState.width}x{timelineState.height}</Badge>
          </div>
        </div>
        
        {timelineState.trackItemIds.length > 0 && (
          <div>
            <label className="text-sm font-medium">Track Items:</label>
            <div className="mt-2 space-y-2">
              {timelineState.trackItemIds.map((id) => {
                const item = timelineState.trackItemsMap[id];
                return (
                  <div key={id} className="flex items-center gap-2 p-2 bg-muted rounded">
                    <Badge variant="outline">{item?.type || 'unknown'}</Badge>
                    <span className="text-sm">{id}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        
        <div>
          <label className="text-sm font-medium">Canvas Background:</label>
          <Badge variant="secondary">{timelineState.canvasSettings.background.type}</Badge>
        </div>
        
        <div className="text-xs text-muted-foreground">
          ✅ Timeline data bridge is working - this data will be passed to the Remotion composition for export
        </div>
      </CardContent>
    </Card>
  );
};
