# Video Export Setup Guide

This guide will help you set up the video export functionality for the React Video Editor.

## 🚀 Quick Start

### 1. Install Render Server Dependencies

```bash
# Install dependencies for the render server
npm run render-server:install
```

### 2. Start the Render Server

**Option A: Development Mode (Recommended for testing)**
```bash
npm run render-server:dev
```

**Option B: Production Mode**
```bash
npm run render-server
```

The render server will start on `http://localhost:3001`

### 3. Start the Main Application

In a separate terminal:
```bash
npm run dev
```

The main application will be available at `http://localhost:5173`

### 4. Test Video Export

1. Open the application in your browser
2. Add some video clips to the timeline
3. Click the **Export** button (download icon) in the timeline header
4. Configure your export settings
5. Click **Start Export**
6. Monitor the progress and download when complete

## 🔧 Architecture Overview

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   React App     │ ──────────────► │  Render Server  │
│   (Frontend)    │                 │   (Express.js)  │
│                 │                 │                 │
│ - Timeline UI   │                 │ - Remotion      │
│ - Export Dialog │                 │ - Video Render  │
│ - Progress      │                 │ - File Storage  │
└─────────────────┘                 └─────────────────┘
```

### Components

1. **Frontend (React App)**
   - Timeline editing interface
   - Export dialog with settings
   - Progress monitoring
   - Download functionality

2. **Render Server (Express.js)**
   - Remotion bundling and rendering
   - Job queue management
   - File storage and serving
   - Progress tracking

3. **API Communication**
   - RESTful endpoints
   - Real-time progress updates
   - File download handling

## 📁 File Structure

```
react-video-editor/
├── src/
│   ├── services/
│   │   └── render-api.ts              # API client for render server
│   ├── features/editor/
│   │   ├── hooks/
│   │   │   └── use-video-export.ts    # Export logic hook
│   │   └── components/
│   │       ├── export-dialog.tsx      # Export settings UI
│   │       └── export-button.tsx      # Export trigger button
│   └── remotion/
│       ├── Root.tsx                   # Remotion composition root
│       └── VideoEditorComposition.tsx # Main video composition
├── remotion-render-server/
│   ├── server.js                      # Express server
│   ├── package.json                   # Server dependencies
│   └── renders/                       # Output directory
└── package.json                       # Main project dependencies
```

## 🎛️ Export Settings

### Resolution Options
- **720p**: 1280×720 (HD)
- **1080p**: 1920×1080 (Full HD) - Default
- **1440p**: 2560×1440 (2K)
- **4K**: 3840×2160 (Ultra HD)

### Quality Settings
- **Low**: Fast rendering, smaller file size
- **Medium**: Balanced quality and speed
- **High**: Better quality, slower rendering - Default
- **Ultra**: Best quality, slowest rendering

### Codec Options
- **H.264**: Most compatible, recommended for web
- **H.265**: Better compression, newer devices
- **VP8**: Open source, web-friendly
- **VP9**: Better compression than VP8

### Frame Rate
- **24 FPS**: Cinematic
- **30 FPS**: Standard video - Default
- **60 FPS**: Smooth motion

## 🔍 Troubleshooting

### Common Issues

#### 1. "Render server offline"
**Problem**: The export button shows "Server Offline"
**Solution**: 
- Make sure the render server is running: `npm run render-server:dev`
- Check if port 3001 is available
- Verify no firewall is blocking the connection

#### 2. "Export failed" or render errors
**Problem**: Export starts but fails during rendering
**Solutions**:
- Check the render server console for detailed error messages
- Ensure all video files are accessible (not moved/deleted)
- Try reducing video resolution or quality
- Check available disk space

#### 3. Slow rendering
**Problem**: Export takes a very long time
**Solutions**:
- Reduce video resolution (try 720p instead of 4K)
- Lower quality setting (try "Medium" instead of "Ultra")
- Reduce video duration
- Close other applications to free up CPU/memory

#### 4. Download fails
**Problem**: Export completes but download doesn't work
**Solutions**:
- Check browser download settings
- Ensure popup blocker isn't interfering
- Try right-clicking the download button and "Save link as"

### Debug Mode

To enable detailed logging:

1. **Frontend**: Open browser developer tools (F12) and check the Console tab
2. **Backend**: The render server logs detailed information to the terminal

### Port Configuration

If port 3001 is in use, you can change it:

```bash
# Set custom port for render server
PORT=3002 npm run render-server:dev
```

Then update the API base URL in `src/services/render-api.ts`:
```typescript
constructor(baseUrl: string = 'http://localhost:3002') {
```

## 🚀 Production Deployment

For production deployment:

1. **Build the main application**:
   ```bash
   npm run build
   ```

2. **Deploy the render server** to a cloud service (AWS, Google Cloud, etc.)

3. **Update API endpoint** in the frontend to point to your production server

4. **Configure environment variables** for the render server:
   - `PORT`: Server port
   - Any cloud storage credentials if using external storage

## 📊 Performance Tips

1. **Hardware Requirements**:
   - CPU: Multi-core processor recommended
   - RAM: 8GB+ for 1080p, 16GB+ for 4K
   - Storage: SSD recommended for faster I/O

2. **Optimization**:
   - Use H.264 codec for best compatibility
   - Start with 1080p resolution for testing
   - Use "High" quality setting as a good balance
   - Monitor system resources during rendering

3. **Scaling**:
   - For high-volume usage, consider implementing a job queue
   - Use cloud rendering services for better scalability
   - Implement caching for frequently rendered content

## 🆘 Getting Help

If you encounter issues:

1. Check the console logs (both frontend and backend)
2. Verify all dependencies are installed correctly
3. Ensure both servers are running on the correct ports
4. Test with a simple video first (single short clip)
5. Check the GitHub issues for similar problems

## 🎯 Next Steps

Once basic export is working:

1. **Add more export formats** (WebM, MOV, etc.)
2. **Implement batch export** for multiple compositions
3. **Add cloud storage integration** (AWS S3, Google Cloud Storage)
4. **Create export presets** for common use cases
5. **Add email notifications** for completed exports
6. **Implement export queue** for multiple simultaneous renders
