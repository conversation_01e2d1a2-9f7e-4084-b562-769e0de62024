import { useState, useCallback, useRef, useEffect } from 'react';
import { renderApi, RenderRequest, RenderStatus, createRenderRequest } from '../../../services/render-api';
import useStore from '../store/use-store';
import { useCanvasStore } from '../store/use-canvas-store';
import { useZoomStore } from '../store/use-zoom-store';

export interface ExportSettings {
  format: 'mp4' | 'webm' | 'mov';
  quality: 'low' | 'medium' | 'high' | 'ultra';
  fps: 24 | 30 | 60;
  codec: 'h264' | 'h265' | 'vp8' | 'vp9';
}

export interface ExportProgress {
  renderId: string;
  status: RenderStatus['status'];
  progress: number;
  timeElapsed: number;
  estimatedTimeRemaining?: number;
  error?: string;
}

export interface UseVideoExportReturn {
  // State
  isExporting: boolean;
  exportProgress: ExportProgress | null;
  exportHistory: ExportProgress[];
  serverStatus: 'checking' | 'online' | 'offline';
  
  // Actions
  startExport: (settings?: Partial<ExportSettings>) => Promise<void>;
  cancelExport: () => void;
  downloadVideo: (renderId: string, filename?: string) => Promise<void>;
  clearHistory: () => void;
  checkServerStatus: () => Promise<boolean>;
  
  // Settings
  exportSettings: ExportSettings;
  updateExportSettings: (settings: Partial<ExportSettings>) => void;
}

const DEFAULT_EXPORT_SETTINGS: ExportSettings = {
  format: 'mp4',
  quality: 'high', // Changed from 'maximum' to 'high' for better performance
  fps: 30,
  codec: 'h264',
};

const QUALITY_SETTINGS = {
  low: { quality: 50, bitrate: 1000 },
  medium: { quality: 65, bitrate: 2500 },
  high: { quality: 80, bitrate: 5000 },
  ultra: { quality: 95, bitrate: 10000 },
};

// Removed RESOLUTION_SETTINGS - now using canvas dimensions

export function useVideoExport(): UseVideoExportReturn {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null);
  const [exportHistory, setExportHistory] = useState<ExportProgress[]>([]);
  const [serverStatus, setServerStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [exportSettings, setExportSettings] = useState<ExportSettings>(DEFAULT_EXPORT_SETTINGS);
  
  const cancelRef = useRef<boolean>(false);
  const startTimeRef = useRef<number>(0);
  
  // Get editor state
  const {
    trackItemIds,
    trackItemsMap,
    trackItemDetailsMap,
    transitionsMap,
    duration,
    size, // Canvas dimensions are stored here
    zoomTiming, // Add zoom timing to export
  } = useStore();

  const { settings: canvasSettings } = useCanvasStore();
  const { config: zoomConfig } = useZoomStore(); // Get current zoom configuration

  // Check server status
  const checkServerStatus = useCallback(async (): Promise<boolean> => {
    setServerStatus('checking');
    try {
      const isAvailable = await renderApi.isServerAvailable();
      setServerStatus(isAvailable ? 'online' : 'offline');
      return isAvailable;
    } catch {
      setServerStatus('offline');
      return false;
    }
  }, []);

  // Update export settings
  const updateExportSettings = useCallback((settings: Partial<ExportSettings>) => {
    setExportSettings(prev => ({ ...prev, ...settings }));
  }, []);

  // Start export process
  const startExport = useCallback(async (settingsOverride?: Partial<ExportSettings>) => {
    if (isExporting) {
      console.warn('Export already in progress');
      return;
    }

    // Check server availability
    const isServerOnline = await checkServerStatus();
    if (!isServerOnline) {
      throw new Error('Render server is not available. Please start the render server first.');
    }

    // Merge settings
    const finalSettings = { ...exportSettings, ...settingsOverride };
    const quality = QUALITY_SETTINGS[finalSettings.quality];

    // Get actual canvas dimensions from main store
    const canvasWidth = size.width;
    const canvasHeight = size.height;

    // Debug logging for export dimensions
    console.log('🎬 VIDEO EXPORT DEBUG:');
    console.log(`📐 Canvas dimensions from store: ${canvasWidth}x${canvasHeight}`);
    console.log(`🎨 Canvas settings:`, canvasSettings);
    console.log(`📊 Padding: ${canvasSettings.padding.value}${canvasSettings.padding.unit}`);
    console.log(`🖼️ Background type: ${canvasSettings.background.type}`);

    // Calculate padding value for debugging
    const paddingValue = canvasSettings.padding.unit === "%"
      ? Math.min(size.width, size.height) * (Math.max(0, Math.min(50, canvasSettings.padding.value)) / 100)
      : Math.max(0, Math.min(200, canvasSettings.padding.value));
    console.log(`📏 Calculated padding value: ${paddingValue}px`);

    console.log(`🚀 Sending to render server: ${canvasWidth}x${canvasHeight}`);

    // Create render request with performance settings
    const renderRequest: RenderRequest = createRenderRequest(
      trackItemIds,
      trackItemsMap,
      trackItemDetailsMap,
      transitionsMap,
      canvasSettings,
      duration,
      finalSettings.fps,
      canvasWidth,
      canvasHeight,
      {
        codec: finalSettings.codec,
        imageFormat: 'jpeg', // Use JPEG for much faster rendering (PNG is 3-5x slower)
        quality: quality.quality,
      }
    );

    setIsExporting(true);
    cancelRef.current = false;
    startTimeRef.current = Date.now();

    try {
      // Show initial progress for blob processing
      setExportProgress({
        renderId: 'preparing',
        status: 'preparing',
        progress: 5,
        timeElapsed: Date.now() - startTimeRef.current,
      });

      // Start render and poll for progress
      await renderApi.renderAndWait(renderRequest, (status) => {
        if (cancelRef.current) {
          return;
        }

        const timeElapsed = Date.now() - startTimeRef.current;
        const estimatedTimeRemaining = status.progress > 0
          ? (timeElapsed / status.progress) * (100 - status.progress)
          : undefined;

        const progress: ExportProgress = {
          renderId: status.renderId,
          status: status.status,
          progress: status.progress,
          timeElapsed,
          estimatedTimeRemaining,
          error: status.error,
        };

        setExportProgress(progress);
      });

      // Export completed successfully
      if (!cancelRef.current && exportProgress) {
        const finalProgress: ExportProgress = {
          ...exportProgress,
          status: 'completed',
          progress: 100,
          timeElapsed: Date.now() - startTimeRef.current,
        };
        
        setExportProgress(finalProgress);
        setExportHistory(prev => [finalProgress, ...prev.slice(0, 9)]); // Keep last 10
      }

    } catch (error) {
      if (!cancelRef.current) {
        const errorProgress: ExportProgress = {
          renderId: exportProgress?.renderId || 'unknown',
          status: 'error',
          progress: exportProgress?.progress || 0,
          timeElapsed: Date.now() - startTimeRef.current,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
        
        setExportProgress(errorProgress);
        setExportHistory(prev => [errorProgress, ...prev.slice(0, 9)]);
      }
      
      throw error;
    } finally {
      setIsExporting(false);
    }
  }, [
    isExporting,
    exportSettings,
    trackItemIds,
    trackItemsMap,
    trackItemDetailsMap,
    transitionsMap,
    canvasSettings,
    duration,
    zoomTiming,
    exportProgress,
    checkServerStatus,
  ]);

  // Cancel export
  const cancelExport = useCallback(() => {
    if (isExporting) {
      cancelRef.current = true;
      setIsExporting(false);
      setExportProgress(null);
    }
  }, [isExporting]);

  // Download video
  const downloadVideo = useCallback(async (renderId: string, filename?: string) => {
    try {
      await renderApi.downloadVideo(renderId, filename);
    } catch (error) {
      console.error('Error downloading video:', error);
      throw error;
    }
  }, []);

  // Clear export history
  const clearHistory = useCallback(() => {
    setExportHistory([]);
  }, []);

  // Check server status on mount
  useEffect(() => {
    checkServerStatus();
  }, [checkServerStatus]);

  return {
    // State
    isExporting,
    exportProgress,
    exportHistory,
    serverStatus,
    
    // Actions
    startExport,
    cancelExport,
    downloadVideo,
    clearHistory,
    checkServerStatus,
    
    // Settings
    exportSettings,
    updateExportSettings,
  };
}
