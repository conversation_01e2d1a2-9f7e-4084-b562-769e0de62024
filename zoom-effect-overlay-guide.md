# Zoom Effect Overlay Guide

## Overview

The Zoom Effect Overlay provides a professional video editing interface for editing zoom effects with dual playheads - one for the start time and one for the end time of the zoom effect. This interface follows your preferences for timeline interfaces with visual effect bars, draggable handles, and real-time feedback.

## Features

### 🎯 Dual Playheads
- **Blue Playhead**: Controls the zoom start time
- **Green Playhead**: Controls the zoom end time
- Both playheads are draggable with real-time feedback

### 🎨 Visual Effect Bar
- **Blue Effect Bar**: Shows the duration and position of the zoom effect
- **Draggable Bar**: Click and drag the entire bar to reposition the effect
- **Visual Feedback**: Professional video editing software-style interactions

### 🎮 Interactive Controls
- **Draggable Handles**: Left and right edges for start/end time adjustment
- **Play Buttons**: Quick preview buttons on each playhead to seek to that time
- **Time Display**: Real-time time display above each playhead
- **Effect Duration**: Shows total effect duration in the center of the bar

## How to Use

### 1. Always Visible Interface
- The zoom effect overlay is **always visible** on the timeline
- No need to toggle or open - it's integrated directly into the timeline interface

### 2. Editing Zoom Timing

#### Adjusting Start Time (Blue Playhead)
- Drag the blue playhead left or right to adjust the zoom start time
- Click the play button below the playhead to preview the start position
- Time is displayed above the playhead in real-time

#### Adjusting End Time (Green Playhead)
- Drag the green playhead left or right to adjust the zoom end time
- Click the play button below the playhead to preview the end position
- Time is displayed above the playhead in real-time

#### Moving the Entire Effect
- Click and drag the blue effect bar to move the entire zoom effect
- The duration remains constant while both start and end times move together

### 3. Visual Feedback
- **Real-time Updates**: All changes are reflected immediately in the store
- **Constraint Handling**: Prevents invalid configurations (e.g., start time after end time)
- **Minimum Duration**: Ensures a minimum 100ms duration between start and end
- **Boundary Limits**: Keeps timing within the video duration bounds

### 4. Always Available
- The overlay is always visible and integrated into the timeline
- No need to close or hide - it's part of the timeline interface

## Technical Implementation

### Files Modified/Created

1. **`src/features/editor/timeline/zoom-effect-overlay.tsx`** - Main overlay component
2. **`src/features/editor/timeline/items/zoom-effect.ts`** - Zoom effect timeline item class
3. **`src/features/editor/store/use-store.ts`** - Uses existing zoom timing state
4. **`src/features/editor/timeline/header.tsx`** - Existing zoom timing controls
5. **`src/features/editor/timeline/timeline.tsx`** - Integrated overlay component

### State Management

The overlay uses the existing zoom timing state from the main store:
- `zoomTiming.startTime` - Start time in milliseconds
- `zoomTiming.endTime` - End time in milliseconds
- Always visible - no toggle state needed

### Integration with Existing System

The overlay integrates seamlessly with:
- **Existing zoom timing controls** in the timeline header
- **Zoom configuration system** from `use-zoom-store.ts`
- **Timeline scaling and scrolling** for proper positioning
- **Player seeking** for preview functionality

## User Experience

### Professional Video Editing Style
- **Visual Effect Bars**: Similar to Adobe Premiere Pro or DaVinci Resolve
- **Draggable Handles**: Industry-standard interaction patterns
- **Real-time Feedback**: Immediate visual and numerical feedback
- **Keyboard Shortcuts**: ESC to close, following standard conventions

### Accessibility
- **Clear Visual Distinction**: Blue for start, green for end
- **Tooltips and Labels**: Descriptive text for all controls
- **Keyboard Support**: Ready for future keyboard shortcuts
- **Responsive Design**: Works with different timeline zoom levels

## Future Enhancements

Potential improvements that could be added:
1. **Snap to Grid**: Snap playheads to timeline grid marks
2. **Keyboard Shortcuts**: Arrow keys for fine adjustment
3. **Multiple Zoom Effects**: Support for multiple zoom effects per clip
4. **Preset Durations**: Quick buttons for common durations (1s, 2s, 3s)
5. **Waveform Integration**: Visual alignment with audio waveforms
6. **Undo/Redo**: Integration with the timeline's undo system

## Troubleshooting

### Common Issues
1. **Overlay not appearing**: The overlay is always visible - check timeline is loaded
2. **Playheads not dragging**: Ensure mouse events are not being blocked
3. **Times not updating**: Verify store connection and state updates
4. **Visual positioning issues**: Check timeline scroll position and zoom level

### Debug Information
The overlay logs debug information to the console during development, including:
- Drag start/end events
- Time calculations
- Constraint violations
- State updates

This overlay provides a professional, intuitive interface for editing zoom effects that matches your preferences for timeline interfaces with visual effect bars and draggable handles.
