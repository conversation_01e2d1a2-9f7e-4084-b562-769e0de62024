import { create } from "zustand";

export type BackgroundType = "solid" | "gradient" | "image";

export interface GradientStop {
  color: string;
  position: number; // 0-100
}

export interface GradientConfig {
  type: "linear" | "radial";
  angle: number; // 0-360 for linear gradients
  stops: GradientStop[];
}

export interface CanvasBackground {
  type: BackgroundType;
  solidColor: string;
  gradient: GradientConfig;
  imageUrl: string | null;
  imageFile: File | null;
  imageObjectUrl: string | null;
}

export interface CanvasPadding {
  value: number; // 0-200px or 0-50% of video dimensions
  unit: "px" | "%";
}

export interface CanvasBlur {
  enabled: boolean;
  intensity: number; // 0-20px blur radius
}

export interface VideoBorderRadius {
  value: number; // 0-50px border radius
}



export interface VideoBackgroundShadow {
  enabled: boolean;
  x: number; // -50 to 50px horizontal offset
  y: number; // -50 to 50px vertical offset
  blur: number; // 0-50px blur radius
  spread: number; // 0-20px spread radius
  color: string; // hex color with alpha
}

export interface CanvasSettings {
  background: CanvasBackground;
  padding: CanvasPadding;
  blur: CanvasBlur;
  videoBorderRadius: VideoBorderRadius;
  videoBackgroundShadow: VideoBackgroundShadow;
}

interface CanvasStore {
  settings: CanvasSettings;

  // Actions
  setBackgroundType: (type: BackgroundType) => void;
  setSolidColor: (color: string) => void;
  setGradient: (gradient: GradientConfig) => void;
  setBackgroundImage: (file: File | null, objectUrl: string | null) => void;
  setPadding: (value: number, unit?: "px" | "%") => void;
  setBlurEnabled: (enabled: boolean) => void;
  setBlurIntensity: (intensity: number) => void;
  setVideoBorderRadius: (value: number) => void;



  // Video Background Shadow Actions
  setVideoBackgroundShadowEnabled: (enabled: boolean) => void;
  setVideoBackgroundShadowX: (x: number) => void;
  setVideoBackgroundShadowY: (y: number) => void;
  setVideoBackgroundShadowBlur: (blur: number) => void;
  setVideoBackgroundShadowSpread: (spread: number) => void;
  setVideoBackgroundShadowColor: (color: string) => void;

  resetSettings: () => void;
}

// Default gradient presets with diverse color variations
export const DEFAULT_GRADIENTS: GradientConfig[] = [
  // Vibrant Red to Coral
  {
    type: "linear",
    angle: 45,
    stops: [
      { color: "#ff0844", position: 0 },
      { color: "#ffb199", position: 100 }
    ]
  },
  // Bright Blue Gradient
  {
    type: "linear",
    angle: 135,
    stops: [
      { color: "#4facfe", position: 0 },
      { color: "#00f2fe", position: 100 }
    ]
  },
  // Pink to Yellow Radial
  {
    type: "radial",
    angle: 0,
    stops: [
      { color: "#fa709a", position: 0 },
      { color: "#fee140", position: 100 }
    ]
  },
  // Dark Dramatic
  {
    type: "linear",
    angle: 90,
    stops: [
      { color: "#0c0c0c", position: 0 },
      { color: "#434343", position: 100 }
    ]
  },
  // Nature Green
  {
    type: "linear",
    angle: 135,
    stops: [
      { color: "#134e5e", position: 0 },
      { color: "#71b280", position: 100 }
    ]
  },
  // Sunset Orange
  {
    type: "linear",
    angle: 180,
    stops: [
      { color: "#ff7e5f", position: 0 },
      { color: "#feb47b", position: 100 }
    ]
  },
  // Ocean Blue Purple
  {
    type: "linear",
    angle: 90,
    stops: [
      { color: "#667eea", position: 0 },
      { color: "#764ba2", position: 100 }
    ]
  },
  // Electric Purple
  {
    type: "linear",
    angle: 135,
    stops: [
      { color: "#8e2de2", position: 0 },
      { color: "#4a00e0", position: 100 }
    ]
  },
  // Warm Peach
  {
    type: "linear",
    angle: 90,
    stops: [
      { color: "#ffecd2", position: 0 },
      { color: "#fcb69f", position: 100 }
    ]
  },
  // Cool Mint
  {
    type: "linear",
    angle: 45,
    stops: [
      { color: "#84fab0", position: 0 },
      { color: "#8fd3f4", position: 100 }
    ]
  },
  // Metallic Silver
  {
    type: "linear",
    angle: 135,
    stops: [
      { color: "#bdc3c7", position: 0 },
      { color: "#2c3e50", position: 100 }
    ]
  },
  // Electric Neon
  {
    type: "radial",
    angle: 0,
    stops: [
      { color: "#00ff87", position: 0 },
      { color: "#60efff", position: 100 }
    ]
  }
];

// Default solid color presets
export const DEFAULT_COLORS = [
  "#000000", // Black
  "#ffffff", // White
  "#808080", // Medium Gray
  "#1a1a1a", // Dark Gray
  "#ff4757", // Red
  "#ff6b35", // Orange
  "#feca57", // Yellow
  "#48ca7e", // Green
  "#0abde3", // Blue
  "#667eea", // Indigo
  "#764ba2", // Purple
  "#f093fb", // Pink
  "#8b4513", // Brown
  "#2c3e50", // Dark Blue
  "#e74c3c", // Crimson
];

const defaultSettings: CanvasSettings = {
  background: {
    type: "image",
    solidColor: "#000000",
    gradient: DEFAULT_GRADIENTS[0],
    imageUrl: null,
    imageFile: null,
    imageObjectUrl: null,
  },
  padding: {
    value: 70,
    unit: "px",
  },
  blur: {
    enabled: true,
    intensity: 8,
  },
  videoBorderRadius: {
    value: 25,
  },
  videoBackgroundShadow: {
    enabled: true,
    x: 0,
    y: 4,
    blur: 25,
    spread: 12,
    color: "rgba(0, 0, 0, 0.25)",
  },
};

export const useCanvasStore = create<CanvasStore>((set, get) => ({
  settings: defaultSettings,

  setBackgroundType: (type: BackgroundType) =>
    set((state) => ({
      settings: {
        ...state.settings,
        background: { ...state.settings.background, type },
        // Keep blur settings when switching background types
      },
    })),

  setSolidColor: (color: string) =>
    set((state) => ({
      settings: {
        ...state.settings,
        background: { ...state.settings.background, solidColor: color },
      },
    })),

  setGradient: (gradient: GradientConfig) =>
    set((state) => ({
      settings: {
        ...state.settings,
        background: { ...state.settings.background, gradient },
      },
    })),

  setBackgroundImage: (file: File | null, objectUrl: string | null) =>
    set((state) => {
      // Clean up previous object URL if it exists
      if (state.settings.background.imageObjectUrl && state.settings.background.imageObjectUrl !== objectUrl) {
        try {
          URL.revokeObjectURL(state.settings.background.imageObjectUrl);
        } catch (error) {
          console.warn("Failed to revoke object URL:", error);
        }
      }

      return {
        settings: {
          ...state.settings,
          background: {
            ...state.settings.background,
            imageFile: file,
            imageObjectUrl: objectUrl,
            imageUrl: objectUrl,
          },
        },
      };
    }),

  setPadding: (value: number, unit: "px" | "%" = "px") =>
    set((state) => ({
      settings: {
        ...state.settings,
        padding: { value: Math.max(0, value), unit },
      },
    })),

  setBlurEnabled: (enabled: boolean) =>
    set((state) => ({
      settings: {
        ...state.settings,
        blur: { ...state.settings.blur, enabled },
      },
    })),

  setBlurIntensity: (intensity: number) =>
    set((state) => ({
      settings: {
        ...state.settings,
        blur: { ...state.settings.blur, intensity: Math.max(0, Math.min(20, intensity)) },
      },
    })),

  setVideoBorderRadius: (value: number) =>
    set((state) => ({
      settings: {
        ...state.settings,
        videoBorderRadius: { value: Math.max(0, Math.min(50, value)) },
      },
    })),



  // Video Background Shadow Actions
  setVideoBackgroundShadowEnabled: (enabled: boolean) =>
    set((state) => ({
      settings: {
        ...state.settings,
        videoBackgroundShadow: { ...state.settings.videoBackgroundShadow, enabled },
      },
    })),

  setVideoBackgroundShadowX: (x: number) =>
    set((state) => ({
      settings: {
        ...state.settings,
        videoBackgroundShadow: {
          ...state.settings.videoBackgroundShadow,
          x: Math.max(-50, Math.min(50, x))
        },
      },
    })),

  setVideoBackgroundShadowY: (y: number) =>
    set((state) => ({
      settings: {
        ...state.settings,
        videoBackgroundShadow: {
          ...state.settings.videoBackgroundShadow,
          y: Math.max(-50, Math.min(50, y))
        },
      },
    })),

  setVideoBackgroundShadowBlur: (blur: number) =>
    set((state) => ({
      settings: {
        ...state.settings,
        videoBackgroundShadow: {
          ...state.settings.videoBackgroundShadow,
          blur: Math.max(0, Math.min(50, blur))
        },
      },
    })),

  setVideoBackgroundShadowSpread: (spread: number) =>
    set((state) => ({
      settings: {
        ...state.settings,
        videoBackgroundShadow: {
          ...state.settings.videoBackgroundShadow,
          spread: Math.max(0, Math.min(20, spread))
        },
      },
    })),

  setVideoBackgroundShadowColor: (color: string) =>
    set((state) => ({
      settings: {
        ...state.settings,
        videoBackgroundShadow: { ...state.settings.videoBackgroundShadow, color },
      },
    })),

  resetSettings: () => set({ settings: defaultSettings }),
}));
