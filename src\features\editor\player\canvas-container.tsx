import React, { useMemo, memo, useState } from "react";
import { useCanvasStore } from "../store/use-canvas-store";
import useStore from "../store/use-store";
import { useCurrentPlayerFrameOptimized } from "../hooks/use-current-frame-optimized";
import { shallow } from "zustand/shallow";
import { useZoomStore } from "../store/use-zoom-store";
import { calculateZoomScaleFromTime } from "../utils/zoom-calculations";
import { getCachedZoomScale, zoomTransformCache, zoomPerformanceMonitor } from "../utils/zoom-performance";
import ZoomPositionOverlay from "../components/zoom-position-overlay";

interface CanvasContainerProps {
  children: React.ReactNode;
  /** Whether zoom positioning overlay is active */
  isZoomPositioningActive?: boolean;
  /** Callback when zoom positioning is closed */
  onCloseZoomPositioning?: () => void;
}

export const CanvasContainer: React.FC<CanvasContainerProps> = memo(({
  children,
  isZoomPositioningActive = false,
  onCloseZoomPositioning = () => {}
}) => {
  const { settings } = useCanvasStore();
  const { config: zoomConfig } = useZoomStore();
  const { size, trackItemsMap, playerRef, fps, zoomTiming, zoomEffects, selectedZoomEffectId } = useStore((state) => ({
    size: state.size,
    trackItemsMap: state.trackItemsMap,
    playerRef: state.playerRef,
    fps: state.fps,
    zoomTiming: state.zoomTiming,
    zoomEffects: state.zoomEffects,
    selectedZoomEffectId: state.selectedZoomEffectId,
  }), shallow);

  const currentFrame = useCurrentPlayerFrameOptimized(playerRef!);

  // Memoize transform origin to prevent unnecessary recalculations
  // Use the center of the zoom area as the transform origin
  const transformOrigin = useMemo(() => {
    const currentTime = (currentFrame / fps) * 1000;

    // If in positioning mode and there's a selected effect, use its zoom area
    if (isZoomPositioningActive && selectedZoomEffectId) {
      const selectedEffect = zoomEffects.find(effect => effect.id === selectedZoomEffectId);
      if (selectedEffect) {
        const zoomArea = selectedEffect.zoomArea || zoomConfig.zoomArea;
        const centerX = zoomArea.x + zoomArea.width / 2;
        const centerY = zoomArea.y + zoomArea.height / 2;
        return `${centerX * 100}% ${centerY * 100}%`;
      }
    }

    // Check for active zoom effects with custom zoom areas
    if (zoomEffects.length > 0) {
      const activeEffectsAtTime = zoomEffects.filter(effect =>
        currentTime >= effect.startTime && currentTime <= effect.endTime
      );

      // Use the most recently created active effect
      const activeEffect = activeEffectsAtTime.length > 0
        ? activeEffectsAtTime[activeEffectsAtTime.length - 1]
        : null;

      if (activeEffect && activeEffect.zoomArea) {
        const centerX = activeEffect.zoomArea.x + activeEffect.zoomArea.width / 2;
        const centerY = activeEffect.zoomArea.y + activeEffect.zoomArea.height / 2;
        return `${centerX * 100}% ${centerY * 100}%`;
      }
    }

    // Fallback to default zoom area
    const centerX = zoomConfig.zoomArea.x + zoomConfig.zoomArea.width / 2;
    const centerY = zoomConfig.zoomArea.y + zoomConfig.zoomArea.height / 2;
    return `${centerX * 100}% ${centerY * 100}%`;
  }, [zoomConfig.zoomArea, zoomEffects, currentFrame, fps, isZoomPositioningActive, selectedZoomEffectId]);

  // Calculate canvas dimensions with padding - optimized with better memoization
  const canvasDimensions = useMemo(() => {
    const paddingValue = settings.padding.unit === "%"
      ? Math.min(size.width, size.height) * (Math.max(0, Math.min(50, settings.padding.value)) / 100)
      : Math.max(0, Math.min(200, settings.padding.value));

    // Canvas stays the same size as the original video
    const canvasWidth = size.width;
    const canvasHeight = size.height;

    // Video gets smaller to accommodate padding
    const availableWidth = canvasWidth - (paddingValue * 2);
    const availableHeight = canvasHeight - (paddingValue * 2);

    // Calculate scale to fit video within available space while maintaining aspect ratio
    const originalAspectRatio = size.width / size.height;
    const availableAspectRatio = availableWidth / availableHeight;

    let videoWidth: number, videoHeight: number;

    if (originalAspectRatio > availableAspectRatio) {
      // Video is wider relative to available space - fit to width
      videoWidth = availableWidth;
      videoHeight = availableWidth / originalAspectRatio;
    } else {
      // Video is taller relative to available space - fit to height
      videoHeight = availableHeight;
      videoWidth = availableHeight * originalAspectRatio;
    }

    // Center the video within the canvas
    const videoLeft = (canvasWidth - videoWidth) / 2;
    const videoTop = (canvasHeight - videoHeight) / 2;

    const scale = videoWidth / size.width;

    // Remove debug logging for better performance
    // Debug logging can be enabled via environment variable if needed
    if (import.meta.env.DEV && import.meta.env.VITE_DEBUG_CANVAS) {
      console.log('🖼️ CANVAS PREVIEW DEBUG:');
      console.log(`📐 Original size: ${size.width}x${size.height}`);
      console.log(`📏 Padding value: ${paddingValue}px (${settings.padding.value}${settings.padding.unit})`);
      console.log(`📦 Canvas dimensions: ${canvasWidth}x${canvasHeight}`);
      console.log(`🎥 Video dimensions: ${videoWidth}x${videoHeight}`);
      console.log(`📍 Video position: left=${videoLeft}, top=${videoTop}`);
      console.log(`🔍 Scale factor: ${scale}`);
      console.log(`🎨 Background type: ${settings.background.type}`);
    }

    return {
      canvasWidth,
      canvasHeight,
      videoWidth,
      videoHeight,
      videoLeft,
      videoTop,
      scale,
    };
  }, [settings.padding.unit, settings.padding.value, size.width, size.height]);

  // Zoom effect logic for the entire canvas (Player context) using centralized zoom utility
  // Optimized with caching and performance monitoring
  const getCanvasZoomScale = useMemo(() => {
    // Convert current frame to time in milliseconds
    const currentTime = (currentFrame / fps) * 1000;

    // Check for active zoom effects (prioritize new multiple zoom effects over legacy single zoom)
    const activeZoomEffects = zoomEffects.length > 0 ? zoomEffects : [];

    // If we have multiple zoom effects, find the one that's active at current time
    if (activeZoomEffects.length > 0) {
      const activeEffectsAtTime = activeZoomEffects.filter(effect =>
        currentTime >= effect.startTime && currentTime <= effect.endTime
      );

      // If multiple effects are active at the same time, use the most recently created one
      // (effects are added to the end of the array, so the last one is most recent)
      const activeEffect = activeEffectsAtTime.length > 0
        ? activeEffectsAtTime[activeEffectsAtTime.length - 1]
        : null;

      if (activeEffect) {
        // Create zoom timing for this specific effect
        const effectZoomTiming = {
          startTime: activeEffect.startTime,
          endTime: activeEffect.endTime
        };

        // Create a modified zoom config that uses the effect's specific settings
        const dynamicZoomConfig = {
          ...zoomConfig,
          maxZoomScale: activeEffect.maxZoomScale || zoomConfig.calculatedZoomScale || zoomConfig.maxZoomScale,
          zoomArea: activeEffect.zoomArea || zoomConfig.zoomArea
        };

        // Use cached zoom calculation for better performance
        return getCachedZoomScale(
          currentTime,
          effectZoomTiming.startTime,
          effectZoomTiming.endTime,
          dynamicZoomConfig.maxZoomScale,
          () => {
            return zoomPerformanceMonitor.trackZoomCalculation(() => {
              const zoomResult = calculateZoomScaleFromTime(
                currentTime,
                effectZoomTiming,
                dynamicZoomConfig
              );
              return zoomResult.zoomScale;
            });
          }
        );
      }
    }

    // Fallback to legacy single zoom timing if no multiple zoom effects are active
    if (zoomTiming && (zoomTiming.startTime !== 0 || zoomTiming.endTime !== 0)) {
      // Calculate the complete zoom range including zoom-out phase
      const zoomOutDuration = zoomConfig.zoomOut.enabled ? zoomConfig.zoomOut.duration : 0;
      const zoomOutEndTime = zoomTiming.endTime + zoomOutDuration;

      // Early return if outside the complete zoom range (including zoom-out) for better performance
      if (currentTime < zoomTiming.startTime || currentTime > zoomOutEndTime) {
        return 1;
      }

      // Use cached zoom calculation for better performance
      // Use calculatedZoomScale instead of maxZoomScale for dynamic zoom based on area size
      return getCachedZoomScale(
        currentTime,
        zoomTiming.startTime,
        zoomTiming.endTime,
        zoomConfig.calculatedZoomScale,
        () => {
          return zoomPerformanceMonitor.trackZoomCalculation(() => {
            // Create a modified zoom config that uses the calculated zoom scale
            const dynamicZoomConfig = {
              ...zoomConfig,
              maxZoomScale: zoomConfig.calculatedZoomScale
            };
            const zoomResult = calculateZoomScaleFromTime(
              currentTime,
              zoomTiming,
              dynamicZoomConfig
            );
            return zoomResult.zoomScale;
          });
        }
      );
    }

    // No zoom effects active
    return 1;
  }, [currentFrame, fps, zoomTiming, zoomEffects, zoomConfig]);

  // Check if there are any visible video items at the current time
  const hasVisibleVideoContent = useMemo(() => {
    const currentTime = (currentFrame / fps) * 1000; // Convert frame to milliseconds
    return Object.values(trackItemsMap).some((item) => {
      return (
        item.type === "video" &&
        item.display.from <= currentTime &&
        item.display.to >= currentTime
      );
    });
  }, [trackItemsMap, currentFrame, fps]);

  // Generate background style
  const backgroundStyle = useMemo(() => {

    let backgroundImage = "";
    let backgroundColor = "";
    let filter = "";

    switch (settings.background.type) {
      case "solid":
        backgroundColor = settings.background.solidColor;
        break;

      case "gradient":
        const { gradient } = settings.background;
        const stops = gradient.stops
          .map(stop => `${stop.color} ${stop.position}%`)
          .join(", ");

        if (gradient.type === "linear") {
          backgroundImage = `linear-gradient(${gradient.angle}deg, ${stops})`;
        } else {
          backgroundImage = `radial-gradient(circle, ${stops})`;
        }
        break;

      case "image":
        if (settings.background.imageObjectUrl) {
          backgroundImage = `url(${settings.background.imageObjectUrl})`;
        }
        break;
    }

    // Apply blur to all background types if enabled
    if (settings.blur.enabled && settings.blur.intensity > 0) {
      filter = `blur(${settings.blur.intensity}px)`;
    }

    const style: React.CSSProperties = {
      filter,
    };

    // Set background properties based on type
    if (settings.background.type === "image") {
      style.backgroundImage = backgroundImage;
      style.backgroundSize = "100% auto";
      style.backgroundPosition = "center";
      style.backgroundRepeat = "no-repeat";
    } else if (settings.background.type === "gradient") {
      style.backgroundImage = backgroundImage;
    } else {
      style.backgroundColor = backgroundColor;
    }

    return style;
  }, [settings]);

  // Generate video background style (shadow)
  const videoBackgroundStyle = useMemo(() => {
    const { videoBackgroundShadow, videoBorderRadius } = settings;

    let boxShadow = "";

    // Apply shadow only if enabled AND there's visible video content
    if (videoBackgroundShadow.enabled && hasVisibleVideoContent) {
      boxShadow = `${videoBackgroundShadow.x}px ${videoBackgroundShadow.y}px ${videoBackgroundShadow.blur}px ${videoBackgroundShadow.spread}px ${videoBackgroundShadow.color}`;
    }

    return {
      boxShadow,
      borderRadius: `${videoBorderRadius.value}px`, // Apply the same border radius as the video
    };
  }, [settings.videoBackgroundShadow, settings.videoBorderRadius, hasVisibleVideoContent]);

  return (
    // Outer wrapper that constrains the zoom to canvas boundaries
    <div
      className="canvas-wrapper"
      style={{
        width: canvasDimensions.canvasWidth,
        height: canvasDimensions.canvasHeight,
        position: "relative",
        overflow: "hidden", // Prevent zoom from spilling outside canvas bounds
      }}
    >
      <div
        className="canvas-container"
        style={{
          width: canvasDimensions.canvasWidth,
          height: canvasDimensions.canvasHeight,
          position: "relative",
          transform: zoomTransformCache.getTransform(getCanvasZoomScale),
          transformOrigin: transformOrigin,
          // Add will-change for better performance during zoom animations
          willChange: getCanvasZoomScale !== 1 ? "transform" : "auto",
          // Use GPU acceleration for smoother transforms
          backfaceVisibility: "hidden",
          perspective: 1000,
        }}
      >
        {/* Background layer - can be blurred independently */}
        <div
          className="canvas-background"
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            ...backgroundStyle,
          }}
        />

        {/* Video container positioned within the canvas */}
        <div
          className="video-container"
          style={{
            position: "absolute",
            left: canvasDimensions.videoLeft,
            top: canvasDimensions.videoTop,
            width: canvasDimensions.videoWidth,
            height: canvasDimensions.videoHeight,
            overflow: "hidden",
            ...videoBackgroundStyle,
          }}
        >
          <div
            style={{
              width: size.width,
              height: size.height,
              transform: `scale(${canvasDimensions.scale})`,
              transformOrigin: "top left",
              borderRadius: `${settings.videoBorderRadius.value}px`,
              overflow: "hidden",
            }}
          >
            {children}
          </div>
        </div>
      </div>

      {/* Zoom Position Overlay - placed in outer wrapper to avoid transform issues */}
      <ZoomPositionOverlay
        isActive={isZoomPositioningActive}
        onClose={onCloseZoomPositioning}
        containerWidth={canvasDimensions.canvasWidth}
        containerHeight={canvasDimensions.canvasHeight}
      />
    </div>
  );
});
