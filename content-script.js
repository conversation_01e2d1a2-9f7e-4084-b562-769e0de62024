// Content script for mouse tracking
// This script is injected into web pages to track mouse movements and clicks

(function() {
    'use strict';
    
    // Prevent multiple injections
    if (window.mouseTrackerInjected) {
        console.log('Mouse tracker already injected');
        return;
    }
    
    console.log('Injecting mouse tracker content script');
    window.mouseTrackerInjected = true;
    
    let isTracking = false;
    let mouseMoveHandler = null;
    let clickHandler = null;

    // Throttle mousemove events to avoid overwhelming the background script
    let lastMouseMove = 0;
    const mouseMoveThrottle = 50; // 50ms throttle

    // Debug counters
    let totalMouseMoves = 0;
    let sentMouseMoves = 0;
    
    function sendMouseData(action, coords) {
        if (!isTracking) return;
        
        console.log('Content script sending mouse data:', action, coords);
        try {
            chrome.runtime.sendMessage({
                origin: 'content',
                content: { action, coords }
            });
        } catch (error) {
            console.error('Error sending mouse data from content script:', error);
        }
    }
    
    function startTracking() {
        if (isTracking) {
            console.log('Mouse tracking already active');
            return;
        }
        
        console.log('Starting mouse tracking in content script');
        isTracking = true;
        
        // Create throttled mousemove handler
        mouseMoveHandler = (e) => {
            totalMouseMoves++;
            const now = Date.now();
            if (now - lastMouseMove > mouseMoveThrottle) {
                sentMouseMoves++;
                console.log('🖱️ [DEBUG] Content script capturing mouse move:', e.clientX, e.clientY, `(${sentMouseMoves}/${totalMouseMoves})`);
                sendMouseData('move', { x: e.clientX, y: e.clientY });
                lastMouseMove = now;
            }
        };
        
        // Create click handler
        clickHandler = (e) => {
            console.log('🖱️ [DEBUG] Content script capturing click:', e.clientX, e.clientY);
            sendMouseData('click', { x: e.clientX, y: e.clientY });
        };
        
        // Add event listeners
        document.addEventListener('mousemove', mouseMoveHandler, { passive: true });
        document.addEventListener('click', clickHandler, { passive: true });

        console.log('🖱️ [DEBUG] Mouse tracking event listeners added to:', document.title);
        console.log('🖱️ [DEBUG] Document ready state:', document.readyState);
        console.log('🖱️ [DEBUG] Document URL:', document.URL);

        // Start periodic status reporting
        const statusInterval = setInterval(() => {
            if (!isTracking) {
                clearInterval(statusInterval);
                return;
            }
            console.log('🖱️ [DEBUG] Mouse tracking status - Total:', totalMouseMoves, 'Sent:', sentMouseMoves);
        }, 2000); // Report every 2 seconds
    }
    
    function stopTracking() {
        if (!isTracking) {
            console.log('Mouse tracking not active');
            return;
        }

        console.log('🖱️ [DEBUG] Stopping mouse tracking in content script');
        console.log('🖱️ [DEBUG] Final stats - Total moves:', totalMouseMoves, 'Sent moves:', sentMouseMoves);
        isTracking = false;
        
        // Remove event listeners
        if (mouseMoveHandler) {
            document.removeEventListener('mousemove', mouseMoveHandler);
            mouseMoveHandler = null;
        }
        
        if (clickHandler) {
            document.removeEventListener('click', clickHandler);
            clickHandler = null;
        }
        
        console.log('Mouse tracking event listeners removed');
    }
    
    // Listen for messages from the extension
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('Content script received message:', message);
        
        if (message.action === 'startMouseTracking') {
            startTracking();
            sendResponse({ success: true });
        } else if (message.action === 'stopMouseTracking') {
            stopTracking();
            sendResponse({ success: true });
        }
        
        return true; // Keep message channel open for async response
    });
    
    // Auto-start tracking if the page is already loaded and tracking was requested
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Content script DOM loaded, ready for tracking');
        });
    } else {
        console.log('Content script loaded, DOM already ready');
    }
    
    console.log('Mouse tracker content script initialized');
})();
