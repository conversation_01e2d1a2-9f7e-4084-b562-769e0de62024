// Enhanced WebM metadata detection utilities
// This provides more robust duration detection for WebM files that often report Infinity

export interface EnhancedVideoMetadata {
  width: number;
  height: number;
  duration: number;
  aspectRatio: number;
  fps?: number;
  isWebM: boolean;
  detectionMethod: 'standard' | 'enhanced' | 'fallback';
}

/**
 * Enhanced WebM duration detection using multiple strategies
 */
export const detectWebMDuration = async (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    let durationFound = false;
    let attempts = 0;
    const maxAttempts = 3;

    const cleanup = () => {
      URL.revokeObjectURL(video.src);
    };

    const tryResolve = (duration: number, method: string) => {
      if (!durationFound && isFinite(duration) && duration > 0) {
        durationFound = true;
        console.log(`WebM duration detected using ${method}: ${duration}s`);
        resolve(duration * 1000); // Convert to milliseconds
        cleanup();
        return true;
      }
      return false;
    };

    // Strategy 1: Standard loadedmetadata event
    video.onloadedmetadata = () => {
      tryResolve(video.duration, 'loadedmetadata');
    };

    // Strategy 2: Duration change event
    video.ondurationchange = () => {
      tryResolve(video.duration, 'durationchange');
    };

    // Strategy 3: Seek to end to force duration calculation
    video.onloadeddata = () => {
      if (!durationFound && attempts < maxAttempts) {
        attempts++;
        try {
          // Try seeking to a large time value
          video.currentTime = 999999;
        } catch (e) {
          console.warn('Seek attempt failed:', e);
        }
      }
    };

    // Strategy 4: Check after seek completes
    video.onseeked = () => {
      if (tryResolve(video.duration, 'seeked')) return;
      
      // If still no duration, try seeking to current time
      if (!durationFound && attempts < maxAttempts) {
        attempts++;
        try {
          video.currentTime = video.currentTime;
        } catch (e) {
          console.warn('Secondary seek attempt failed:', e);
        }
      }
    };

    // Strategy 5: Progress event (for streaming detection)
    video.onprogress = () => {
      if (tryResolve(video.duration, 'progress')) return;
    };

    // Strategy 6: Timeupdate event
    video.ontimeupdate = () => {
      if (tryResolve(video.duration, 'timeupdate')) return;
    };

    video.onerror = (e) => {
      if (!durationFound) {
        console.error('WebM video error:', e);
        reject(new Error(`WebM video loading failed: ${file.name}`));
        cleanup();
      }
    };

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!durationFound) {
        console.warn(`WebM duration detection timed out for ${file.name}`);
        reject(new Error(`WebM duration detection timeout: ${file.name}`));
        cleanup();
      }
    }, 30000);

    // Start loading
    video.preload = 'metadata';
    video.src = URL.createObjectURL(file);
    video.load();
  });
};

/**
 * Get enhanced video metadata with WebM-specific handling
 */
export const getEnhancedVideoMetadata = async (file: File): Promise<EnhancedVideoMetadata> => {
  const isWebM = file.type.includes('webm') || file.name.toLowerCase().endsWith('.webm');
  
  return new Promise(async (resolve, reject) => {
    const video = document.createElement("video");
    
    const timeout = setTimeout(() => {
      reject(new Error(`Enhanced video metadata loading timed out for: ${file.name}`));
      URL.revokeObjectURL(video.src);
    }, 35000);

    video.onloadedmetadata = async () => {
      clearTimeout(timeout);
      
      const width = video.videoWidth;
      const height = video.videoHeight;
      let duration = video.duration * 1000; // Convert to milliseconds
      const aspectRatio = width / height;
      let detectionMethod: 'standard' | 'enhanced' | 'fallback' = 'standard';

      // Enhanced WebM handling
      if (isWebM && (!isFinite(duration) || duration <= 0)) {
        try {
          URL.revokeObjectURL(video.src);
          duration = await detectWebMDuration(file);
          detectionMethod = 'enhanced';
        } catch (webmError) {
          console.warn(`Enhanced WebM detection failed for ${file.name}:`, webmError);
          // Fallback duration based on file size (rough estimate)
          const fileSizeMB = file.size / (1024 * 1024);
          duration = Math.max(10000, Math.min(fileSizeMB * 1000, 300000)); // 10s to 5min based on size
          detectionMethod = 'fallback';
        }
      }

      // Final validation
      if (!width || !height || !isFinite(duration) || duration <= 0) {
        reject(new Error(`Invalid enhanced video metadata for: ${file.name}`));
        URL.revokeObjectURL(video.src);
        return;
      }

      resolve({
        width,
        height,
        duration,
        aspectRatio,
        isWebM,
        detectionMethod,
      });

      URL.revokeObjectURL(video.src);
    };

    video.onerror = () => {
      clearTimeout(timeout);
      reject(new Error(`Enhanced video metadata loading failed for: ${file.name}`));
      URL.revokeObjectURL(video.src);
    };

    video.preload = 'metadata';
    video.src = URL.createObjectURL(file);
    video.load();
  });
};

/**
 * Validate and sanitize duration for Remotion player
 */
export const sanitizeDurationForRemotion = (duration: number, filename: string): number => {
  if (!isFinite(duration) || duration <= 0) {
    console.warn(`Sanitizing invalid duration for ${filename}: ${duration} -> 10000ms`);
    return 10000; // 10 seconds fallback
  }
  
  // Ensure duration is not too large (max 1 hour)
  const maxDuration = 3600000; // 1 hour in milliseconds
  if (duration > maxDuration) {
    console.warn(`Duration too large for ${filename}: ${duration}ms -> ${maxDuration}ms`);
    return maxDuration;
  }
  
  return Math.round(duration);
};

/**
 * Calculate safe durationInFrames for Remotion
 */
export const calculateSafeDurationInFrames = (duration: number, fps: number, filename?: string): number => {
  const sanitizedDuration = sanitizeDurationForRemotion(duration, filename || 'unknown');
  const durationInFrames = Math.round((sanitizedDuration / 1000) * fps);
  
  // Ensure minimum of 1 frame
  return Math.max(1, durationInFrames);
};
