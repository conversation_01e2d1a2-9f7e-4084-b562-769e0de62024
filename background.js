var coord_list = [];

// Handle extension icon click - open control page
chrome.action.onClicked.addListener(async () => {
  // Create or focus the control page
  const url = chrome.runtime.getURL('control.html');

  // Check if control page is already open
  const tabs = await chrome.tabs.query({ url: url });

  if (tabs.length > 0) {
    // Focus existing tab
    chrome.tabs.update(tabs[0].id, { active: true });
    chrome.windows.update(tabs[0].windowId, { focused: true });
  } else {
    // Create new tab
    chrome.tabs.create({ url: url });
  }
});

// Handle messages from content scripts and control page
chrome.runtime.onMessage.addListener(async (msg, sender, sendResponse) => {
  console.log('🔄 [DEBUG] Background received message:', {
    origin: msg.origin,
    action: msg.content?.action,
    senderTab: sender.tab?.id,
    currentCoordCount: coord_list.length
  });

  if (msg.origin === "content") {
    // Mouse tracking data from injected content script
    coord_list.push(msg.content);
    console.log('🖱️ [DEBUG] Added mouse data point, total:', coord_list.length);

    // Send acknowledgment back to content script
    sendResponse({ received: true, totalPoints: coord_list.length });
  } else if (msg.origin === "control" && msg.content.action === "stop") {
    // Control page requesting mouse data
    console.log('🛑 [DEBUG] Control page requesting mouse data, sending', coord_list.length, 'points');

    try {
      // Find the control page tab
      const controlUrl = chrome.runtime.getURL('control.html');
      console.log('🔍 [DEBUG] Looking for control page tabs with URL:', controlUrl);

      const tabs = await chrome.tabs.query({ url: controlUrl });
      console.log('🔍 [DEBUG] Found', tabs.length, 'control page tabs');

      if (tabs.length > 0) {
        console.log('📤 [DEBUG] Sending mouse data to control page tab:', tabs[0].id);

        // Send message to the control page tab with timeout handling
        const messagePromise = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            console.warn('⚠️ [DEBUG] Message send timeout - control page may not be responding');
            reject(new Error('Message timeout'));
          }, 5000);

          chrome.tabs.sendMessage(tabs[0].id, {
            origin: "background",
            content: coord_list,
          }, (response) => {
            clearTimeout(timeout);
            if (chrome.runtime.lastError) {
              console.error('❌ [DEBUG] Error sending message to control page:', chrome.runtime.lastError);
              reject(chrome.runtime.lastError);
            } else {
              console.log('✅ [DEBUG] Control page message response:', response);
              resolve(response);
            }
          });
        });

        await messagePromise.catch(error => {
          console.error('❌ [DEBUG] Failed to send message to control page:', error);
        });
      } else {
        console.warn('⚠️ [DEBUG] No control page tabs found');
      }

      const sentCount = coord_list.length;
      coord_list = [];
      console.log('✅ [DEBUG] Sent', sentCount, 'mouse points, cleared coord_list');
      sendResponse({ sent: true, pointsSent: sentCount });
    } catch (error) {
      console.error('❌ [DEBUG] Error in stop message handling:', error);
      sendResponse({ error: error.message });
    }
  } else {
    // Other mouse tracking data (fallback)
    console.log('🔄 [DEBUG] Fallback: adding mouse data:', msg.content);
    coord_list.push(msg.content);
    sendResponse({ received: true, totalPoints: coord_list.length });
  }

  return true; // Keep message channel open for async response
});

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Mouse Tracker & Screen Recorder installed');

  // Set initial storage values
  chrome.storage.sync.set({
    isRecording: false
  });
});
