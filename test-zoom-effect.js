#!/usr/bin/env node

/**
 * Test script to validate the zoom effect functionality
 * This creates a test render with a sample video and the hardcoded zoom effect
 */

import fetch from 'node-fetch';

const RENDER_SERVER_URL = 'http://localhost:3001';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testZoomEffect() {
  try {
    logInfo('Testing zoom effect with sample video...');
    
    // Create a sample video track item for testing
    const sampleVideoItem = {
      id: 'test-video-1',
      type: 'video',
      display: {
        from: 0,      // Start at beginning
        to: 5000,     // 5 seconds duration
      },
      details: {
        src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', // Sample video
        width: 1920,
        height: 1080,
        volume: 50,
        left: 0,
        top: 0,
        transform: 'scale(1, 1)',
        crop: {
          x: 0,
          y: 0,
          width: 1920,
          height: 1080,
        }
      },
      trim: {
        from: 0,
        to: 5000,
      },
      playbackRate: 1,
      animations: {
        in: null,
        out: null,
      }
    };

    const renderRequest = {
      compositionId: 'Composition',
      inputProps: {
        trackItemIds: ['test-video-1'],
        trackItemsMap: {
          'test-video-1': sampleVideoItem
        },
        trackItemDetailsMap: {
          'test-video-1': sampleVideoItem
        },
        transitionsMap: {},
        canvasSettings: {
          background: {
            type: 'solid',
            solidColor: '#000000',
            gradient: { type: 'linear', angle: 0, stops: [] },
            imageUrl: null,
            imageFile: null,
            imageObjectUrl: null,
          },
          padding: { value: 70, unit: 'px' },
          blur: { enabled: false, intensity: 0 },
          videoBorderRadius: { value: 0 },
          videoBackgroundShadow: {
            enabled: false,
            x: 0, y: 0, blur: 0, spread: 0,
            color: 'rgba(0, 0, 0, 0)',
          },
        },
        duration: 5000, // 5 seconds to see the full zoom effect
        fps: 30,
        width: 1080,
        height: 1920,
      },
      codec: 'h264',
      imageFormat: 'jpeg',
      quality: 80,
    };

    logInfo('Sending render request with zoom effect...');
    const response = await fetch(`${RENDER_SERVER_URL}/render`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(renderRequest),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.renderId) {
        logSuccess(`Zoom effect render started successfully: ${data.renderId}`);
        logInfo('The video should show:');
        logInfo('- Normal playback for the first 1 second');
        logInfo('- Smooth zoom in and out effect from 1-4 seconds');
        logInfo('- Normal playback for the last 1 second');
        logInfo(`Check the render output in: remotion-render-server/renders/`);
        return true;
      } else {
        logError(`Zoom effect render failed: ${data.error || 'Unknown error'}`);
        return false;
      }
    } else {
      logError(`Zoom effect render failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logError(`Zoom effect test failed: ${error.message}`);
    return false;
  }
}

async function checkServerHealth() {
  try {
    logInfo('Checking render server health...');
    const response = await fetch(`${RENDER_SERVER_URL}/health`);
    
    if (response.ok) {
      logSuccess('Render server is healthy');
      return true;
    } else {
      logError(`Server health check failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Cannot connect to render server: ${error.message}`);
    logError('Make sure the render server is running: npm run render-server:dev');
    return false;
  }
}

async function runZoomTest() {
  log(`${colors.bold}🔍 Zoom Effect Test${colors.reset}\n`);
  
  // Check server health first
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    process.exit(1);
  }
  
  console.log(); // Empty line
  
  // Test zoom effect
  const zoomTestPassed = await testZoomEffect();
  
  console.log(); // Empty line
  
  if (zoomTestPassed) {
    logSuccess('🎉 Zoom effect test completed successfully!');
    logInfo('The render has been started. Check the output video to verify:');
    logInfo('1. Video plays normally for 1 second');
    logInfo('2. Smooth zoom effect occurs from 1-4 seconds');
    logInfo('3. Video returns to normal for the last second');
  } else {
    logError('❌ Zoom effect test failed');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run the test
runZoomTest().catch(error => {
  logError(`Zoom test failed: ${error.message}`);
  process.exit(1);
});
