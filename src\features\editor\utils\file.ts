// Local file handling utilities for client-side video editor

export const fileToBlob = async (file: File) => {
  const blob = await new Response(file.stream()).blob();
  return blob;
};

export const blobToStream = async (blob: Blob) => {
  const file = new File([blob], "video.mp4");
  const stream = file.stream();
  return stream;
};

export const fileToStream = async (file: File) => {
  return file.stream();
};

export const createObjectURL = (file: File) => {
  return URL.createObjectURL(file);
};

export const revokeObjectURL = (url: string) => {
  URL.revokeObjectURL(url);
};

export interface VideoMetadata {
  width: number;
  height: number;
  duration: number;
  aspectRatio: number;
}

export interface AudioMetadata {
  duration: number;
  sampleRate?: number;
  numberOfChannels?: number;
}

// Enhanced WebM duration detection using multiple methods
const getWebMDuration = async (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    let durationFound = false;

    // Method 1: Try to get duration from loadedmetadata
    video.onloadedmetadata = () => {
      if (!durationFound && isFinite(video.duration) && video.duration > 0) {
        durationFound = true;
        resolve(video.duration * 1000);
        URL.revokeObjectURL(video.src);
        return;
      }
    };

    // Method 2: Try to get duration from durationchange event
    video.ondurationchange = () => {
      if (!durationFound && isFinite(video.duration) && video.duration > 0) {
        durationFound = true;
        resolve(video.duration * 1000);
        URL.revokeObjectURL(video.src);
        return;
      }
    };

    // Method 3: Try to get duration by seeking to end
    video.onloadeddata = () => {
      if (!durationFound) {
        // Try seeking to a large time value to force duration calculation
        video.currentTime = Number.MAX_SAFE_INTEGER;
      }
    };

    video.onseeked = () => {
      if (!durationFound && isFinite(video.duration) && video.duration > 0) {
        durationFound = true;
        resolve(video.duration * 1000);
        URL.revokeObjectURL(video.src);
        return;
      }
    };

    video.onerror = () => {
      if (!durationFound) {
        reject(new Error(`Could not load WebM metadata for file: ${file.name}`));
        URL.revokeObjectURL(video.src);
      }
    };

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!durationFound) {
        reject(new Error(`WebM duration detection timed out for file: ${file.name}`));
        URL.revokeObjectURL(video.src);
      }
    }, 30000);

    video.src = URL.createObjectURL(file);
    video.load();
  });
};

export const getVideoMetadata = (file: File): Promise<VideoMetadata> => {
  return new Promise(async (resolve, reject) => {
    const video = document.createElement("video");
    const isWebM = file.type.includes('webm') || file.name.toLowerCase().endsWith('.webm');

    // Add timeout for longer videos
    const timeout = setTimeout(() => {
      reject(new Error(`Video metadata loading timed out after 30 seconds for file: ${file.name}`));
      URL.revokeObjectURL(video.src);
    }, 30000); // 30 second timeout

    video.onloadedmetadata = async () => {
      clearTimeout(timeout);
      const width = video.videoWidth;
      const height = video.videoHeight;
      let duration = video.duration * 1000; // Convert to milliseconds
      const aspectRatio = width / height;

      // Special handling for WebM files with infinite duration
      if (isWebM && (!isFinite(duration) || duration <= 0)) {
        try {
          URL.revokeObjectURL(video.src);
          duration = await getWebMDuration(file);
        } catch (webmError) {
          console.warn(`WebM duration detection failed for ${file.name}, using fallback duration:`, webmError);
          // Fallback: Use a default duration of 10 seconds for WebM files
          duration = 10000; // 10 seconds in milliseconds
        }
      }

      // Validate metadata
      if (!width || !height || !isFinite(duration) || duration <= 0) {
        reject(new Error(`Invalid video metadata for file: ${file.name}. Width: ${width}, Height: ${height}, Duration: ${duration}`));
        URL.revokeObjectURL(video.src);
        return;
      }

      resolve({
        width,
        height,
        duration,
        aspectRatio,
      });

      URL.revokeObjectURL(video.src);
    };

    video.onerror = () => {
      clearTimeout(timeout);
      reject(new Error(`Could not load video metadata for file: ${file.name}`));
      URL.revokeObjectURL(video.src);
    };

    video.src = URL.createObjectURL(file);
    video.load();
  });
};

export const getAudioMetadata = (file: File): Promise<AudioMetadata> => {
  return new Promise((resolve, reject) => {
    const audio = document.createElement("audio");

    // Add timeout for audio files
    const timeout = setTimeout(() => {
      reject(new Error(`Audio metadata loading timed out after 30 seconds for file: ${file.name}`));
      URL.revokeObjectURL(audio.src);
    }, 30000);

    audio.onloadedmetadata = () => {
      clearTimeout(timeout);
      let duration = audio.duration * 1000; // Convert to milliseconds

      // Validate duration for audio files (similar to video validation)
      if (!isFinite(duration) || duration <= 0) {
        console.warn(`Invalid audio duration detected for ${file.name}: ${duration}, using fallback`);
        duration = 10000; // 10 seconds fallback
      }

      resolve({
        duration,
        // Note: sampleRate and numberOfChannels are not directly available from HTML Audio element
        // For more detailed metadata, we would need to use Web Audio API
      });

      URL.revokeObjectURL(audio.src);
    };

    audio.onerror = () => {
      clearTimeout(timeout);
      reject(new Error(`Could not load audio metadata for file: ${file.name}`));
      URL.revokeObjectURL(audio.src);
    };

    audio.src = URL.createObjectURL(file);
    audio.load();
  });
};

// Utility function to detect if a file is WebM
export const isWebMFile = (file: File): boolean => {
  return file.type.includes('webm') || file.name.toLowerCase().endsWith('.webm');
};

// Utility function to validate duration values
export const validateDuration = (duration: number, filename: string): number => {
  if (!isFinite(duration) || duration <= 0) {
    console.warn(`Invalid duration detected for ${filename}: ${duration}, using fallback`);
    return 10000; // 10 seconds fallback in milliseconds
  }
  return duration;
};

// Utility function to get a File object from a URL (for compatibility with preview example)
export const getFileFromUrl = async (url: string): Promise<File> => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch file from URL: ${response.statusText}`);
    }
    const blob = await response.blob();

    // Extract filename from URL or use a default
    const urlPath = new URL(url).pathname;
    const filename = urlPath.split('/').pop() || 'video.mp4';

    return new File([blob], filename, { type: blob.type });
  } catch (error) {
    throw new Error(`Could not create file from URL: ${url}. ${error}`);
  }
};

export const generateWaveformData = async (file: File, samples: number = 1000): Promise<number[]> => {
  return new Promise((resolve, reject) => {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer;
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // Use both channels if stereo, otherwise just the first channel
        const channelCount = audioBuffer.numberOfChannels;
        const channelData = audioBuffer.getChannelData(0);
        const channelData2 = channelCount > 1 ? audioBuffer.getChannelData(1) : null;

        const blockSize = Math.floor(channelData.length / samples);
        const waveformData: number[] = [];

        for (let i = 0; i < samples; i++) {
          const start = i * blockSize;
          const end = Math.min(start + blockSize, channelData.length);
          let sum = 0;
          let count = 0;

          // Calculate RMS (Root Mean Square) for better amplitude representation
          for (let j = start; j < end; j++) {
            const sample1 = channelData[j];
            const sample2 = channelData2 ? channelData2[j] : sample1;
            const avgSample = (sample1 + sample2) / (channelData2 ? 2 : 1);
            sum += avgSample * avgSample;
            count++;
          }

          const rms = count > 0 ? Math.sqrt(sum / count) : 0;
          waveformData.push(rms);
        }

        // Normalize to 0-1 range with better dynamic range
        const max = Math.max(...waveformData);
        const normalizedData = waveformData.map(value => {
          if (max === 0) return 0;
          // Apply slight compression to make quiet parts more visible
          const normalized = value / max;
          return Math.pow(normalized, 0.7); // Gentle compression curve
        });

        resolve(normalizedData);
      } catch (error) {
        console.error("Waveform generation error:", error);
        reject(new Error("Could not generate waveform data"));
      }
    };

    reader.onerror = () => {
      reject(new Error("Could not read audio file"));
    };

    reader.readAsArrayBuffer(file);
  });
};
