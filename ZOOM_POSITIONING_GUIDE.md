# Zoom Positioning Feature Guide

This document explains how to use the new zoom positioning feature in the React Video Editor.

## 🎯 Overview

The zoom positioning feature allows you to:
- **Select where to zoom**: Click on the video preview to set the zoom center point
- **Visual feedback**: See exactly where the zoom will focus with real-time indicators
- **Precise control**: Set exact coordinates or use the interactive overlay
- **Reset to center**: Quickly return zoom to the center position

## 🎮 How to Use

### 1. **Set Zoom Timing** (Required First)
Before positioning the zoom, you need to set when the zoom happens:
1. Click the first **Target** button (🎯) in the timeline header to set zoom start time
2. Move the playhead to your desired end time
3. Click the second **Target** button (🎯) to set zoom end time

### 2. **Select Zoom Area**
1. Click the **Move** button (↔️) in the timeline header to activate zoom area selection
2. An interactive overlay will appear over the video preview
3. **Drag** to draw a rectangle over the area you want to zoom into
4. The **size of the rectangle determines the zoom level**: smaller area = higher zoom
5. The blue rectangle shows the current zoom area
6. Yellow rectangle appears while dragging to show your selection

### 3. **Fine-tune Area**
- **Area display**: Shows current area size and center position as percentages
- **Reset button**: Click the **Reset** button (↻) to return to default area (center 50%×50%)
- **Dynamic zoom**: Zoom scale is calculated automatically based on area size
- **Escape key**: Press ESC to cancel current drag or close the overlay
- **R key**: Press R to reset to default area

## 🎨 Visual Indicators

### In the Overlay:
- **Blue rectangle**: Current zoom area with center point indicator
- **Yellow rectangle**: Drag selection area (while dragging)
- **Crosshair lines**: Show center of current zoom area
- **Area info**: Bottom-left shows area size and center coordinates
- **Zoom info**: Bottom-right shows timing and dynamic scale information
- **Instructions**: Top-left shows current action (drag/release)

### In the Timeline Header:
- **Move button**: Toggles blue when area selection mode is active
- **Coordinates display**: Shows current area center (e.g., "50%,50%")
- **Reset button**: Returns zoom area to default center area

## ⚙️ Technical Details

### Coordinate System
- **X-axis**: 0% = left edge, 50% = center, 100% = right edge
- **Y-axis**: 0% = top edge, 50% = center, 100% = bottom edge
- **Default**: Center position (50%, 50%)

### Zoom Behavior
- Zoom scales from the selected position using CSS `transform-origin`
- Works with all zoom scales and timing configurations
- Compatible with existing zoom-out effects
- Maintains smooth performance during playback

### Integration
- **Player preview**: Real-time zoom positioning during playback
- **Remotion rendering**: Positioned zoom included in final video output
- **Performance optimized**: Cached calculations and memoized transforms

## 🔧 Customization

### Programmatic Control
```typescript
import { useZoomStore } from '../store/use-zoom-store';

const { setZoomPosition, resetZoomPosition } = useZoomStore();

// Set custom position
setZoomPosition({ x: 0.3, y: 0.7 }); // 30% from left, 70% from top

// Reset to center
resetZoomPosition();
```

### Accessing Current Position
```typescript
const { config } = useZoomStore();
console.log('Current zoom position:', config.position); // { x: 0.5, y: 0.5 }
```

## 🎬 Workflow Example

1. **Import/add your video** to the timeline
2. **Set zoom timing**: Start at 2s, end at 5s (3-second zoom)
3. **Position zoom**: Click the Move button, then click on a person's face in the video
4. **Preview**: Play the video to see the zoom focus on the selected area
5. **Adjust**: Fine-tune position or reset to center if needed
6. **Render**: Export video with positioned zoom effect

## 🚀 Tips for Best Results

- **Preview first**: Always preview the zoom effect before final rendering
- **Consider composition**: Position zoom on important subjects or action
- **Timing matters**: Longer zoom durations work better for subtle positioning
- **Test different scales**: Higher zoom scales make positioning more noticeable
- **Use reset**: Don't hesitate to reset and try different positions

## 🔍 Troubleshooting

**Overlay not appearing?**
- Make sure you've set zoom timing first
- Check that the Move button is highlighted (blue)

**Position not saving?**
- Click directly on the video area, not the overlay controls
- Ensure you're clicking within the video bounds

**Zoom looks off in final render?**
- Preview in the player first to verify positioning
- Check that zoom timing matches your expectations

## 🎯 Advanced Usage

### Multiple Zoom Effects
- Each video track item can have its own zoom position
- Set different positions for different time ranges
- Combine with other effects for complex animations

### Performance Considerations
- Zoom positioning is optimized for smooth playback
- Large zoom scales may impact performance on slower devices
- Consider zoom duration for best user experience

---

**Need help?** The zoom positioning feature integrates seamlessly with existing zoom controls and maintains all previous functionality while adding precise positioning capabilities.
