<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mouse Tracker Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-area {
            border: 2px dashed #ccc;
            padding: 40px;
            margin: 20px 0;
            text-align: center;
            border-radius: 10px;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }

        .test-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0;
        }

        .test-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-weight: 500;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary { background: #667eea; }
        .btn-success { background: #27ae60; }
        .btn-warning { background: #f39c12; }
        .btn-danger { background: #e74c3c; }
        .btn-info { background: #3498db; }

        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }

        .instructions h3 {
            margin-top: 0;
            color: #2980b9;
        }

        .moving-target {
            width: 50px;
            height: 50px;
            background: #e74c3c;
            border-radius: 50%;
            position: relative;
            margin: 20px auto;
            animation: bounce 2s infinite;
            cursor: pointer;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }

        .click-counter {
            text-align: center;
            font-size: 18px;
            margin: 20px 0;
            color: #333;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .grid-item {
            aspect-ratio: 1;
            background: #667eea;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .grid-item:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }

        .grid-item.clicked {
            background: #27ae60;
            transform: scale(0.95);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖱️ Mouse Tracker Test Page</h1>
        
        <div class="instructions">
            <h3>How to Test:</h3>
            <ol>
                <li>Open the Mouse Tracker extension control page</li>
                <li>Select "Current Tab" for recording type</li>
                <li>Enable mouse tracking</li>
                <li>Click "Start Recording"</li>
                <li>Move your mouse around this page and click on various elements</li>
                <li>Click "Stop Recording" to see the results</li>
            </ol>
        </div>

        <div class="test-area">
            <h2>Mouse Movement Test Area</h2>
            <p>Move your mouse around this area to generate tracking data</p>
            <div class="moving-target" onclick="targetClicked()"></div>
            <p>Try to click the bouncing red circle!</p>
        </div>

        <div class="buttons">
            <button class="test-btn btn-primary" onclick="buttonClicked(this)">Primary Button</button>
            <button class="test-btn btn-success" onclick="buttonClicked(this)">Success Button</button>
            <button class="test-btn btn-warning" onclick="buttonClicked(this)">Warning Button</button>
            <button class="test-btn btn-danger" onclick="buttonClicked(this)">Danger Button</button>
            <button class="test-btn btn-info" onclick="buttonClicked(this)">Info Button</button>
        </div>

        <div class="click-counter">
            <strong>Total Clicks: <span id="clickCount">0</span></strong>
        </div>

        <div class="grid" id="clickGrid">
            <!-- Grid items will be generated by JavaScript -->
        </div>
    </div>

    <script>
        let clickCount = 0;
        const clickCountElement = document.getElementById('clickCount');

        function updateClickCount() {
            clickCount++;
            clickCountElement.textContent = clickCount;
        }

        function buttonClicked(button) {
            updateClickCount();
            const originalText = button.textContent;
            button.textContent = 'Clicked!';
            button.style.transform = 'scale(0.95)';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.transform = '';
            }, 500);
        }

        function targetClicked() {
            updateClickCount();
            const target = document.querySelector('.moving-target');
            target.style.background = '#27ae60';
            target.style.transform = 'scale(1.2)';
            
            setTimeout(() => {
                target.style.background = '#e74c3c';
                target.style.transform = '';
            }, 300);
        }

        // Create grid of clickable items
        function createGrid() {
            const grid = document.getElementById('clickGrid');
            for (let i = 1; i <= 12; i++) {
                const item = document.createElement('div');
                item.className = 'grid-item';
                item.textContent = i;
                item.onclick = function() {
                    updateClickCount();
                    this.classList.add('clicked');
                    setTimeout(() => {
                        this.classList.remove('clicked');
                    }, 500);
                };
                grid.appendChild(item);
            }
        }

        // Initialize the page
        createGrid();

        // Add some random movement tracking
        document.addEventListener('mousemove', function(e) {
            // This is just for visual feedback - the extension will track the real movements
            const x = e.clientX;
            const y = e.clientY;
            
            // Create a small trail effect
            const trail = document.createElement('div');
            trail.style.position = 'fixed';
            trail.style.left = x + 'px';
            trail.style.top = y + 'px';
            trail.style.width = '4px';
            trail.style.height = '4px';
            trail.style.background = 'rgba(102, 126, 234, 0.6)';
            trail.style.borderRadius = '50%';
            trail.style.pointerEvents = 'none';
            trail.style.zIndex = '9999';
            document.body.appendChild(trail);
            
            setTimeout(() => {
                trail.remove();
            }, 1000);
        });
    </script>
</body>
</html>
