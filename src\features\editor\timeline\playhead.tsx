import { useCurrentPlayerFrameOptimized } from "../hooks/use-current-frame-optimized";
import useStore from "../store/use-store";
import { MouseEvent, TouchEvent, useEffect, useRef, useState, useCallback } from "react";
import { timeMsToUnits, unitsToTimeMs } from "../utils/timeline";
import { TIMELINE_OFFSET_CANVAS_LEFT } from "../constants/constants";
import PerformanceMonitor from "../debug/performance-monitor";
import CPUMonitor from "../debug/cpu-monitor";

const Playhead = ({ scrollLeft }: { scrollLeft: number }) => {
  const playheadRef = useRef<HTMLDivElement>(null);
  const { playerRef, fps, scale, setIsPlayheadDragging } = useStore();
  const currentFrame = useCurrentPlayerFrameOptimized(playerRef!);
  const position =
    timeMsToUnits((currentFrame / fps) * 1000, scale.zoom) - scrollLeft;
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartPosition, setDragStartPosition] = useState(position);
  const [dragPosition, setDragPosition] = useState(position);
  const [hasMoved, setHasMoved] = useState(false); // Track if actual dragging occurred
  const [isLocallyDragging, setIsLocallyDragging] = useState(false); // Local dragging state to override position

  // Refs for throttling
  const lastSeekTime = useRef(0);
  const seekTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Throttle seeking to reduce CPU usage
  const SEEK_THROTTLE_MS = 16; // ~60fps for smooth seeking

  // Store the target frame for seeking after dragging ends
  const pendingSeekFrameRef = useRef<number | null>(null);

  // Deferred seeking: block all seeks during dragging, perform final seek when dragging ends
  const deferredSeek = useCallback((targetFrame: number) => {
    if (isDragging) {
      // During dragging, just store the target frame - don't seek at all
      pendingSeekFrameRef.current = targetFrame;
      return;
    }

    // Normal seeking when not dragging with throttling
    const now = Date.now();

    // Clear any pending seek
    if (seekTimeoutRef.current) {
      clearTimeout(seekTimeoutRef.current);
      seekTimeoutRef.current = null;
    }

    // If enough time has passed, seek immediately
    if (now - lastSeekTime.current >= SEEK_THROTTLE_MS) {
      lastSeekTime.current = now;
      PerformanceMonitor.getInstance().trackSeekOperation();
      CPUMonitor.getInstance().trackSeek();
      playerRef?.current?.seekTo(targetFrame);
    } else {
      // Otherwise, schedule a seek
      const delay = SEEK_THROTTLE_MS - (now - lastSeekTime.current);
      seekTimeoutRef.current = setTimeout(() => {
        lastSeekTime.current = Date.now();
        PerformanceMonitor.getInstance().trackSeekOperation();
        CPUMonitor.getInstance().trackSeek();
        playerRef?.current?.seekTo(targetFrame);
        seekTimeoutRef.current = null;
      }, delay);
    }
  }, [playerRef, isDragging]);

  const handleMouseUp = () => {
    console.log('🔍 DEBUG: Playhead interaction ended, hasMoved:', hasMoved);

    // DISABLE PERFORMANCE MONITORING TO TEST IF THIS CAUSES VIDEO REFRESH
    // PerformanceMonitor.getInstance().stopMonitoring();
    // CPUMonitor.getInstance().endOperation('playhead-dragging');
    // CPUMonitor.getInstance().updateVideoState({ isDragging: false });
    // CPUMonitor.getInstance().stopMonitoring();

    setIsDragging(false);
    setHasMoved(false);

    // Perform seek if there was actual movement to update the video position
    if (hasMoved) {
      const time = unitsToTimeMs(dragPosition, scale.zoom);
      const targetFrame = Math.round((time * fps) / 1000);
      console.log('🔍 DEBUG: Seeking to frame:', targetFrame, 'to update position');
      playerRef?.current?.seekTo(targetFrame);
    }

    // Disable local dragging override AFTER seeking to prevent position jump
    setTimeout(() => {
      setIsLocallyDragging(false);
    }, 50); // Small delay to let seek complete

    // DISABLE GLOBAL STATE CHANGES ENTIRELY TO PREVENT VIDEO REFRESH
    console.log('🔍 DEBUG: Skipping all setIsPlayheadDragging calls to prevent video refresh');
  };

  const handleMouseDown = (
    e:
      | MouseEvent<HTMLDivElement, globalThis.MouseEvent>
      | TouchEvent<HTMLDivElement>,
  ) => {
    e.preventDefault(); // Prevent default drag behavior
    console.log('🔍 DEBUG: Playhead interaction started - current frame:', currentFrame);

    // DISABLE PERFORMANCE MONITORING TO TEST IF THIS CAUSES VIDEO REFRESH
    // PerformanceMonitor.getInstance().startMonitoring();
    // CPUMonitor.getInstance().startMonitoring();
    // CPUMonitor.getInstance().updateVideoState({ isDragging: true });
    // CPUMonitor.getInstance().startOperation('playhead-dragging');

    setIsDragging(true);
    setIsLocallyDragging(true); // Enable local dragging override
    setHasMoved(false); // Reset movement tracking
    const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
    setDragStartX(clientX);
    setDragStartPosition(position);
    setDragPosition(position);
  };

  const handleMouseMove = (
    e: globalThis.MouseEvent | globalThis.TouchEvent,
  ) => {
    if (isDragging) {
      PerformanceMonitor.getInstance().trackFunction('handleMouseMove');
      PerformanceMonitor.getInstance().trackFrame();

      e.preventDefault(); // Prevent default drag behavior
      const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
      const delta = clientX - dragStartX + scrollLeft;
      const newPosition = dragStartPosition + delta;

      // Track that actual movement has occurred
      if (Math.abs(delta) > 2) { // Small threshold to ignore tiny movements
        setHasMoved(true);
        // DISABLE GLOBAL DRAGGING STATE TO PREVENT VIDEO REFRESH
        console.log('🔍 DEBUG: Skipping setIsPlayheadDragging(true) to prevent video refresh');
        // if (!useStore.getState().isPlayheadDragging) {
        //   setIsPlayheadDragging(true);
        // }
      }

      // Update visual position immediately for smooth dragging
      setDragPosition(newPosition);

      // Use requestAnimationFrame for smooth visual updates
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        const time = unitsToTimeMs(newPosition, scale.zoom);
        const targetFrame = (time * fps) / 1000;

        // Snap to frame boundaries for better performance
        const snappedFrame = Math.round(targetFrame);

        // Use deferred seek - blocks seeking during dragging
        deferredSeek(snappedFrame);

        animationFrameRef.current = null;
      });
    }
  };

  useEffect(() => {
    const preventDefaultDrag = (e: Event) => {
      e.preventDefault();
    };

    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      document.addEventListener("touchmove", handleMouseMove);
      document.addEventListener("touchend", handleMouseUp);
      document.addEventListener("dragstart", preventDefaultDrag);
    } else {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.removeEventListener("touchmove", handleMouseMove);
      document.removeEventListener("touchend", handleMouseUp);
      document.removeEventListener("dragstart", preventDefaultDrag);
    }

    // Cleanup event listeners on component unmount
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.removeEventListener("touchmove", handleMouseMove);
      document.removeEventListener("touchend", handleMouseUp);
      document.removeEventListener("dragstart", preventDefaultDrag);

      // Clean up any pending operations
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (seekTimeoutRef.current) {
        clearTimeout(seekTimeoutRef.current);
        seekTimeoutRef.current = null;
      }
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (seekTimeoutRef.current) {
        clearTimeout(seekTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={playheadRef}
      onMouseDown={handleMouseDown}
      onTouchStart={handleMouseDown}
      onDragStart={(e) => e.preventDefault()}
      style={{
        position: "absolute",
        left: 40 + TIMELINE_OFFSET_CANVAS_LEFT + (isLocallyDragging ? dragPosition : position),
        top: 50,
        width: 1,
        height: "calc(100% - 40px)",
        zIndex: 50,
        cursor: "pointer",
        touchAction: "none", // Prevent default touch actions
      }}
    >
      <div
        style={{
          borderRadius: "0 0 4px 4px",
        }}
        className="absolute top-0 h-4 w-2 -translate-x-1/2 transform bg-red-500 text-xs font-semibold text-zinc-800"
      ></div>
      <div className="relative h-full">
        <div className="absolute top-0 h-full w-3 -translate-x-1/2 transform"></div>
        <div className="absolute top-0 h-full w-0.5 -translate-x-1/2 transform bg-red-500/50"></div>
      </div>
    </div>
  );
};

export default Playhead;
