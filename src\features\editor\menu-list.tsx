import useLayoutStore from "./store/use-layout-store";
import { Icons } from "@/components/shared/icons";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default function MenuList() {
  const { setActiveMenuItem, setShowMenuItem, activeMenuItem, showMenuItem } =
    useLayoutStore();
  return (
    <div className="flex w-14 flex-col items-center gap-1 border-r border-border/80 py-2">
      <Button
        onClick={() => {
          setActiveMenuItem("media");
          setShowMenuItem(true);
        }}
        className={cn(
          showMenuItem && (activeMenuItem === "media" || activeMenuItem === "videos" || activeMenuItem === "audios" || activeMenuItem === "canvas")
            ? "bg-secondary"
            : "text-muted-foreground",
        )}
        variant={"ghost"}
        size={"icon"}
      >
        <Icons.upload width={16} />
      </Button>
    </div>
  );
}
