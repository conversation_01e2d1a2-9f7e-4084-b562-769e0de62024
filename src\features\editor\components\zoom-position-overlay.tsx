import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useZoomStore } from '../store/use-zoom-store';
import useStore from '../store/use-store';
import { Target, X } from 'lucide-react';

interface ZoomPositionOverlayProps {
  /** Whether the overlay is active/visible */
  isActive: boolean;
  /** Callback when overlay is closed */
  onClose: () => void;
  /** Container dimensions */
  containerWidth: number;
  containerHeight: number;
}

/**
 * Interactive overlay component that allows users to click on the video preview
 * to set the zoom center point. Shows visual feedback for the current zoom position.
 */
export const ZoomPositionOverlay: React.FC<ZoomPositionOverlayProps> = ({
  isActive,
  onClose,
  containerWidth,
  containerHeight,
}) => {
  const { config: zoomConfig, setZoomAreaFromRect, resetZoomArea, getZoomCenter } = useZoomStore();
  const { selectedZoomEffectId, zoomEffects, updateZoomEffect } = useStore((state) => ({
    selectedZoomEffectId: state.selectedZoomEffectId,
    zoomEffects: state.zoomEffects,
    updateZoomEffect: state.updateZoomEffect,
  }));

  // Get the selected effect's zoom area, or fall back to global config
  const selectedEffect = zoomEffects.find(effect => effect.id === selectedZoomEffectId);
  const currentZoomArea = selectedEffect?.zoomArea || zoomConfig.zoomArea;

  const [isHovering, setIsHovering] = useState(false);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [dragEnd, setDragEnd] = useState({ x: 0, y: 0 });
  const overlayRef = useRef<HTMLDivElement>(null);

  // Handle mouse down to start dragging
  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (!overlayRef.current) return;

    const rect = overlayRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left) / rect.width;
    const y = (event.clientY - rect.top) / rect.height;

    // Clamp values between 0 and 1
    const clampedX = Math.max(0, Math.min(1, x));
    const clampedY = Math.max(0, Math.min(1, y));

    setIsDragging(true);
    setDragStart({ x: clampedX, y: clampedY });
    setDragEnd({ x: clampedX, y: clampedY });
  }, []);

  // Handle mouse up to finish dragging
  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      // Only set zoom area if there's a meaningful drag (minimum size)
      const width = Math.abs(dragEnd.x - dragStart.x);
      const height = Math.abs(dragEnd.y - dragStart.y);

      if (width > 0.02 && height > 0.02) { // Minimum 2% size
        if (selectedZoomEffectId) {
          // Update the selected effect's zoom area
          const newZoomArea = {
            x: Math.min(dragStart.x, dragEnd.x),
            y: Math.min(dragStart.y, dragEnd.y),
            width: Math.abs(dragEnd.x - dragStart.x),
            height: Math.abs(dragEnd.y - dragStart.y),
          };
          updateZoomEffect(selectedZoomEffectId, { zoomArea: newZoomArea });
        } else {
          // Fallback to global zoom area if no effect is selected
          setZoomAreaFromRect(dragStart.x, dragStart.y, dragEnd.x, dragEnd.y);
        }
      }

      setIsDragging(false);
    }
  }, [isDragging, dragStart, dragEnd, selectedZoomEffectId, updateZoomEffect, setZoomAreaFromRect]);

  // Handle mouse move for hover preview and dragging
  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (!overlayRef.current) return;

    const rect = overlayRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left) / rect.width;
    const y = (event.clientY - rect.top) / rect.height;

    // Clamp values between 0 and 1
    const clampedX = Math.max(0, Math.min(1, x));
    const clampedY = Math.max(0, Math.min(1, y));

    if (isDragging) {
      setDragEnd({ x: clampedX, y: clampedY });
    } else {
      setHoverPosition({ x: clampedX, y: clampedY });
    }
  }, [isDragging]);

  // Handle mouse enter/leave
  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false);
  }, []);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (isDragging) {
          // Cancel current drag
          setIsDragging(false);
        } else {
          onClose();
        }
      } else if (event.key === 'r' || event.key === 'R') {
        resetZoomArea();
      }
    };

    if (isActive) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isActive, isDragging, onClose, resetZoomArea]);

  if (!isActive) return null;

  // Calculate current zoom area coordinates using the selected effect's zoom area
  const zoomAreaX = currentZoomArea.x * containerWidth;
  const zoomAreaY = currentZoomArea.y * containerHeight;
  const zoomAreaWidth = currentZoomArea.width * containerWidth;
  const zoomAreaHeight = currentZoomArea.height * containerHeight;

  // Calculate center of current zoom area
  const centerX = zoomAreaX + zoomAreaWidth / 2;
  const centerY = zoomAreaY + zoomAreaHeight / 2;

  // Calculate hover position
  const hoverX = hoverPosition.x * containerWidth;
  const hoverY = hoverPosition.y * containerHeight;

  // Calculate drag rectangle if dragging
  const dragRect = isDragging ? {
    x: Math.min(dragStart.x, dragEnd.x) * containerWidth,
    y: Math.min(dragStart.y, dragEnd.y) * containerHeight,
    width: Math.abs(dragEnd.x - dragStart.x) * containerWidth,
    height: Math.abs(dragEnd.y - dragStart.y) * containerHeight,
  } : null;

  return (
    <div
      ref={overlayRef}
      className="absolute inset-0 cursor-crosshair"
      style={{
        width: containerWidth,
        height: containerHeight,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(1px)',
        zIndex: 9999,
        pointerEvents: 'auto',
      }}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Instructions overlay */}
      <div className="absolute top-4 left-4 bg-black/80 text-white px-3 py-2 rounded-md text-sm">
        <div className="flex items-center gap-2 mb-1">
          <Target size={14} />
          <span>{isDragging ? 'Release to set zoom area' : 'Drag to select zoom area'}</span>
        </div>
        <div className="text-xs text-gray-300">
          Press ESC to {isDragging ? 'cancel drag' : 'close'} • R to reset
        </div>
      </div>

      {/* Close button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onClose();
        }}
        className="absolute top-4 right-4 bg-black/80 text-white p-2 rounded-md hover:bg-black/90 transition-colors"
        title="Close zoom positioning"
      >
        <X size={16} />
      </button>

      {/* Reset button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          resetZoomArea();
        }}
        className="absolute top-4 right-16 bg-black/80 text-white px-3 py-2 rounded-md hover:bg-black/90 transition-colors text-sm"
        title="Reset zoom area"
      >
        Reset
      </button>

      {/* Current zoom area indicator */}
      <div
        className="absolute border-2 border-blue-400 bg-blue-400/10 pointer-events-none"
        style={{
          left: zoomAreaX,
          top: zoomAreaY,
          width: zoomAreaWidth,
          height: zoomAreaHeight,
        }}
      >
        <div className="absolute inset-0 border border-white/30" />
        {/* Center point indicator */}
        <div
          className="absolute w-3 h-3 border border-blue-400 bg-blue-400/40 rounded-full transform -translate-x-1/2 -translate-y-1/2"
          style={{
            left: '50%',
            top: '50%',
          }}
        />
      </div>

      {/* Drag rectangle (while dragging) */}
      {isDragging && dragRect && (
        <div
          className="absolute border-2 border-yellow-400 bg-yellow-400/10 pointer-events-none"
          style={{
            left: dragRect.x,
            top: dragRect.y,
            width: dragRect.width,
            height: dragRect.height,
          }}
        >
          <div className="absolute inset-0 border border-white/50" />
        </div>
      )}

      {/* Hover position indicator (when not dragging) */}
      {isHovering && !isDragging && (
        <div
          className="absolute w-2 h-2 border border-white/60 bg-white/20 rounded-full transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
          style={{
            left: hoverX,
            top: hoverY,
          }}
        />
      )}

      {/* Crosshair lines for center */}
      <div
        className="absolute w-full h-px bg-blue-400/30 pointer-events-none"
        style={{ top: centerY }}
      />
      <div
        className="absolute h-full w-px bg-blue-400/30 pointer-events-none"
        style={{ left: centerX }}
      />

      {/* Zoom area coordinates display */}
      <div className="absolute bottom-4 left-4 bg-black/80 text-white px-3 py-2 rounded-md text-sm font-mono">
        <div>Area: {(currentZoomArea.width * 100).toFixed(1)}% × {(currentZoomArea.height * 100).toFixed(1)}%</div>
        <div>Center: {((currentZoomArea.x + currentZoomArea.width/2) * 100).toFixed(1)}%, {((currentZoomArea.y + currentZoomArea.height/2) * 100).toFixed(1)}%</div>
        {selectedEffect && (
          <div className="text-xs text-blue-300 mt-1">
            Editing: {(() => {
              const effectIndex = zoomEffects.findIndex(e => e.id === selectedZoomEffectId);
              const colors = ['Blue', 'Green', 'Purple', 'Pink', 'Indigo', 'Teal', 'Amber', 'Rose'];
              return colors[effectIndex % colors.length];
            })()} Zoom
          </div>
        )}
      </div>

      {/* Zoom timing and scale info */}
      {selectedEffect && (
        <div className="absolute bottom-4 right-4 bg-black/80 text-white px-3 py-2 rounded-md text-sm">
          <div>Zoom: {selectedEffect.startTime}ms - {selectedEffect.endTime}ms</div>
          <div>Scale: {zoomConfig.calculatedZoomScale.toFixed(1)}x (dynamic)</div>
        </div>
      )}
    </div>
  );
};

export default ZoomPositionOverlay;
