import { AbsoluteFill, OffthreadVideo, Audio, Sequence } from "remotion";
import { IItem, IVideo, IAudio } from "@designcombo/types";
import { calculateFrames } from "../utils/frames";
import { Animated } from "./animated";
import {
  calculateContainerStyles,
  calculateMediaStyles,
} from "./styles";
import { getAnimations } from "../utils/get-animations";
interface SequenceItemOptions {
  fps: number;
  currentTime?: number;
  zIndex?: number;
  active?: boolean;
}

export const SequenceItem: Record<
  string,
  (item: IItem, options: SequenceItemOptions) => JSX.Element
> = {
  video: (item, options: SequenceItemOptions) => {
    const { fps, zIndex } = options;
    const { details, animations } = item as IVideo;
    const { animationIn, animationOut } = getAnimations(animations!, item);
    const playbackRate = item.playbackRate || 1;
    const { from, durationInFrames } = calculateFrames(
      {
        from: item.display.from / playbackRate,
        to: item.display.to / playbackRate,
      },
      fps,
    );
    const crop = details.crop || {
      x: 0,
      y: 0,
      width: item.details.width,
      height: item.details.height,
    };

    // Direct video rendering without individual zoom effect
    const VideoContent = () => {
      return (
        <div style={calculateMediaStyles(details, crop)}>
          <OffthreadVideo
            startFrom={(item.trim?.from! / 1000) * fps}
            endAt={(item.trim?.to! / 1000) * fps}
            playbackRate={playbackRate}
            src={details.src}
            volume={(details.volume || 0) / 100}
          />
        </div>
      );
    };

    return (
      <Sequence
        key={item.id}
        from={from}
        durationInFrames={durationInFrames}
        style={{ pointerEvents: "none", zIndex }}
      >
        <AbsoluteFill
          data-track-item="transition-element"
          className={`designcombo-scene-item id-${item.id} designcombo-scene-item-type-${item.type}`}
          style={calculateContainerStyles(details, crop)}
        >
          {/* animation layer */}
          <Animated
            style={calculateContainerStyles(details, crop, {
              overflow: "hidden",
            })}
            animationIn={animationIn}
            animationOut={animationOut}
            durationInFrames={durationInFrames}
          >
            <VideoContent />
          </Animated>
        </AbsoluteFill>
      </Sequence>
    );
  },

  audio: (item, options: SequenceItemOptions) => {
    const { fps } = options;
    const { details } = item as IAudio;
    const playbackRate = item.playbackRate || 1;
    const { from, durationInFrames } = calculateFrames(
      {
        from: item.display.from / playbackRate,
        to: item.display.to / playbackRate,
      },
      fps,
    );

    return (
      <Sequence
        key={item.id}
        from={from}
        durationInFrames={durationInFrames}
      >
        <Audio
          startFrom={(item.trim?.from! / 1000) * fps}
          endAt={(item.trim?.to! / 1000) * fps}
          playbackRate={playbackRate}
          src={details.src}
          volume={(details.volume || 100) / 100}
        />
      </Sequence>
    );
  },
};
