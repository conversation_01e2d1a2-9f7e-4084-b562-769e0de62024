// Performance monitoring utility for debugging timeline issues
class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private isMonitoring = false;
  private startTime = 0;
  private frameCount = 0;
  private lastFrameTime = 0;
  private functionCalls: { [key: string]: number } = {};
  private expensiveOperations: { name: string; duration: number; timestamp: number }[] = [];

  // CPU and video playback specific monitoring
  private cpuSamples: number[] = [];
  private frameRates: number[] = [];
  private remotionEvents: { event: string; timestamp: number; frame?: number }[] = [];
  private videoPlaybackMetrics = {
    seekOperations: 0,
    frameUpdates: 0,
    renderCalls: 0,
    lastSeekTime: 0,
    averageSeekInterval: 0
  };

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startMonitoring() {
    this.isMonitoring = true;
    this.startTime = performance.now();
    this.frameCount = 0;
    this.functionCalls = {};
    this.expensiveOperations = [];
    this.cpuSamples = [];
    this.frameRates = [];
    this.remotionEvents = [];
    this.videoPlaybackMetrics = {
      seekOperations: 0,
      frameUpdates: 0,
      renderCalls: 0,
      lastSeekTime: 0,
      averageSeekInterval: 0
    };
    console.log('🔍 Performance monitoring started - CPU and video playback tracking enabled');
    this.startCPUMonitoring();
  }

  stopMonitoring() {
    this.isMonitoring = false;
    const duration = performance.now() - this.startTime;
    const fps = this.frameCount / (duration / 1000);

    // Calculate CPU statistics
    const avgCPU = this.cpuSamples.length > 0 ?
      this.cpuSamples.reduce((a, b) => a + b, 0) / this.cpuSamples.length : 0;
    const maxCPU = this.cpuSamples.length > 0 ? Math.max(...this.cpuSamples) : 0;

    // Calculate average frame rate
    const avgFrameRate = this.frameRates.length > 0 ?
      this.frameRates.reduce((a, b) => a + b, 0) / this.frameRates.length : 0;

    console.log('📊 Performance Report:');
    console.log(`Duration: ${duration.toFixed(2)}ms`);
    console.log(`Frames: ${this.frameCount}`);
    console.log(`Average FPS: ${fps.toFixed(2)}`);
    console.log(`🔥 CPU Usage - Avg: ${avgCPU.toFixed(1)}%, Max: ${maxCPU.toFixed(1)}%`);
    console.log(`🎬 Video Metrics:`, this.videoPlaybackMetrics);
    console.log(`📈 Frame Rate - Avg: ${avgFrameRate.toFixed(1)} fps`);
    console.log('Function calls:', this.functionCalls);
    console.log('Expensive operations:', this.expensiveOperations);
    console.log('🎥 Remotion Events:', this.remotionEvents.slice(-10)); // Last 10 events

    // Analyze performance issues
    this.analyzePerformanceIssues(avgCPU, maxCPU, avgFrameRate);
  }

  trackFunction(functionName: string) {
    if (!this.isMonitoring) return;
    this.functionCalls[functionName] = (this.functionCalls[functionName] || 0) + 1;
  }

  trackExpensiveOperation(name: string, startTime: number) {
    if (!this.isMonitoring) return;
    const duration = performance.now() - startTime;
    if (duration > 1) { // Only track operations longer than 1ms
      this.expensiveOperations.push({
        name,
        duration,
        timestamp: performance.now() - this.startTime
      });
    }
  }

  trackFrame() {
    if (!this.isMonitoring) return;
    this.frameCount++;
    const now = performance.now();
    if (this.lastFrameTime > 0) {
      const frameDuration = now - this.lastFrameTime;
      if (frameDuration > 16.67) { // Slower than 60fps
        console.warn(`⚠️ Slow frame: ${frameDuration.toFixed(2)}ms`);
      }
    }
    this.lastFrameTime = now;
  }

  // Wrapper function to monitor any function
  monitor<T extends (...args: any[]) => any>(fn: T, name: string): T {
    return ((...args: any[]) => {
      const startTime = performance.now();
      this.trackFunction(name);
      const result = fn(...args);
      this.trackExpensiveOperation(name, startTime);
      return result;
    }) as T;
  }

  // Wrapper for async functions
  monitorAsync<T extends (...args: any[]) => Promise<any>>(fn: T, name: string): T {
    return (async (...args: any[]) => {
      const startTime = performance.now();
      this.trackFunction(name);
      const result = await fn(...args);
      this.trackExpensiveOperation(name, startTime);
      return result;
    }) as T;
  }

  // CPU monitoring methods
  private startCPUMonitoring() {
    if (typeof window === 'undefined') return;

    const sampleCPU = () => {
      if (!this.isMonitoring) return;

      // Estimate CPU usage based on frame timing and task scheduling
      const start = performance.now();
      setTimeout(() => {
        const delay = performance.now() - start;
        // If setTimeout is delayed significantly, CPU is likely under load
        const estimatedCPU = Math.min(100, Math.max(0, (delay - 1) * 10));
        this.cpuSamples.push(estimatedCPU);

        // Sample every 100ms
        setTimeout(sampleCPU, 100);
      }, 1);
    };

    sampleCPU();
  }

  // Video playback specific tracking
  trackRemotionEvent(event: string, frame?: number) {
    if (!this.isMonitoring) return;

    this.remotionEvents.push({
      event,
      timestamp: performance.now() - this.startTime,
      frame
    });

    // Keep only last 100 events to prevent memory issues
    if (this.remotionEvents.length > 100) {
      this.remotionEvents = this.remotionEvents.slice(-100);
    }
  }

  trackSeekOperation() {
    if (!this.isMonitoring) return;

    const now = performance.now();
    this.videoPlaybackMetrics.seekOperations++;

    if (this.videoPlaybackMetrics.lastSeekTime > 0) {
      const interval = now - this.videoPlaybackMetrics.lastSeekTime;
      this.videoPlaybackMetrics.averageSeekInterval =
        (this.videoPlaybackMetrics.averageSeekInterval + interval) / 2;
    }

    this.videoPlaybackMetrics.lastSeekTime = now;
    this.trackRemotionEvent('seek', undefined);
  }

  trackFrameUpdate(frame: number) {
    if (!this.isMonitoring) return;

    this.videoPlaybackMetrics.frameUpdates++;
    this.trackRemotionEvent('frameupdate', frame);
  }

  trackRenderCall() {
    if (!this.isMonitoring) return;

    this.videoPlaybackMetrics.renderCalls++;
    this.trackRemotionEvent('render', undefined);
  }

  // Performance analysis
  private analyzePerformanceIssues(avgCPU: number, maxCPU: number, avgFrameRate: number) {
    console.log('\n🔍 Performance Analysis:');

    if (maxCPU > 80) {
      console.log('🚨 HIGH CPU USAGE DETECTED!');
      console.log(`   Max CPU: ${maxCPU.toFixed(1)}% (threshold: 80%)`);

      // Analyze potential causes
      if (this.videoPlaybackMetrics.frameUpdates > 1000) {
        console.log('   ⚠️  Excessive frame updates detected');
        console.log(`   Frame updates: ${this.videoPlaybackMetrics.frameUpdates}`);
      }

      if (this.videoPlaybackMetrics.averageSeekInterval < 16) {
        console.log('   ⚠️  Very frequent seek operations detected');
        console.log(`   Avg seek interval: ${this.videoPlaybackMetrics.averageSeekInterval.toFixed(1)}ms`);
      }

      if (this.videoPlaybackMetrics.renderCalls > 500) {
        console.log('   ⚠️  Excessive render calls detected');
        console.log(`   Render calls: ${this.videoPlaybackMetrics.renderCalls}`);
      }
    }

    if (avgFrameRate < 30) {
      console.log('⚠️  Low frame rate detected');
      console.log(`   Average FPS: ${avgFrameRate.toFixed(1)} (target: 60)`);
    }

    // Check for specific performance patterns
    const recentEvents = this.remotionEvents.slice(-20);
    const frameUpdateCount = recentEvents.filter(e => e.event === 'frameupdate').length;
    const renderCount = recentEvents.filter(e => e.event === 'render').length;

    if (frameUpdateCount > 15) {
      console.log('⚠️  High frequency frame updates in recent activity');
    }

    if (renderCount > 10) {
      console.log('⚠️  High frequency renders in recent activity');
    }

    console.log('\n💡 Recommendations:');
    if (maxCPU > 80) {
      console.log('   • Check if playhead dragging throttling is working');
      console.log('   • Verify frame update optimization is active');
      console.log('   • Consider reducing video quality during playback');
    }

    if (avgFrameRate < 30) {
      console.log('   • Enable frame skipping during heavy operations');
      console.log('   • Check for blocking operations in render loop');
    }
  }
}

export default PerformanceMonitor;

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).perfMonitor = PerformanceMonitor.getInstance();
}
