import React, { useState, useEffect } from 'react';
import useStore from '../store/use-store';
import PerformanceMonitor from './performance-monitor';

const DebugPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [cpuUsage, setCpuUsage] = useState(0);
  const [frameRate, setFrameRate] = useState(0);
  const { isPlayheadDragging, trackItemIds } = useStore();
  
  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    
    const measurePerformance = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setFrameRate(frameCount);
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measurePerformance);
    };
    
    measurePerformance();
  }, []);

  // Toggle visibility with Ctrl+Shift+D
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setIsVisible(!isVisible);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isVisible]);

  if (!isVisible) {
    return (
      <div 
        style={{
          position: 'fixed',
          top: 10,
          right: 10,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 9999,
          cursor: 'pointer'
        }}
        onClick={() => setIsVisible(true)}
      >
        Debug (Ctrl+Shift+D)
      </div>
    );
  }

  return (
    <div 
      style={{
        position: 'fixed',
        top: 10,
        right: 10,
        background: 'rgba(0,0,0,0.9)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontSize: '12px',
        zIndex: 9999,
        minWidth: '250px',
        fontFamily: 'monospace'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
        <strong>Performance Debug</strong>
        <button 
          onClick={() => setIsVisible(false)}
          style={{ background: 'none', border: 'none', color: 'white', cursor: 'pointer' }}
        >
          ✕
        </button>
      </div>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>Frame Rate:</strong> {frameRate} fps
      </div>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>Playhead Dragging:</strong> {isPlayheadDragging ? '🔴 YES' : '🟢 NO'}
      </div>
      
      <div style={{ marginBottom: '5px' }}>
        <strong>Video Clips:</strong> {trackItemIds.length}
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Status:</strong> {frameRate < 30 ? '⚠️ SLOW' : '✅ GOOD'}
      </div>
      
      <div style={{ borderTop: '1px solid #444', paddingTop: '10px' }}>
        <button 
          onClick={() => {
            console.log('🔍 Starting manual performance monitoring...');
            PerformanceMonitor.getInstance().startMonitoring();
          }}
          style={{ 
            background: '#007acc', 
            border: 'none', 
            color: 'white', 
            padding: '5px 10px', 
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '5px',
            fontSize: '11px'
          }}
        >
          Start Monitor
        </button>
        
        <button 
          onClick={() => {
            console.log('🔍 Stopping performance monitoring...');
            PerformanceMonitor.getInstance().stopMonitoring();
          }}
          style={{ 
            background: '#d73a49', 
            border: 'none', 
            color: 'white', 
            padding: '5px 10px', 
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '11px'
          }}
        >
          Stop Monitor
        </button>
      </div>
      
      <div style={{ marginTop: '10px', fontSize: '10px', color: '#ccc' }}>
        Open DevTools Console for detailed logs
      </div>
    </div>
  );
};

export default DebugPanel;
