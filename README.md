<p align="center">
  <a href="https://github.com/designcombo/react-video-editor">
    <img width="150px" height="150px" src="https://cdn.designcombo.dev/logo-white.png"/>
  </a>
</p>
<h1 align="center">React Video Editor</h1>

<div align="center">
  
Video Editor application using React and TypeScript.

<p align="center">
    <a href="https://combo.sh/">Combo</a>
    ·  
    <a href="https://discord.gg/jrZs3wZyM5">Discord</a>
    ·  
    <a href="https://github.com/designcombo/react-video-editor">X</a>
</p>
</div>

[![](https://cdn.designcombo.dev/editor-preview.png)](https://github.com/designcombo/react-video-editor)

## ✨ Features

- 🎬 Timeline Editing: Arrange and trim media on a visual timeline.
- 🌟 Effects and Transitions: Apply visual effects, filters, and transitions.
- 🔀 Multi-track Support: Edit multiple video and audio tracks simultaneously.
- 📤 Export Options: Save videos in various resolutions and formats.
- 👀 Real-time Preview: See immediate previews of edits.
- 📤 Video Export: Render and download finished videos with customizable settings.
- 🎥 Multiple Formats: Export in various resolutions, codecs, and quality settings.

## ⌨️ Development

Clone locally:

```bash
<NAME_EMAIL>:designcombo/react-video-editor.git
cd react-video-editor
pnpm install
pnpm dev
```

Open your browser and visit http://localhost:5173 , see more at [Development](https://github.com/designcombo/react-video-editor/react-video-editor).

## 🎥 Video Export Setup

To enable video export functionality:

1. **Install render server dependencies:**
   ```bash
   npm run render-server:install
   ```

2. **Start the render server:**
   ```bash
   npm run render-server:dev
   ```

3. **Test the setup:**
   ```bash
   npm run test-export
   ```

For detailed setup instructions, see [EXPORT_SETUP.md](./EXPORT_SETUP.md).

## 📝 License

Copyright © 2025 [DesignCombo](https://combo.sh/).