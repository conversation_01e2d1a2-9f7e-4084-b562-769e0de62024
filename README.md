# Mouse Tracker & Screen Recorder

A Chrome extension that combines mouse tracking with screen recording capabilities. Track mouse movements and clicks while recording your screen, window, or browser tab.

## Features

- **Screen Recording**: Record your entire screen, specific window, or current browser tab
- **Mouse Tracking**: Track mouse movements and clicks on the selected tab
- **Visual Mouse Path**: See a visualization of your mouse movements
- **Data Export**: Copy mouse tracking data as JSON
- **Modern Interface**: Clean, intuitive control panel instead of popup

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select this extension folder
4. The extension icon will appear in your toolbar

## Usage

1. **Open Control Panel**: Click the extension icon in your toolbar to open the control page
2. **Choose Recording Type**:
   - **Current Tab**: Records the active browser tab (requires a web page to be open)
   - **Window**: Records a specific window (you'll be prompted to select)
   - **Entire Screen**: Records your full screen
3. **Configure Mouse Tracking**:
   - Enable/disable mouse tracking on the selected tab
   - Choose whether to show mouse path visualization
4. **Check Target Tab**: The extension will show which tab will be used for recording/tracking
5. **Start Recording**: Click "Start Recording" to begin
6. **Stop Recording**: Click "Stop Recording" when finished
7. **View Results**:
   - Download the screen recording as a WebM file
   - View mouse tracking statistics and visualization
   - Copy mouse data as JSON

## Testing

A test page (`test-page.html`) is included to help you test the extension:

1. Open `test-page.html` in your browser
2. Open the extension control panel
3. Select "Current Tab" and enable mouse tracking
4. Start recording and interact with the test page
5. Stop recording to see the results

## Permissions

The extension requires these permissions:
- `activeTab`: To inject mouse tracking into the current tab
- `scripting`: To execute mouse tracking scripts
- `storage`: To save recording state
- `desktopCapture`: To record screen and windows
- `tabs`: To manage tab recording
- `tabCapture`: To record browser tabs

## Export Format

The mouse tracking data is exported in the following JSON format:

```json
{
    "mouseActions": [
        {
            "action": "move",
            "coords": {
                "x": 1764,
                "y": 114
            }
        },
        {
            "action": "click",
            "coords": {
                "x": 1762,
                "y": 116
            }
        }
    ],
    "recordingInfo": {
        "totalCoordinates": 150,
        "totalClicks": 5,
        "timestamp": "2024-01-15T10:30:00.000Z"
    }
}
```

## Technical Details

- Screen recordings are saved as WebM files with VP9/VP8 codec
- Mouse tracking data includes coordinates and action types (move/click)
- The extension uses Chrome's native APIs for optimal performance
- All recording happens locally - no data is sent to external servers

## Changes from Original

This version replaces the original popup interface with a full control page and adds comprehensive screen recording capabilities while maintaining all original mouse tracking functionality.

## Known Limitations

- Mouse tracking only works on http/https sites (not on `chrome://` pages or extension pages)
- Tab recording requires a valid web page to be open (http/https)
- Screen recording requires user permission for desktop capture
- Tab recording may not work on some protected pages
- The extension will automatically switch to a suitable tab for recording if needed

## Troubleshooting

**"Extension has not been invoked for the current page" error:**
- Make sure you have a web page (http:// or https://) open in another tab
- The extension cannot record its own control page or Chrome internal pages

**"Cannot access contents of url" error:**
- Ensure the target tab is a regular web page, not a Chrome extension or system page
- Try refreshing the target tab and clicking "Refresh Target Tab" in the control panel
