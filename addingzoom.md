# Adding Zoom Effects to Remotion Video Editor

This document explains how the dynamic zoom effect system was implemented in the React Video Editor project, including both the original hardcoded version and the new user-controllable zoom timing features.

## 🎯 Overview

The zoom effect system provides a smooth zoom in and out animation that can be applied to video track items during Remotion rendering. The system now supports both:

1. **Hardcoded zoom timing** (original implementation) - Fixed 1-4 second zoom
2. **Dynamic zoom timing** (new feature) - User-controllable start/end points via timeline buttons

## 🎮 User Interface Controls

### Timeline Header Buttons
The timeline header now includes zoom timing controls:

- **🎯 Zoom Start Button**: Sets the zoom start time to the current playhead position
- **🎯 Zoom End Button**: Sets the zoom end time to the current playhead position
- **❌ Clear Button**: Clears the zoom timing (disables zoom effect)
- **Duration Display**: Shows the current zoom duration between the buttons

### How to Use
1. **Set Zoom Start**: Seek to desired start time → Click first Target button
2. **Set Zoom End**: Seek to desired end time → Click second Target button
3. **Preview**: The zoom effect will be applied during export
4. **Clear**: Click X button to disable zoom effect

## 📅 Timeline Behavior

### Default Behavior (Hardcoded)
```
0s ────────── 1s ────────── 4s ────────── End
   Normal      Zoom Effect    Normal
   Playback    (3 seconds)    Playback
   Scale: 1.0  Scale: 1.0→1.5→1.0  Scale: 1.0
```

### Dynamic Behavior (User-Controlled)
```
0s ──── Start ──────── End ──── Duration End
   Normal   Zoom Effect   Normal
   Playback (Custom)     Playback
   Scale: 1.0 1.0→1.5→1.0 Scale: 1.0
```
*Start and End times are set by user via timeline buttons*

## 🔧 Implementation Details

### Key Files Modified
- **`src/features/editor/player/sequence-item.tsx`** - Zoom effect logic
- **`src/features/editor/timeline/header.tsx`** - UI controls
- **`src/features/editor/store/use-store.ts`** - State management
- **`src/remotion/VideoEditorComposition.tsx`** - Remotion integration

### Key Components

#### 1. Remotion Imports
```typescript
import { 
  AbsoluteFill, 
  OffthreadVideo, 
  Audio, 
  Sequence, 
  useCurrentFrame,  // ← Added for zoom effect
  interpolate       // ← Added for future enhancements
} from "remotion";
```

#### 2. State Management (Zustand Store)
```typescript
interface IZoomTiming {
  startTime: number; // in milliseconds
  endTime: number;   // in milliseconds
}

interface ITimelineStore {
  // ... existing properties
  zoomTiming: IZoomTiming;
  setZoomStartTime: (time: number) => void;
  setZoomEndTime: (time: number) => void;
  clearZoomTiming: () => void;
}
```

#### 3. Timeline Header Controls
```typescript
// Zoom timing control handlers
const handleSetZoomStart = () => {
  const currentTime = getCurrentTime();
  setZoomStartTime(currentTime);
};

const handleSetZoomEnd = () => {
  const currentTime = getCurrentTime();
  setZoomEndTime(currentTime);
};

// UI Components in timeline header
<div className="flex items-center bg-muted/50 rounded-md p-0.5 ml-3">
  <Button onClick={handleSetZoomStart} title="Set zoom start">
    <Target size={14} />
  </Button>
  <div className="text-xs text-muted-foreground px-1">
    {timeToString({ time: zoomTiming.endTime - zoomTiming.startTime })}
  </div>
  <Button onClick={handleSetZoomEnd} title="Set zoom end">
    <Target size={14} />
  </Button>
  <Button onClick={handleClearZoom} title="Clear zoom timing">
    <X size={14} />
  </Button>
</div>
```

#### 4. ZoomEffect Component
```typescript
const ZoomEffect = () => {
  const frame = useCurrentFrame();

  // Use zoom timing from options (Remotion) or store (Player)
  let effectiveZoomTiming;
  try {
    const storeData = useStore();
    effectiveZoomTiming = zoomTiming || storeData.zoomTiming;
  } catch {
    // Store not available in Remotion context, use options or no default
    effectiveZoomTiming = zoomTiming || { startTime: 0, endTime: 0 };
  }

  // Convert zoom timing from milliseconds to frames
  const zoomStartFrame = Math.floor((effectiveZoomTiming.startTime / 1000) * fps);
  const zoomEndFrame = Math.floor((effectiveZoomTiming.endTime / 1000) * fps);
  const zoomDurationFrames = zoomEndFrame - zoomStartFrame;

  let zoomScale = 1;

  // Only apply zoom if we have valid duration and timing
  if (zoomDurationFrames > 0 && frame >= zoomStartFrame && frame <= zoomEndFrame) {
    const progress = (frame - zoomStartFrame) / zoomDurationFrames;
    // Sine wave creates smooth zoom in/out
    zoomScale = 1 + 0.5 * Math.sin(progress * Math.PI);
  }

  return (
    <div
      style={{
        transform: `scale(${zoomScale})`,
        transformOrigin: "center center",
        width: "100%",
        height: "100%",
      }}
    >
      {/* Video content */}
    </div>
  );
};
```

#### 3. Integration with Video Sequence
The `ZoomEffect` component replaces the direct video rendering in the `Animated` component:

```typescript
<Animated
  style={calculateContainerStyles(details, crop, {
    overflow: "hidden",
  })}
  animationIn={animationIn}
  animationOut={animationOut}
  durationInFrames={durationInFrames}
>
  <ZoomEffect /> {/* ← Replaces direct video rendering */}
</Animated>
```

## 🎨 Animation Mathematics

### Zoom Scale Calculation
```typescript
// Progress: 0 to 1 over the zoom duration
const progress = (frame - zoomStartFrame) / zoomDurationFrames;

// Sine wave: creates smooth zoom in and out within the duration
// sin(0) = 0, sin(π/2) = 1, sin(π) = 0
// This ensures the zoom returns to normal scale (1.0) at the end
zoomScale = 1 + maxZoomScale * Math.sin(progress * Math.PI);
```

### Scale Values Over Time
- **0% progress** (start): `1 + 0.5 * sin(0) = 1.0` (normal size)
- **50% progress** (middle): `1 + 0.5 * sin(π/2) = 1.5` (maximum zoom)
- **100% progress** (end): `1 + 0.5 * sin(π) = 1.0` (back to normal)

## 🧪 Testing

### Test Files Created
1. **`test-zoom-effect.js`** - Full test with sample video (original hardcoded)
2. **`test-zoom-simple.js`** - Structure validation test
3. **`test-zoom-timing.js`** - Dynamic zoom timing functionality test

### Running Tests
```bash
# Start the render server
npm run render-server:dev

# Run the simple structure test
node test-zoom-simple.js

# Run the full video test (requires internet for sample video)
node test-zoom-effect.js

# Test dynamic zoom timing functionality
node test-zoom-timing.js
```

### Test Results
✅ Composition loads without errors  
✅ useCurrentFrame hook works properly  
✅ Transform calculations are valid  
✅ Render server processes the composition  

## 🎬 How to Use

### For End Users
1. Start the development server: `npm run dev`
2. Add any video file to your timeline
3. **Set zoom timing** (optional):
   - Seek to desired zoom start time
   - Click the first 🎯 Target button in the timeline header
   - Seek to desired zoom end time
   - Click the second 🎯 Target button in the timeline header
   - The duration display shows the zoom length
4. Export the video using the render functionality
5. The zoom effect will be applied with your custom timing (or default 1-4 seconds)

### For Developers
The zoom effect is automatically applied to all video track items. No additional configuration is needed.

## ⚙️ Customization Options

### Timing Adjustments
```typescript
// Change when zoom starts (currently 1 second)
const zoomStartFrame = fps * 2; // Start at 2 seconds

// Change zoom duration (currently 3 seconds)
const zoomDurationFrames = fps * 5; // 5 seconds duration
```

### Zoom Intensity
```typescript
// Change maximum zoom level (currently 1.5x)
zoomScale = 1 + 0.8 * Math.sin(progress * Math.PI); // 1.8x max zoom
zoomScale = 1 + 0.3 * Math.sin(progress * Math.PI); // 1.3x max zoom
```

### Animation Curves
```typescript
// Linear zoom (no easing)
zoomScale = 1 + 0.5 * progress;

// Ease in/out
zoomScale = 1 + 0.5 * (1 - Math.cos(progress * Math.PI)) / 2;

// Bounce effect
zoomScale = 1 + 0.5 * Math.abs(Math.sin(progress * Math.PI * 2));
```

### Transform Origin
```typescript
// Zoom from top-left
transformOrigin: "top left"

// Zoom from bottom-right
transformOrigin: "bottom right"

// Custom position (30% from left, 70% from top)
transformOrigin: "30% 70%"
```

## 🔄 Integration with Existing Features

### Compatibility
- ✅ Works with existing animations (animationIn/animationOut)
- ✅ Respects video cropping settings
- ✅ Maintains video aspect ratios
- ✅ Compatible with playback rate adjustments
- ✅ Works with volume controls

### Performance
- Minimal performance impact (simple mathematical calculation per frame)
- No external dependencies required
- Leverages Remotion's optimized rendering pipeline

## 🚀 New Features (v2.0)

### ✅ Implemented Features
1. **✅ Dynamic Configuration**: Zoom timing is now user-configurable via UI buttons
2. **✅ Timeline Integration**: Zoom controls integrated into timeline header
3. **✅ Real-time Feedback**: Duration display shows current zoom length
4. **✅ Validation**: Graceful handling of invalid timing configurations
5. **✅ Context Awareness**: Works in both Player and Remotion render contexts

### 🔮 Future Enhancements
1. **Multiple Zoom Points**: Support multiple zoom effects throughout the timeline
2. **Area Selection**: Allow users to specify zoom focus areas
3. **Zoom Types**: Add different zoom animation types (linear, bounce, elastic)
4. **Visual Timeline Markers**: Show zoom regions directly on the timeline ruler
5. **Zoom Intensity Control**: Adjustable maximum zoom level (currently fixed at 1.5x)

### Implementation Ideas
```typescript
// Future: Configurable zoom properties
interface ZoomConfig {
  startTime: number;
  duration: number;
  maxScale: number;
  focusPoint: { x: number; y: number };
  easing: 'sine' | 'linear' | 'bounce' | 'elastic';
}
```

## 📝 Notes

- The zoom effect is currently hardcoded and applies to all video exports
- Frame calculations are based on the composition's FPS setting
- The effect uses CSS transforms for optimal performance
- Transform origin is set to "center center" for natural zoom behavior

## 🐛 Troubleshooting

### Common Issues
1. **Zoom not visible**: Ensure video duration is longer than 4 seconds
2. **Jerky animation**: Check that FPS is consistent (30fps recommended)
3. **Render failures**: Verify Remotion dependencies are properly installed

### Debug Tips
```typescript
// Add logging to see zoom values
console.log(`Frame: ${frame}, Scale: ${zoomScale}`);
```

---

*This zoom effect implementation provides a foundation for more advanced zoom features in the React Video Editor project.*
