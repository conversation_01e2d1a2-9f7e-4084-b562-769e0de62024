import { Player } from "../player";
import { useRef, useEffect, useState } from "react";
import useStore from "../store/use-store";
import StateManager from "@designcombo/state";
import SceneEmpty from "./empty";
import Board from "./board";
import { SceneInteractions } from "./interactions";

export default function Scene({
  stateManager,
}: {
  stateManager: StateManager;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { size, trackItemIds } = useStore();
  const [scale, setScale] = useState(1);

  // Calculate scale to fit scene content within the container
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const PADDING = 96;
    const containerHeight = container.clientHeight - PADDING;
    const containerWidth = container.clientWidth - PADDING;
    const { width, height } = size;

    const scaleX = containerWidth / width;
    const scaleY = containerHeight / height;
    const desiredScale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%



    setScale(desiredScale);
  }, [size, containerRef]);

  // Handle container resize
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(() => {
      const PADDING = 96;
      const containerHeight = container.clientHeight - PADDING;
      const containerWidth = container.clientWidth - PADDING;
      const { width, height } = size;

      const scaleX = containerWidth / width;
      const scaleY = containerHeight / height;
      const desiredScale = Math.min(scaleX, scaleY, 1);



      setScale(desiredScale);
    });

    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, [size]);

  return (
    <div
      style={{
        width: "100%",
        height: "100%",
        position: "relative",
        flex: 1,
      }}
      ref={containerRef}
    >
      {trackItemIds.length === 0 && <SceneEmpty />}
      <div
        className="player-container bg-sidebar"
        style={{
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          overflow: "hidden",
        }}
      >
        <div
          style={{
            width: size.width * scale,
            height: size.height * scale,
            position: "relative",
          }}
        >
          <div
            style={{
              transform: `scale(${scale})`,
              transformOrigin: "top left",
              width: size.width,
              height: size.height,
            }}
          >
            <Board size={size}>
              <Player />
              <SceneInteractions
                stateManager={stateManager}
                containerRef={containerRef}
                size={size}
                scale={scale}
              />
            </Board>
          </div>
        </div>
      </div>
    </div>
  );
}
