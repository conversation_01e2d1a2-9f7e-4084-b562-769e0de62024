/**
 * CPU Monitor - Specialized utility for tracking CPU usage during video playback
 * This helps identify performance bottlenecks in the Remotion player
 */

interface CPUMetrics {
  timestamp: number;
  cpuUsage: number;
  frameRate: number;
  memoryUsage: number;
  activeOperations: string[];
}

interface VideoPlaybackState {
  isPlaying: boolean;
  isDragging: boolean;
  currentFrame: number;
  seekOperations: number;
  frameUpdates: number;
}

export class CPUMonitor {
  private static instance: CPUMonitor;
  private isMonitoring = false;
  private metrics: CPUMetrics[] = [];
  private videoState: VideoPlaybackState = {
    isPlaying: false,
    isDragging: false,
    currentFrame: 0,
    seekOperations: 0,
    frameUpdates: 0
  };
  
  private cpuCheckInterval: NodeJS.Timeout | null = null;
  private frameRateInterval: NodeJS.Timeout | null = null;
  private frameCount = 0;
  private lastFrameTime = 0;
  private currentOperations = new Set<string>();

  static getInstance(): CPUMonitor {
    if (!CPUMonitor.instance) {
      CPUMonitor.instance = new CPUMonitor();
    }
    return CPUMonitor.instance;
  }

  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.metrics = [];
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
    this.currentOperations.clear();
    
    console.log('🔥 CPU Monitor started - Tracking video playback performance');
    
    // Monitor CPU usage every 100ms
    this.cpuCheckInterval = setInterval(() => {
      this.sampleCPUUsage();
    }, 100);
    
    // Monitor frame rate every second
    this.frameRateInterval = setInterval(() => {
      this.calculateFrameRate();
    }, 1000);
  }

  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.cpuCheckInterval) {
      clearInterval(this.cpuCheckInterval);
      this.cpuCheckInterval = null;
    }
    
    if (this.frameRateInterval) {
      clearInterval(this.frameRateInterval);
      this.frameRateInterval = null;
    }
    
    this.generateReport();
  }

  // Track video playback state changes
  updateVideoState(state: Partial<VideoPlaybackState>) {
    this.videoState = { ...this.videoState, ...state };
    
    if (state.isDragging !== undefined) {
      console.log(`🎬 Video state: ${state.isDragging ? 'DRAGGING' : 'NORMAL'}`);
    }
  }

  // Track specific operations that might cause CPU spikes
  startOperation(operationName: string) {
    this.currentOperations.add(operationName);
    console.log(`🔧 Operation started: ${operationName}`);
  }

  endOperation(operationName: string) {
    this.currentOperations.delete(operationName);
    console.log(`✅ Operation completed: ${operationName}`);
  }

  // Track frame updates from Remotion
  trackFrameUpdate(frame: number) {
    this.videoState.currentFrame = frame;
    this.videoState.frameUpdates++;
    this.frameCount++;
  }

  // Track seek operations
  trackSeek() {
    this.videoState.seekOperations++;
  }

  private sampleCPUUsage() {
    if (!this.isMonitoring) return;
    
    const startTime = performance.now();
    
    // Use a CPU-intensive task to estimate CPU load
    setTimeout(() => {
      const endTime = performance.now();
      const delay = endTime - startTime;
      
      // Estimate CPU usage based on setTimeout delay
      // Normal delay should be ~1ms, higher delays indicate CPU load
      const estimatedCPU = Math.min(100, Math.max(0, (delay - 1) * 20));
      
      // Get memory usage
      const memoryUsage = (performance as any).memory ? 
        (performance as any).memory.usedJSHeapSize / 1024 / 1024 : 0;
      
      const metric: CPUMetrics = {
        timestamp: performance.now(),
        cpuUsage: estimatedCPU,
        frameRate: this.getCurrentFrameRate(),
        memoryUsage,
        activeOperations: Array.from(this.currentOperations)
      };
      
      this.metrics.push(metric);
      
      // Keep only last 100 samples to prevent memory issues
      if (this.metrics.length > 100) {
        this.metrics = this.metrics.slice(-100);
      }
      
      // Log high CPU usage immediately
      if (estimatedCPU > 70) {
        console.warn(`🚨 HIGH CPU: ${estimatedCPU.toFixed(1)}% - Operations: ${metric.activeOperations.join(', ')}`);
        console.warn(`   Video state: Playing=${this.videoState.isPlaying}, Dragging=${this.videoState.isDragging}, Frame=${this.videoState.currentFrame}`);
      }
    }, 1);
  }

  private calculateFrameRate() {
    const now = performance.now();
    const timeDiff = now - this.lastFrameTime;
    const frameRate = (this.frameCount / timeDiff) * 1000;
    
    this.frameCount = 0;
    this.lastFrameTime = now;
    
    return frameRate;
  }

  private getCurrentFrameRate(): number {
    const recentMetrics = this.metrics.slice(-5);
    if (recentMetrics.length === 0) return 0;
    
    return recentMetrics.reduce((sum, m) => sum + m.frameRate, 0) / recentMetrics.length;
  }

  private generateReport() {
    if (this.metrics.length === 0) {
      console.log('📊 No CPU metrics collected');
      return;
    }
    
    const avgCPU = this.metrics.reduce((sum, m) => sum + m.cpuUsage, 0) / this.metrics.length;
    const maxCPU = Math.max(...this.metrics.map(m => m.cpuUsage));
    const avgFrameRate = this.metrics.reduce((sum, m) => sum + m.frameRate, 0) / this.metrics.length;
    const avgMemory = this.metrics.reduce((sum, m) => sum + m.memoryUsage, 0) / this.metrics.length;
    
    console.log('\n🔥 CPU Monitor Report:');
    console.log(`📈 CPU Usage: Avg ${avgCPU.toFixed(1)}%, Max ${maxCPU.toFixed(1)}%`);
    console.log(`🎬 Frame Rate: Avg ${avgFrameRate.toFixed(1)} fps`);
    console.log(`💾 Memory: Avg ${avgMemory.toFixed(1)} MB`);
    console.log(`🎯 Video Metrics:`);
    console.log(`   • Frame Updates: ${this.videoState.frameUpdates}`);
    console.log(`   • Seek Operations: ${this.videoState.seekOperations}`);
    console.log(`   • Current Frame: ${this.videoState.currentFrame}`);
    
    // Identify performance issues
    const highCPUPeriods = this.metrics.filter(m => m.cpuUsage > 70);
    if (highCPUPeriods.length > 0) {
      console.log(`\n🚨 High CPU periods detected: ${highCPUPeriods.length}/${this.metrics.length} samples`);
      
      // Find common operations during high CPU
      const operationsDuringHighCPU = highCPUPeriods
        .flatMap(m => m.activeOperations)
        .reduce((acc, op) => {
          acc[op] = (acc[op] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
      
      console.log('🔍 Operations during high CPU:', operationsDuringHighCPU);
    }
    
    // Performance recommendations
    console.log('\n💡 Performance Analysis:');
    if (maxCPU > 80) {
      console.log('   🚨 CRITICAL: CPU usage exceeded 80%');
      console.log('   • Check if frame update throttling is working');
      console.log('   • Verify video rendering optimizations');
      console.log('   • Consider reducing video quality during playback');
    } else if (maxCPU > 60) {
      console.log('   ⚠️  WARNING: CPU usage exceeded 60%');
      console.log('   • Monitor for performance degradation');
    } else {
      console.log('   ✅ CPU usage within acceptable range');
    }
    
    if (avgFrameRate < 30) {
      console.log('   ⚠️  Low frame rate detected');
      console.log('   • Check for blocking operations');
      console.log('   • Verify frame update optimization');
    }
    
    // Export data for further analysis
    (window as any).cpuMetrics = this.metrics;
    console.log('\n📊 Raw metrics exported to window.cpuMetrics for analysis');
  }

  // Get current status for debugging
  getStatus() {
    const recent = this.metrics.slice(-5);
    const avgCPU = recent.length > 0 ? 
      recent.reduce((sum, m) => sum + m.cpuUsage, 0) / recent.length : 0;
    
    return {
      isMonitoring: this.isMonitoring,
      currentCPU: avgCPU,
      videoState: this.videoState,
      activeOperations: Array.from(this.currentOperations),
      metricsCount: this.metrics.length
    };
  }
}

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).cpuMonitor = CPUMonitor.getInstance();
}

export default CPUMonitor;
