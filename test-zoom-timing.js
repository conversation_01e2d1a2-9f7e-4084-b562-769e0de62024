#!/usr/bin/env node

/**
 * Test script to validate the dynamic zoom timing functionality
 * This tests the new zoom start/end button functionality
 */

import fetch from 'node-fetch';

const RENDER_SERVER_URL = 'http://localhost:3001';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testDynamicZoomTiming() {
  try {
    logInfo('Testing dynamic zoom timing functionality...');
    
    // Test with custom zoom timing: 2-6 seconds
    const customZoomTiming = {
      startTime: 2000, // 2 seconds
      endTime: 6000,   // 6 seconds
    };

    // Create a sample video track item for testing
    const sampleVideoItem = {
      id: 'test-video-dynamic-zoom',
      type: 'video',
      display: {
        from: 0,      // Start at beginning
        to: 8000,     // 8 seconds duration to see full effect
      },
      details: {
        src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
        width: 1920,
        height: 1080,
        volume: 50,
        left: 0,
        top: 0,
        transform: 'scale(1, 1)',
        crop: {
          x: 0,
          y: 0,
          width: 1920,
          height: 1080,
        }
      },
      trim: {
        from: 0,
        to: 8000,
      },
      playbackRate: 1,
      animations: {
        in: null,
        out: null,
      }
    };

    const renderRequest = {
      compositionId: 'Composition',
      inputProps: {
        trackItemIds: ['test-video-dynamic-zoom'],
        trackItemsMap: {
          'test-video-dynamic-zoom': sampleVideoItem
        },
        trackItemDetailsMap: {
          'test-video-dynamic-zoom': sampleVideoItem
        },
        transitionsMap: {},
        canvasSettings: {
          background: {
            type: 'solid',
            solidColor: '#000000',
            gradient: { type: 'linear', angle: 0, stops: [] },
            imageUrl: null,
            imageFile: null,
            imageObjectUrl: null,
          },
          padding: { value: 70, unit: 'px' },
          blur: { enabled: false, intensity: 0 },
          videoBorderRadius: { value: 0 },
          videoBackgroundShadow: {
            enabled: false,
            x: 0, y: 0, blur: 0, spread: 0,
            color: 'rgba(0, 0, 0, 0)',
          },
        },
        duration: 8000, // 8 seconds to see the full effect
        fps: 30,
        width: 1080,
        height: 1920,
        // Pass the custom zoom timing
        zoomTiming: customZoomTiming,
      },
      codec: 'h264',
      imageFormat: 'jpeg',
      quality: 80,
    };

    logInfo('Sending render request with custom zoom timing (2-6 seconds)...');
    const response = await fetch(`${RENDER_SERVER_URL}/render`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(renderRequest),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.renderId) {
        logSuccess(`Dynamic zoom timing render started: ${data.renderId}`);
        logInfo('The video should show:');
        logInfo('- Normal playback for the first 2 seconds');
        logInfo('- Smooth zoom in and out effect from 2-6 seconds');
        logInfo('- Normal playback for the last 2 seconds');
        logInfo(`Check the render output in: remotion-render-server/renders/`);
        return true;
      } else {
        logError(`Dynamic zoom render failed: ${data.error || 'Unknown error'}`);
        return false;
      }
    } else {
      logError(`Dynamic zoom render failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logError(`Dynamic zoom test failed: ${error.message}`);
    return false;
  }
}

async function testZoomTimingValidation() {
  try {
    logInfo('Testing zoom timing validation...');
    
    // Test with invalid timing (end before start)
    const invalidZoomTiming = {
      startTime: 5000, // 5 seconds
      endTime: 2000,   // 2 seconds (invalid - before start)
    };

    const renderRequest = {
      compositionId: 'Composition',
      inputProps: {
        trackItemIds: [],
        trackItemsMap: {},
        trackItemDetailsMap: {},
        transitionsMap: {},
        canvasSettings: {
          background: {
            type: 'solid',
            solidColor: '#1a1a1a',
            gradient: { type: 'linear', angle: 0, stops: [] },
            imageUrl: null,
            imageFile: null,
            imageObjectUrl: null,
          },
          padding: { value: 70, unit: 'px' },
          blur: { enabled: false, intensity: 0 },
          videoBorderRadius: { value: 0 },
          videoBackgroundShadow: {
            enabled: false,
            x: 0, y: 0, blur: 0, spread: 0,
            color: 'rgba(0, 0, 0, 0)',
          },
        },
        duration: 5000,
        fps: 30,
        width: 1080,
        height: 1920,
        zoomTiming: invalidZoomTiming,
      },
      codec: 'h264',
      imageFormat: 'jpeg',
      quality: 80,
    };

    const response = await fetch(`${RENDER_SERVER_URL}/render`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(renderRequest),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        logSuccess('Invalid zoom timing handled gracefully (no zoom applied)');
        return true;
      } else {
        logError(`Validation test failed: ${data.error}`);
        return false;
      }
    } else {
      logError(`Validation test failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Validation test failed: ${error.message}`);
    return false;
  }
}

async function checkServerHealth() {
  try {
    logInfo('Checking render server health...');
    const response = await fetch(`${RENDER_SERVER_URL}/health`);
    
    if (response.ok) {
      logSuccess('Render server is healthy');
      return true;
    } else {
      logError(`Server health check failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Cannot connect to render server: ${error.message}`);
    logError('Make sure the render server is running: npm run render-server:dev');
    return false;
  }
}

async function runDynamicZoomTests() {
  log(`${colors.bold}🎯 Dynamic Zoom Timing Tests${colors.reset}\n`);
  
  // Check server health first
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    process.exit(1);
  }
  
  console.log(); // Empty line
  
  // Test dynamic zoom timing
  const dynamicZoomPassed = await testDynamicZoomTiming();
  
  console.log(); // Empty line
  
  // Test validation
  const validationPassed = await testZoomTimingValidation();
  
  console.log(); // Empty line
  
  if (dynamicZoomPassed && validationPassed) {
    logSuccess('🎉 All dynamic zoom timing tests passed!');
    logInfo('Features working correctly:');
    logInfo('✓ Custom zoom start/end timing');
    logInfo('✓ Dynamic frame calculation');
    logInfo('✓ Invalid timing validation');
    logInfo('✓ Graceful fallback when zoom is disabled');
    logInfo('');
    logInfo('🎮 How to use in the UI:');
    logInfo('1. Open the video editor at http://localhost:5174');
    logInfo('2. Add a video to the timeline');
    logInfo('3. Seek to desired zoom start time');
    logInfo('4. Click the first Target button to set zoom start');
    logInfo('5. Seek to desired zoom end time');
    logInfo('6. Click the second Target button to set zoom end');
    logInfo('7. Export the video to see the custom zoom effect');
  } else {
    logError('❌ Some dynamic zoom timing tests failed');
    process.exit(1);
  }
}

// Run the tests
runDynamicZoomTests().catch(error => {
  logError(`Test suite failed: ${error.message}`);
  process.exit(1);
});
