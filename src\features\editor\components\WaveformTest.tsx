import React, { useRef, useEffect, useState } from 'react';
import { generateWaveformData } from '../utils/file';

interface WaveformTestProps {
  audioFile?: File;
  width?: number;
  height?: number;
}

const WaveformTest: React.FC<WaveformTestProps> = ({ 
  audioFile, 
  width = 400, 
  height = 60 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!audioFile) return;

    const generateWaveform = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const data = await generateWaveformData(audioFile, 200);
        setWaveformData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to generate waveform');
      } finally {
        setIsLoading(false);
      }
    };

    generateWaveform();
  }, [audioFile]);

  useEffect(() => {
    if (!canvasRef.current || !waveformData.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw waveform similar to our timeline implementation
    const centerY = height / 2;
    const maxAmplitude = height * 0.4;

    ctx.fillStyle = '#4b5563';
    ctx.strokeStyle = '#4b5563';
    ctx.lineWidth = 1;

    // Draw waveform as filled shape
    ctx.beginPath();

    // Draw top part
    for (let x = 0; x < width; x++) {
      const dataIndex = Math.floor((x / width) * waveformData.length);
      const amplitude = waveformData[dataIndex] || 0;
      const waveHeight = amplitude * maxAmplitude;
      const topY = centerY - waveHeight;

      if (x === 0) {
        ctx.moveTo(x, topY);
      } else {
        ctx.lineTo(x, topY);
      }
    }

    // Draw bottom part (reverse direction)
    for (let x = width - 1; x >= 0; x--) {
      const dataIndex = Math.floor((x / width) * waveformData.length);
      const amplitude = waveformData[dataIndex] || 0;
      const waveHeight = amplitude * maxAmplitude;
      const bottomY = centerY + waveHeight;

      ctx.lineTo(x, bottomY);
    }

    ctx.closePath();
    ctx.fill();

    // Draw center line
    ctx.strokeStyle = '#6b7280';
    ctx.lineWidth = 0.5;
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();

  }, [waveformData, width, height]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center" style={{ width, height }}>
        <div className="text-sm text-gray-500">Generating waveform...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center" style={{ width, height }}>
        <div className="text-sm text-red-500">Error: {error}</div>
      </div>
    );
  }

  if (!audioFile) {
    return (
      <div className="flex items-center justify-center border-2 border-dashed border-gray-300" style={{ width, height }}>
        <div className="text-sm text-gray-500">No audio file selected</div>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 rounded">
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="block"
      />
      <div className="p-2 text-xs text-gray-600">
        File: {audioFile.name} | Samples: {waveformData.length}
      </div>
    </div>
  );
};

export default WaveformTest;
