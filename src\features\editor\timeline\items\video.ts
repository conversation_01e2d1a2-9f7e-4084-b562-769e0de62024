import {
  Control,
  Pattern,
  Video as VideoBase,
  VideoProps as VideoPropsBase,
  timeMsToUnits,
  unitsToTimeMs,
} from "@designcombo/timeline";
import { Filmstrip, FilmstripBacklogOptions } from "../types";
import ThumbnailCache from "../../utils/thumbnail-cache";
import { IDisplay, IMetadata, ITrim } from "@designcombo/types";
import {
  calculateOffscreenSegments,
  calculateThumbnailSegmentLayout,
} from "../../utils/filmstrip";
import { fileToStream, getFileFromUrl } from "../../utils/file";
import { useLocalVideosStore } from "../../store/use-local-videos-store";
import { type MP4Clip } from "@designcombo/frames";
import { createMediaControls } from "../controls";
import useStore from "../../store/use-store";
import PerformanceMonitor from "../../debug/performance-monitor";

const EMPTY_FILMSTRIP: Filmstrip = {
  offset: 0,
  startTime: 0,
  thumbnailsCount: 0,
  widthOnScreen: 0,
};

interface VideoProps extends VideoPropsBase {
  aspectRatio: number;
  metadata: Partial<IMetadata> & {
    previewUrl: string;
    localVideoId?: string;
    fileName?: string;
  };
}
class Video extends VideoBase {
  static type = "Video";
  public clip?: MP4Clip | null;
  declare id: string;
  public resourceId: string = "";
  declare tScale: number;
  public isSelected = false;
  declare display: IDisplay;
  declare trim: ITrim;
  declare playbackRate: number;
  declare duration: number;
  public prevDuration: number;
  public itemType = "video";
  public metadata?: Partial<IMetadata>;
  declare src: string;

  public aspectRatio = 1;
  public scrollLeft = 0;
  public filmstripBacklogOptions?: FilmstripBacklogOptions;
  public thumbnailsPerSegment = 0;
  public segmentSize = 0;

  public offscreenSegments = 0;
  public thumbnailWidth: number = 0;
  public thumbnailHeight: number = 60; // Will be updated to actual track height
  public thumbnailsList: { url: string; ts: number }[] = [];
  public isFetchingThumbnails = false;
  public thumbnailCache = new ThumbnailCache();

  // Performance optimization: throttle thumbnail loading during dragging
  private thumbnailLoadTimeoutRef: NodeJS.Timeout | null = null;
  private lastThumbnailLoadTime = 0;
  private readonly THUMBNAIL_LOAD_THROTTLE_MS = 100; // Throttle thumbnail loading
  private wasPlayheadDragging = false; // Track previous dragging state
  private wasVideoSelecting = false; // Track previous video selection state

  public currentFilmstrip: Filmstrip = EMPTY_FILMSTRIP;
  public nextFilmstrip: Filmstrip = { ...EMPTY_FILMSTRIP, segmentIndex: 0 };
  public loadingFilmstrip: Filmstrip = EMPTY_FILMSTRIP;

  private offscreenCanvas: OffscreenCanvas | null = null;
  private offscreenCtx: OffscreenCanvasRenderingContext2D | null = null;

  private isDirty: boolean = true;

  private fallbackSegmentIndex: number = 0;
  private fallbackSegmentsCount: number = 0;
  private previewUrl: string = "";

  static createControls(): { controls: Record<string, Control> } {
    return { controls: createMediaControls() };
  }

  constructor(props: VideoProps) {
    super(props);
    this.id = props.id;
    this.tScale = props.tScale;
    this.objectCaching = false;
    this.rx = 4;
    this.ry = 4;
    this.display = props.display;
    this.trim = props.trim;
    this.duration = props.duration;
    this.prevDuration = props.duration;
    this.fill = "#e2e8f0"; // Light gray for light mode
    this.borderOpacityWhenMoving = 1;
    this.metadata = props.metadata;

    this.aspectRatio = props.aspectRatio;

    this.src = props.src;
    this.strokeWidth = 0;

    this.transparentCorners = false;
    this.hasBorders = false;

    this.previewUrl = props.metadata?.previewUrl;
    this.initOffscreenCanvas();
    this.initialize();
  }

  private initOffscreenCanvas() {
    if (!this.offscreenCanvas) {
      this.offscreenCanvas = new OffscreenCanvas(this.width, this.height);
      this.offscreenCtx = this.offscreenCanvas.getContext("2d");
    }

    // Resize if dimensions changed
    if (
      this.offscreenCanvas.width !== this.width ||
      this.offscreenCanvas.height !== this.height
    ) {
      this.offscreenCanvas.width = this.width;
      this.offscreenCanvas.height = this.height;
      this.isDirty = true;
    }
  }

  public initDimensions() {
    // Update thumbnail height to match actual track height
    this.thumbnailHeight = this.height;

    // Ensure we have valid aspect ratio and thumbnail width
    if (!isFinite(this.aspectRatio) || this.aspectRatio <= 0) {
      console.warn("Invalid aspect ratio, using default 16:9");
      this.aspectRatio = 16/9;
    }

    this.thumbnailWidth = this.thumbnailHeight * this.aspectRatio;

    // Ensure thumbnail width is valid
    if (!isFinite(this.thumbnailWidth) || this.thumbnailWidth <= 0) {
      console.warn("Invalid thumbnail width, using default");
      this.thumbnailWidth = Math.round(this.height * (16/9));
    }

    console.log("Initialized dimensions:", {
      thumbnailWidth: this.thumbnailWidth,
      thumbnailHeight: this.thumbnailHeight,
      aspectRatio: this.aspectRatio
    });

    const segmentOptions = calculateThumbnailSegmentLayout(this.thumbnailWidth);
    this.thumbnailsPerSegment = segmentOptions.thumbnailsPerSegment;
    this.segmentSize = segmentOptions.segmentSize;

    // Removed console.log for performance
  }

  public async initialize() {
    // Removed console.log for performance
    await this.loadFallbackThumbnail();

    this.initDimensions();
    this.onScrollChange({ scrollLeft: 0 });

    this.canvas?.requestRenderAll();

    this.createFallbackPattern();
    await this.prepareAssets();

    // Force initial render after assets are prepared
    this.isDirty = true;
    this.onScrollChange({ scrollLeft: 0, force: true });
    // Removed console.log for performance
  }

  public async prepareAssets() {
    if (typeof window === "undefined") return;

    try {
      const { MP4Clip } = await import("@designcombo/frames");

      // Check if this is a local video file
      const localVideoId = (this.metadata as any)?.localVideoId;
      if (localVideoId) {
        // Get the local video file from the store
        const localVideo = useLocalVideosStore.getState().actions.getVideoById(localVideoId);
        if (localVideo) {
          console.log("Loading local video file for thumbnails:", localVideo.name);
          const stream = await fileToStream(localVideo.file);
          this.clip = new MP4Clip(stream);
          console.log("MP4Clip created successfully for local video");
          return;
        }
      }

      // Enhanced fallback: support both blob URLs and regular URLs
      if (this.src.startsWith("blob:")) {
        // Handle blob URLs directly
        console.log("Loading blob URL for thumbnails:", this.src);
        const response = await fetch(this.src);
        const blob = await response.blob();
        const file = new File([blob], "video.mp4");
        const stream = await fileToStream(file);
        this.clip = new MP4Clip(stream);
        console.log("MP4Clip created successfully for blob URL");
      } else if (this.src.startsWith("http://") || this.src.startsWith("https://")) {
        // Handle regular URLs using getFileFromUrl (like preview example)
        console.log("Loading remote URL for thumbnails:", this.src);
        const file = await getFileFromUrl(this.src);
        const stream = await fileToStream(file);
        this.clip = new MP4Clip(stream);
        console.log("MP4Clip created successfully for remote URL");
      } else {
        console.warn("Video source is not a supported format:", this.src);
      }
    } catch (error) {
      console.error("Error loading MP4Clip:", error);
    }
  }

  private calculateFilmstripDimensions({
    segmentIndex,
    widthOnScreen,
  }: {
    segmentIndex: number;
    widthOnScreen: number;
  }) {
    // Handle invalid segment index
    if (!isFinite(segmentIndex) || segmentIndex < 0) {
      console.warn("Invalid segment index, using 0:", segmentIndex);
      segmentIndex = 0;
    }

    // Ensure we have valid dimensions
    if (!isFinite(this.segmentSize) || this.segmentSize <= 0) {
      console.warn("Invalid segment size, recalculating dimensions");
      this.initDimensions();
    }

    const filmstripOffset = segmentIndex * this.segmentSize;
    const shouldUseLeftBacklog = segmentIndex > 0;
    const leftBacklogSize = shouldUseLeftBacklog ? this.segmentSize : 0;

    const totalWidth = timeMsToUnits(
      this.duration,
      this.tScale,
      this.playbackRate,
    );

    const rightRemainingSize =
      totalWidth - widthOnScreen - leftBacklogSize - filmstripOffset;
    const rightBacklogSize = Math.min(this.segmentSize, rightRemainingSize);

    const filmstripStartTime = unitsToTimeMs(filmstripOffset, this.tScale);
    const filmstrimpThumbnailsCount = Math.max(0,
      1 +
      Math.round(
        (widthOnScreen + leftBacklogSize + rightBacklogSize) /
          this.thumbnailWidth,
      ));

    console.log("Calculated filmstrip dimensions:", {
      segmentIndex,
      filmstripOffset,
      filmstripStartTime,
      filmstrimpThumbnailsCount,
      thumbnailWidth: this.thumbnailWidth
    });

    return {
      filmstripOffset,
      leftBacklogSize,
      rightBacklogSize,
      filmstripStartTime,
      filmstrimpThumbnailsCount,
    };
  }

  // load fallback thumbnail, resize it and cache it
  private async loadFallbackThumbnail() {
    const fallbackThumbnail = this.previewUrl;
    if (!fallbackThumbnail) {
      console.log("No fallback thumbnail URL provided");
      return;
    }

    console.log("Loading fallback thumbnail:", fallbackThumbnail);
    return new Promise<void>((resolve) => {
      const img = new Image();
      img.crossOrigin = "anonymous";

      // Handle data URLs differently - don't add timestamp
      if (fallbackThumbnail.startsWith('data:')) {
        img.src = fallbackThumbnail;
      } else {
        img.src = fallbackThumbnail + "?t=" + Date.now();
      }

      img.onload = () => {
        console.log("Fallback thumbnail loaded successfully");
        // Create a temporary canvas to resize the image
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d")!;

        // Calculate new width maintaining aspect ratio
        const aspectRatio = img.width / img.height;
        const targetHeight = this.height; // Use actual track height
        const targetWidth = Math.round(targetHeight * aspectRatio);

        // Ensure we have valid dimensions
        if (!isFinite(aspectRatio) || aspectRatio <= 0 || targetWidth <= 0) {
          console.warn("Invalid aspect ratio, using default 16:9");
          this.aspectRatio = 16/9;
          this.thumbnailWidth = Math.round(this.height * (16/9));
        } else {
          this.aspectRatio = aspectRatio;
          this.thumbnailWidth = targetWidth;
        }

        // Set canvas size and draw resized image
        canvas.height = targetHeight;
        canvas.width = this.thumbnailWidth;
        ctx.drawImage(img, 0, 0, this.thumbnailWidth, targetHeight);

        // Create new image from resized canvas
        const resizedImg = new Image();
        resizedImg.src = canvas.toDataURL();
        resizedImg.onload = () => {
          // Cache the resized image
          this.thumbnailCache.setThumbnail("fallback", resizedImg);
          console.log("Fallback thumbnail cached with dimensions:", {
            width: this.thumbnailWidth,
            height: targetHeight,
            aspectRatio: this.aspectRatio
          });
          resolve();
        };
      };
      img.onerror = (error) => {
        console.error("Failed to load fallback thumbnail:", fallbackThumbnail, error);
        // Set default dimensions even if thumbnail fails
        this.aspectRatio = 16/9;
        this.thumbnailWidth = Math.round(this.height * (16/9));
        console.log("Using default dimensions:", { width: this.thumbnailWidth, aspectRatio: this.aspectRatio });
        resolve(); // Still resolve to continue initialization
      };
    });
  }

  private generateTimestamps(startTime: number, count: number): number[] {
    // Validate inputs
    if (!isFinite(startTime) || startTime < 0) {
      console.warn("Invalid start time, using 0:", startTime);
      startTime = 0;
    }

    if (!isFinite(count) || count <= 0) {
      console.warn("Invalid count, using 0:", count);
      return [];
    }

    const timePerThumbnail = unitsToTimeMs(
      this.thumbnailWidth,
      this.tScale,
      this.playbackRate,
    );

    if (!isFinite(timePerThumbnail) || timePerThumbnail <= 0) {
      console.warn("Invalid time per thumbnail:", timePerThumbnail);
      return [];
    }

    return Array.from({ length: count }, (_, i) => {
      const timeInFilmstripe = startTime + i * timePerThumbnail;
      return Math.ceil(timeInFilmstripe / 1000);
    });
  }

  private createFallbackPattern() {
    const canvas = this.canvas;
    if (!canvas) {
      console.log("No canvas available for fallback pattern");
      return;
    }

    const canvasWidth = this.canvas!.width;
    const maxPatternSize = 12000;
    const fallbackSource = this.thumbnailCache.getThumbnail("fallback");

    if (!fallbackSource) {
      console.log("No fallback thumbnail available for pattern creation");
      return;
    }

    console.log("Creating fallback pattern with source:", fallbackSource);

    // Compute the total width and number of segments needed
    const totalWidthNeeded = Math.min(canvasWidth * 20, maxPatternSize);
    const segmentsRequired = Math.ceil(totalWidthNeeded / this.segmentSize);
    this.fallbackSegmentsCount = segmentsRequired;
    const patternWidth = segmentsRequired * this.segmentSize;

    // Setup canvas dimensions
    const offCanvas = document.createElement("canvas");
    offCanvas.height = this.thumbnailHeight;
    offCanvas.width = patternWidth;

    const context = offCanvas.getContext("2d")!;
    const thumbnailsTotal = segmentsRequired * this.thumbnailsPerSegment;

    // Draw the fallback image across the entirety of the canvas horizontally
    for (let i = 0; i < thumbnailsTotal; i++) {
      const x = i * this.thumbnailWidth;
      context.drawImage(
        fallbackSource,
        x,
        0,
        this.thumbnailWidth,
        this.thumbnailHeight,
      );
    }

    // Create the pattern and apply it
    const fillPattern = new Pattern({
      source: offCanvas,
      repeat: "no-repeat",
      offsetX: 0,
    });

    this.set("fill", fillPattern);
    console.log("Fallback pattern created and applied, requesting render");
    this.isDirty = true; // Mark as dirty to ensure render
    this.canvas?.requestRenderAll();
  }
  // Throttled thumbnail loading to prevent performance issues during dragging
  private throttledLoadAndRenderThumbnails() {
    const now = Date.now();

    // Get global playhead dragging and video selection state
    const { isPlayheadDragging, isVideoSelecting } = useStore.getState();

    // Check if dragging or selecting just stopped - if so, force immediate refresh
    const justStoppedDragging = this.wasPlayheadDragging && !isPlayheadDragging;
    const justStoppedSelecting = this.wasVideoSelecting && !isVideoSelecting;
    this.wasPlayheadDragging = isPlayheadDragging;
    this.wasVideoSelecting = isVideoSelecting;

    if (justStoppedDragging || justStoppedSelecting) {
      // Don't force immediate refresh - let normal throttling handle it
      // This prevents video refresh when dropping the playhead or finishing selection
      console.log('🔍 DEBUG: Dragging or selecting stopped, using normal throttling instead of forced refresh');
    }

    // Use more aggressive throttling during playhead dragging
    const throttleMs = isPlayheadDragging ? 500 : this.THUMBNAIL_LOAD_THROTTLE_MS;

    // Clear any pending thumbnail load
    if (this.thumbnailLoadTimeoutRef) {
      clearTimeout(this.thumbnailLoadTimeoutRef);
      this.thumbnailLoadTimeoutRef = null;
    }

    // If enough time has passed, load immediately
    if (now - this.lastThumbnailLoadTime >= throttleMs) {
      this.lastThumbnailLoadTime = now;
      this.loadAndRenderThumbnails();
    } else {
      // Otherwise, schedule a load
      const delay = throttleMs - (now - this.lastThumbnailLoadTime);
      this.thumbnailLoadTimeoutRef = setTimeout(() => {
        this.lastThumbnailLoadTime = Date.now();
        this.loadAndRenderThumbnails();
        this.thumbnailLoadTimeoutRef = null;
      }, delay);
    }
  }

  public async loadAndRenderThumbnails() {
    const perfMonitor = PerformanceMonitor.getInstance();
    const perfStartTime = performance.now();
    perfMonitor.trackFunction('loadAndRenderThumbnails');

    if (this.isFetchingThumbnails || !this.clip) {
      console.log('🔍 DEBUG: Skipping thumbnail load - isFetching:', this.isFetchingThumbnails, 'hasClip:', !!this.clip);
      return;
    }

    // CRITICAL: Skip expensive thumbnail generation during playhead dragging or video selection
    const { isPlayheadDragging, isVideoSelecting } = useStore.getState();
    if (isPlayheadDragging || isVideoSelecting) {
      console.log('🔍 DEBUG: Skipping thumbnail load - playhead dragging or video selecting');
      // During dragging/selecting, just mark as dirty and return - thumbnails will update when operation stops
      this.isDirty = true;
      return;
    }

    console.log('🔍 DEBUG: Starting expensive thumbnail generation');

    // Removed console.log for performance
    // set segmentDrawn to segmentToDraw
    this.loadingFilmstrip = { ...this.nextFilmstrip };
    this.isFetchingThumbnails = true;

    // Calculate dimensions and offsets
    const { startTime, thumbnailsCount } = this.loadingFilmstrip;
    // Removed console.log for performance

    // Generate required timestamps
    const timestamps = this.generateTimestamps(startTime, thumbnailsCount);
    // Removed console.log for performance

    try {
      // Match and prepare thumbnails
      const thumbnailStartTime = performance.now();
      console.log('🔍 DEBUG: Calling MP4Clip.thumbnailsList with', timestamps.length, 'timestamps');

      let thumbnailsArr = await this.clip.thumbnailsList(this.thumbnailWidth, {
        timestamps: timestamps.map((timestamp) => timestamp * 1e6),
      });

      const thumbnailDuration = performance.now() - thumbnailStartTime;
      console.log('🔍 DEBUG: MP4Clip.thumbnailsList took', thumbnailDuration.toFixed(2), 'ms');
      perfMonitor.trackExpensiveOperation('MP4Clip.thumbnailsList', thumbnailStartTime);

      const updatedThumbnails = thumbnailsArr.map((thumbnail) => {
        return {
          ts: Math.round(thumbnail.ts / 1e6),
          img: thumbnail.img,
        };
      });

      // Load all thumbnails in parallel
      await this.loadThumbnailBatch(updatedThumbnails);

      this.isDirty = true; // Mark as dirty after preparing new thumbnails
      // this.isFallbackDirty = true;
      this.isFetchingThumbnails = false;

      this.currentFilmstrip = { ...this.loadingFilmstrip };

      console.log('🔍 DEBUG: Thumbnails loaded successfully');
      requestAnimationFrame(() => {
        // Only trigger render if not dragging or selecting to avoid performance issues
        const { isPlayheadDragging, isVideoSelecting } = useStore.getState();
        if (!isPlayheadDragging && !isVideoSelecting) {
          console.log('🔍 DEBUG: Requesting canvas render');
          this.canvas?.requestRenderAll();
        } else {
          console.log('🔍 DEBUG: Skipping canvas render - still dragging or selecting');
        }
      });

      const totalDuration = performance.now() - perfStartTime;
      console.log('🔍 DEBUG: Total loadAndRenderThumbnails took', totalDuration.toFixed(2), 'ms');
      perfMonitor.trackExpensiveOperation('loadAndRenderThumbnails', perfStartTime);
    } catch (error) {
      console.error("Error loading thumbnails:", error);
      this.isFetchingThumbnails = false;
    }
  }

  private async loadThumbnailBatch(thumbnails: { ts: number; img: Blob }[]) {
    const loadPromises = thumbnails.map(async (thumbnail) => {
      if (this.thumbnailCache.getThumbnail(thumbnail.ts)) return;

      return new Promise<void>((resolve) => {
        const img = new Image();
        img.src = URL.createObjectURL(thumbnail.img);
        img.onload = () => {
          URL.revokeObjectURL(img.src); // Clean up the blob URL after image loads
          this.thumbnailCache.setThumbnail(thumbnail.ts, img);
          resolve();
        };
      });
    });

    await Promise.all(loadPromises);
  }

  public _render(ctx: CanvasRenderingContext2D) {
    const perfMonitor = PerformanceMonitor.getInstance();
    const startTime = performance.now();
    perfMonitor.trackFunction('_render');
    perfMonitor.trackRenderCall();

    super._render(ctx);

    ctx.save();
    ctx.translate(-this.width / 2, -this.height / 2);

    // Clip the area to prevent drawing outside
    ctx.beginPath();
    ctx.rect(0, 0, this.width, this.height);
    ctx.clip();

    // Check if dragging or selecting just stopped and force refresh if needed
    const { isPlayheadDragging, isVideoSelecting } = useStore.getState();
    const justStoppedDragging = this.wasPlayheadDragging && !isPlayheadDragging;
    const justStoppedSelecting = this.wasVideoSelecting && !isVideoSelecting;

    if (isPlayheadDragging || isVideoSelecting) {
      console.log('🔍 DEBUG: _render called during dragging or selecting');
    }

    // Force render to offscreen if we have thumbnails to show or if dragging/selecting just stopped
    this.renderToOffscreen(true || justStoppedDragging || justStoppedSelecting);

    // Update tracking state for next render
    this.wasPlayheadDragging = isPlayheadDragging;
    this.wasVideoSelecting = isVideoSelecting;

    if (this.offscreenCanvas) {
      ctx.drawImage(this.offscreenCanvas, 0, 0);
    }

    ctx.restore();
    // this.drawTextIdentity(ctx);
    this.updateSelected(ctx);

    const duration = performance.now() - startTime;
    if (duration > 1) {
      console.log('🔍 DEBUG: _render took', duration.toFixed(2), 'ms');
    }
    perfMonitor.trackExpensiveOperation('_render', startTime);
  }

  public setDuration(duration: number) {
    this.duration = duration;
    this.prevDuration = duration;
  }

  public async setSrc(src: string) {
    super.setSrc(src);
    this.clip = null;
    await this.initialize();
    await this.prepareAssets();
    this.thumbnailCache.clearCacheButFallback();
    this.onScale();
  }
  public onResizeSnap() {
    this.renderToOffscreen(true);
  }
  public onResize() {
    this.renderToOffscreen(true);
  }

  public renderToOffscreen(force?: boolean) {
    if (!this.offscreenCtx) {
      // Removed console.log for performance
      return;
    }

    // Skip expensive rendering during playhead dragging or video selection unless forced
    const { isPlayheadDragging, isVideoSelecting } = useStore.getState();
    if ((isPlayheadDragging || isVideoSelecting) && !force) {
      return;
    }

    const cacheInfo = this.thumbnailCache.getCacheInfo();
    // Removed console.log for performance

    // Always render if forced, or if we have thumbnails to show
    const shouldRender = force || this.isDirty || cacheInfo.hasFallback;
    if (!shouldRender) {
      // Removed console.log for performance
      return;
    }

    this.offscreenCanvas!.width = this.width;
    const ctx = this.offscreenCtx;
    const { startTime, offset, thumbnailsCount } = this.currentFilmstrip;
    const thumbnailWidth = this.thumbnailWidth;
    const thumbnailHeight = this.thumbnailHeight;

    // Removed console.log for performance

    // Calculate the offset caused by the trimming
    const trimFromSize = timeMsToUnits(
      this.trim.from,
      this.tScale,
      this.playbackRate,
    );

    let timeInFilmstripe = startTime;
    const timePerThumbnail = unitsToTimeMs(
      thumbnailWidth,
      this.tScale,
      this.playbackRate,
    );

    // Clear the offscreen canvas
    ctx.clearRect(0, 0, this.width, this.height);

    // Clip with rounded corners
    ctx.beginPath();
    ctx.roundRect(0, 0, this.width, this.height, this.rx);
    ctx.clip();

    let renderedCount = 0;
    // Draw thumbnails
    for (let i = 0; i < thumbnailsCount; i++) {
      let img = this.thumbnailCache.getThumbnail(
        Math.ceil(timeInFilmstripe / 1000),
      );

      if (!img) {
        img = this.thumbnailCache.getThumbnail("fallback");
      }

      if (img && img.complete) {
        const xPosition = i * thumbnailWidth + offset - trimFromSize;
        ctx.drawImage(img, xPosition, 0, thumbnailWidth, thumbnailHeight);
        renderedCount++;
        timeInFilmstripe += timePerThumbnail;
      }
    }

    // Removed console.log for performance
    this.isDirty = false;
  }

  public drawTextIdentity(ctx: CanvasRenderingContext2D) {
    const iconPath = new Path2D(
      "M16.5625 0.925L12.5 3.275V0.625L11.875 0H0.625L0 0.625V9.375L0.625 10H11.875L12.5 9.375V6.875L16.5625 9.2125L17.5 8.625V1.475L16.5625 0.925ZM11.25 8.75H1.25V1.25H11.25V8.75ZM16.25 7.5L12.5 5.375V4.725L16.25 2.5V7.5Z",
    );
    ctx.save();
    ctx.translate(-this.width / 2, -this.height / 2);
    ctx.translate(0, 14);
    ctx.font = "600 12px 'Geist Variable'";
    ctx.fillStyle = "#f4f4f5";
    ctx.textAlign = "left";
    ctx.clip();
    ctx.fillText("Video", 36, 10);

    ctx.translate(8, 1);

    ctx.fillStyle = "#f4f4f5";
    ctx.fill(iconPath);
    ctx.restore();
  }

  public setSelected(selected: boolean) {
    this.isSelected = selected;
    this.set({ dirty: true });
  }

  public updateSelected(ctx: CanvasRenderingContext2D) {
    const borderColor = this.isSelected
      ? "rgba(255, 255, 255,1.0)"
      : "rgba(255, 255, 255,0.1)";
    ctx.save();
    ctx.beginPath();
    ctx.roundRect(
      -this.width / 2,
      -this.height / 2,
      this.width,
      this.height,
      6,
    );
    ctx.lineWidth = 1;
    ctx.strokeStyle = borderColor;
    ctx.stroke();
    ctx.restore();
  }

  public calulateWidthOnScreen() {
    const canvasEl = document.getElementById("designcombo-timeline-canvas");
    const canvasWidth = canvasEl?.clientWidth;
    const scrollLeft = this.scrollLeft;
    const timelineWidth = canvasWidth!;
    const cutFromBottomEdge = Math.max(
      timelineWidth - (this.width + this.left + scrollLeft),
      0,
    );
    const visibleHeight = Math.min(
      timelineWidth - this.left - scrollLeft,
      timelineWidth,
    );

    return Math.max(visibleHeight - cutFromBottomEdge, 0);
  }

  // Calculate the width that is not visible on the screen measured from the left
  public calculateOffscreenWidth({ scrollLeft }: { scrollLeft: number }) {
    const offscreenWidth = Math.min(this.left + scrollLeft, 0);

    return Math.abs(offscreenWidth);
  }

  public onScrollChange({
    scrollLeft,
    force,
  }: {
    scrollLeft: number;
    force?: boolean;
  }) {
    const perfMonitor = PerformanceMonitor.getInstance();
    const perfStartTime = performance.now();
    perfMonitor.trackFunction('onScrollChange');

    console.log('🔍 DEBUG: onScrollChange called - scrollLeft:', scrollLeft, 'force:', force, 'isDragging:', useStore.getState().isPlayheadDragging);

    const offscreenWidth = this.calculateOffscreenWidth({ scrollLeft });
    const trimFromSize = timeMsToUnits(
      this.trim.from,
      this.tScale,
      this.playbackRate,
    );

    const offscreenSegments = calculateOffscreenSegments(
      offscreenWidth,
      trimFromSize,
      this.segmentSize,
    );

    this.offscreenSegments = offscreenSegments;

    // calculate start segment to draw
    const segmentToDraw = offscreenSegments;

    if (this.currentFilmstrip.segmentIndex === segmentToDraw && !force) {
      // Removed console.log for performance
      return false;
    }

    if (segmentToDraw !== this.fallbackSegmentIndex) {
      const fillPattern = this.fill as Pattern;
      if (fillPattern instanceof Pattern) {
        fillPattern.offsetX =
          this.segmentSize *
          (segmentToDraw - Math.floor(this.fallbackSegmentsCount / 2));
      }

      this.fallbackSegmentIndex = segmentToDraw;
    }

    if (!this.isFetchingThumbnails || force) {
      this.scrollLeft = scrollLeft;
      const widthOnScreen = this.calulateWidthOnScreen();
      // With these lines:
      const { filmstripOffset, filmstripStartTime, filmstrimpThumbnailsCount } =
        this.calculateFilmstripDimensions({
          widthOnScreen: this.calulateWidthOnScreen(),
          segmentIndex: segmentToDraw,
        });

      this.nextFilmstrip = {
        segmentIndex: segmentToDraw,
        offset: filmstripOffset,
        startTime: filmstripStartTime,
        thumbnailsCount: filmstrimpThumbnailsCount,
        widthOnScreen,
      };

      console.log('🔍 DEBUG: Triggering throttled thumbnail loading');
      // Use throttled loading to prevent performance issues during dragging
      this.throttledLoadAndRenderThumbnails();
    } else {
      console.log('🔍 DEBUG: Skipping thumbnail loading - already fetching');
    }

    const duration = performance.now() - perfStartTime;
    if (duration > 1) {
      console.log('🔍 DEBUG: onScrollChange took', duration.toFixed(2), 'ms');
    }
    perfMonitor.trackExpensiveOperation('onScrollChange', perfStartTime);
  }
  public onScale() {
    this.currentFilmstrip = { ...EMPTY_FILMSTRIP };
    this.nextFilmstrip = { ...EMPTY_FILMSTRIP, segmentIndex: 0 };
    this.loadingFilmstrip = { ...EMPTY_FILMSTRIP };
    this.onScrollChange({ scrollLeft: this.scrollLeft, force: true });
  }

  // Cleanup method to prevent memory leaks
  public cleanup() {
    if (this.thumbnailLoadTimeoutRef) {
      clearTimeout(this.thumbnailLoadTimeoutRef);
      this.thumbnailLoadTimeoutRef = null;
    }
    this.thumbnailCache.clearCache();
  }
}

export default Video;
