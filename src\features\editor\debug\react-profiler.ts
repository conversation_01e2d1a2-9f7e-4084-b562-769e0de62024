/**
 * React Profiler - Tracks React component re-renders and performance
 * This helps identify if the CPU issue is caused by excessive React re-renders
 */

interface ComponentRenderMetrics {
  componentName: string;
  renderCount: number;
  totalRenderTime: number;
  averageRenderTime: number;
  lastRenderTime: number;
  slowRenders: number; // renders > 5ms
}

interface RenderEvent {
  componentName: string;
  timestamp: number;
  duration: number;
  phase: 'mount' | 'update';
  actualDuration: number;
  baseDuration: number;
}

export class ReactProfiler {
  private static instance: ReactProfiler;
  private isActive = false;
  private componentMetrics: Map<string, ComponentRenderMetrics> = new Map();
  private renderEvents: RenderEvent[] = [];
  private totalRenderTime = 0;
  private renderCount = 0;

  static getInstance(): ReactProfiler {
    if (!ReactProfiler.instance) {
      ReactProfiler.instance = new ReactProfiler();
    }
    return ReactProfiler.instance;
  }

  startProfiling() {
    this.isActive = true;
    this.componentMetrics.clear();
    this.renderEvents = [];
    this.totalRenderTime = 0;
    this.renderCount = 0;
    console.log('⚛️ React Profiler started - Tracking component re-renders');
  }

  stopProfiling() {
    this.isActive = false;
    this.generateReport();
    console.log('⚛️ React Profiler stopped');
  }

  // Track component render
  trackRender(
    componentName: string,
    phase: 'mount' | 'update',
    actualDuration: number,
    baseDuration: number = actualDuration
  ) {
    if (!this.isActive) return;

    const timestamp = performance.now();
    this.renderCount++;
    this.totalRenderTime += actualDuration;

    // Update component metrics
    const existing = this.componentMetrics.get(componentName);
    if (existing) {
      existing.renderCount++;
      existing.totalRenderTime += actualDuration;
      existing.averageRenderTime = existing.totalRenderTime / existing.renderCount;
      existing.lastRenderTime = timestamp;
      if (actualDuration > 5) {
        existing.slowRenders++;
      }
    } else {
      this.componentMetrics.set(componentName, {
        componentName,
        renderCount: 1,
        totalRenderTime: actualDuration,
        averageRenderTime: actualDuration,
        lastRenderTime: timestamp,
        slowRenders: actualDuration > 5 ? 1 : 0
      });
    }

    // Track render event
    const event: RenderEvent = {
      componentName,
      timestamp,
      duration: actualDuration,
      phase,
      actualDuration,
      baseDuration
    };

    this.renderEvents.push(event);

    // Log slow renders immediately
    if (actualDuration > 10) {
      console.warn(`⚛️ Slow render: ${componentName} took ${actualDuration.toFixed(2)}ms (${phase})`);
    }

    // Keep only last 500 events
    if (this.renderEvents.length > 500) {
      this.renderEvents = this.renderEvents.slice(-500);
    }
  }

  // Track when a component starts rendering (for manual timing)
  startComponentRender(componentName: string): () => void {
    if (!this.isActive) return () => {};

    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      this.trackRender(componentName, 'update', duration);
    };
  }

  private generateReport() {
    console.log('\n⚛️ React Performance Report:');
    console.log(`📊 Total Renders: ${this.renderCount}`);
    console.log(`⏱️  Total Render Time: ${this.totalRenderTime.toFixed(2)}ms`);
    console.log(`📈 Average Render Time: ${(this.totalRenderTime / this.renderCount).toFixed(2)}ms`);

    // Sort components by total render time
    const sortedComponents = Array.from(this.componentMetrics.values())
      .sort((a, b) => b.totalRenderTime - a.totalRenderTime);

    console.log('\n🔥 Most Expensive Components:');
    sortedComponents.slice(0, 10).forEach((comp, index) => {
      console.log(`${index + 1}. ${comp.componentName}:`);
      console.log(`   Renders: ${comp.renderCount}`);
      console.log(`   Total Time: ${comp.totalRenderTime.toFixed(2)}ms`);
      console.log(`   Avg Time: ${comp.averageRenderTime.toFixed(2)}ms`);
      console.log(`   Slow Renders: ${comp.slowRenders}`);
    });

    // Find components with excessive re-renders
    const excessiveRenders = sortedComponents.filter(comp => comp.renderCount > 50);
    if (excessiveRenders.length > 0) {
      console.log('\n⚠️ Components with Excessive Re-renders (>50):');
      excessiveRenders.forEach(comp => {
        console.log(`• ${comp.componentName}: ${comp.renderCount} renders`);
      });
    }

    // Find components with slow renders
    const slowComponents = sortedComponents.filter(comp => comp.slowRenders > 0);
    if (slowComponents.length > 0) {
      console.log('\n🐌 Components with Slow Renders (>5ms):');
      slowComponents.forEach(comp => {
        console.log(`• ${comp.componentName}: ${comp.slowRenders}/${comp.renderCount} slow renders`);
      });
    }

    // Analyze render patterns
    this.analyzeRenderPatterns();

    // Export data
    (window as any).reactProfileData = {
      componentMetrics: Array.from(this.componentMetrics.values()),
      renderEvents: this.renderEvents,
      totalRenderTime: this.totalRenderTime,
      renderCount: this.renderCount
    };

    console.log('\n📊 Full React profile exported to window.reactProfileData');
  }

  private analyzeRenderPatterns() {
    console.log('\n🔍 React Performance Analysis:');

    const recentEvents = this.renderEvents.slice(-50); // Last 50 renders
    const recentRenderTime = recentEvents.reduce((sum, event) => sum + event.duration, 0);
    const avgRecentRenderTime = recentRenderTime / recentEvents.length;

    console.log(`📈 Recent Render Performance: ${avgRecentRenderTime.toFixed(2)}ms avg`);

    if (avgRecentRenderTime > 5) {
      console.log('🚨 CRITICAL: Recent renders are slow (>5ms average)');
    }

    // Check for render storms (many renders in short time)
    const renderStorms = this.findRenderStorms();
    if (renderStorms.length > 0) {
      console.log(`⚡ Render Storms Detected: ${renderStorms.length}`);
      renderStorms.forEach((storm, index) => {
        console.log(`Storm ${index + 1}: ${storm.renderCount} renders in ${storm.duration.toFixed(2)}ms`);
      });
    }

    // CPU usage estimation from React renders
    const totalDuration = this.renderEvents.length > 0 ? 
      this.renderEvents[this.renderEvents.length - 1].timestamp - this.renderEvents[0].timestamp : 0;
    
    if (totalDuration > 0) {
      const reactCPUUsage = (this.totalRenderTime / totalDuration) * 100;
      console.log(`💻 React CPU Usage: ${reactCPUUsage.toFixed(1)}%`);
      
      if (reactCPUUsage > 30) {
        console.log('🚨 CRITICAL: React renders consuming >30% CPU');
        console.log('   Consider memoization, reducing re-renders, or optimizing components');
      }
    }

    console.log('\n💡 React Recommendations:');
    
    if (this.renderCount > 200) {
      console.log('   • High render count - consider React.memo for expensive components');
    }
    
    if (avgRecentRenderTime > 3) {
      console.log('   • Slow renders detected - profile individual components');
    }
    
    const excessiveComponents = Array.from(this.componentMetrics.values())
      .filter(comp => comp.renderCount > 30);
    
    if (excessiveComponents.length > 0) {
      console.log('   • Components re-rendering frequently:');
      excessiveComponents.forEach(comp => {
        console.log(`     - ${comp.componentName}: ${comp.renderCount} renders`);
      });
    }
  }

  private findRenderStorms(): Array<{renderCount: number, duration: number}> {
    const storms: Array<{renderCount: number, duration: number}> = [];
    const windowSize = 100; // 100ms window
    
    for (let i = 0; i < this.renderEvents.length - 5; i++) {
      const windowStart = this.renderEvents[i].timestamp;
      const windowEnd = windowStart + windowSize;
      
      const rendersInWindow = this.renderEvents.filter(event => 
        event.timestamp >= windowStart && event.timestamp <= windowEnd
      );
      
      if (rendersInWindow.length > 10) { // More than 10 renders in 100ms
        storms.push({
          renderCount: rendersInWindow.length,
          duration: windowSize
        });
        i += rendersInWindow.length; // Skip ahead to avoid overlapping storms
      }
    }
    
    return storms;
  }

  getStatus() {
    return {
      isActive: this.isActive,
      renderCount: this.renderCount,
      totalRenderTime: this.totalRenderTime,
      componentCount: this.componentMetrics.size,
      recentEvents: this.renderEvents.slice(-5)
    };
  }
}

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).reactProfiler = ReactProfiler.getInstance();
}

export default ReactProfiler;
