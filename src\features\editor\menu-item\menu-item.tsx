import useLayoutStore from "../store/use-layout-store";
import { LocalMedia } from "./local-media";

const ActiveMenuItem = () => {
  const { activeMenuItem } = useLayoutStore();

  if (activeMenuItem === "media" || activeMenuItem === "videos" || activeMenuItem === "audios" || activeMenuItem === "canvas") {
    return <LocalMedia />;
  }

  return null;
};

export const MenuItem = () => {
  return (
    <div className="w-[300px] flex-1">
      <ActiveMenuItem />
    </div>
  );
};
