import React from 'react';
import { Composition } from 'remotion';
import { VideoEditorComposition } from './VideoEditorComposition';
import { ITrackItem, ITransition } from '@designcombo/types';
import { CanvasSettings } from '../features/editor/store/use-canvas-store';
import { IZoomConfig } from '../features/editor/store/use-zoom-store';

interface IZoomTiming {
  startTime: number; // in milliseconds
  endTime: number;   // in milliseconds
}

interface RemotionRootProps {
  trackItemIds?: string[];
  trackItemsMap?: Record<string, ITrackItem>;
  trackItemDetailsMap?: Record<string, any>;
  transitionsMap?: Record<string, ITransition>;
  canvasSettings?: CanvasSettings;
  duration?: number;
  fps?: number;
  width?: number;
  height?: number;
  zoomTiming?: IZoomTiming;
  zoomConfig?: IZoomConfig;
}

export const RemotionRoot: React.FC<RemotionRootProps> = ({
  trackItemIds = [],
  trackItemsMap = {},
  trackItemDetailsMap = {},
  transitionsMap = {},
  canvasSettings,
  duration = 10000, // 10 seconds in ms
  fps = 30,
  width = 1080,
  height = 1920,
  zoomTiming,
  zoomConfig,
}) => {
  // Calculate duration in frames
  const durationInFrames = Math.ceil((duration / 1000) * fps);

  // Default canvas settings if not provided
  const defaultCanvasSettings: CanvasSettings = {
    background: {
      type: 'solid' as const,
      solidColor: '#000000',
      gradient: {
        type: 'linear' as const,
        angle: 0,
        stops: []
      },
      imageUrl: null,
      imageFile: null,
      imageObjectUrl: null,
    },
    padding: {
      value: 70,
      unit: 'px' as const,
    },
    blur: {
      enabled: false,
      intensity: 0,
    },
    videoBorderRadius: {
      value: 0,
    },
    videoBackgroundShadow: {
      enabled: false,
      x: 0,
      y: 0,
      blur: 0,
      spread: 0,
      color: 'rgba(0, 0, 0, 0)',
    },
  };

  // Create a wrapper component that matches Remotion's expected signature
  const CompositionComponent: React.FC<Record<string, unknown>> = (inputProps) => {
    return (
      <VideoEditorComposition
        canvasSettings={canvasSettings || defaultCanvasSettings}
        width={width}
        height={height}
        fps={fps}
        durationInFrames={durationInFrames}
        trackItemIds={trackItemIds}
        trackItemsMap={trackItemsMap}
        trackItemDetailsMap={trackItemDetailsMap}
        transitionsMap={transitionsMap}
        zoomTiming={zoomTiming}
        zoomConfig={zoomConfig}
        {...inputProps}
      />
    );
  };

  return (
    <>
      <Composition
        id="Composition"
        component={CompositionComponent}
        durationInFrames={durationInFrames}
        fps={fps}
        width={width}
        height={height}
      />
    </>
  );
};
