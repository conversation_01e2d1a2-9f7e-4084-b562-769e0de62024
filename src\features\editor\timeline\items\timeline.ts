import TimelineBase from "@designcombo/timeline";
import Video from "./video";
import Audio from "./audio";
import { throttle } from "lodash";
import { TimelineOptions } from "@designcombo/timeline";
import { ITimelineScaleState } from "@designcombo/types";
import useStore from "../../store/use-store";

class Timeline extends TimelineBase {
  public isShiftKey: boolean = false;
  constructor(
    canvasEl: HTMLCanvasElement,
    options: Partial<TimelineOptions> & {
      scale: ITimelineScaleState;
      duration: number;
      guideLineColor?: string;
    },
  ) {
    super(canvasEl, options); // Call the parent class constructor

    // Add shift keyboard listener
    window.addEventListener("keydown", this.handleKeyDown);
    window.addEventListener("keyup", this.handleKeyUp);
  }

  private handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Shift") {
      this.isShiftKey = true;
    }
  };

  private handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === "Shift") {
      this.isShiftKey = false;
    }
  };

  public purge(): void {
    super.purge();

    // Cleanup event listener for Shift key
    window.removeEventListener("keydown", this.handleKeyDown);
    window.removeEventListener("keyup", this.handleKeyUp);

    // Cleanup scroll change timeout
    if (this.scrollChangeTimeoutRef) {
      clearTimeout(this.scrollChangeTimeoutRef);
      this.scrollChangeTimeoutRef = null;
    }
  }

  public setViewportPos(posX: number, posY: number) {
    const limitedPos = this.getViewportPos(posX, posY);
    const vt = this.viewportTransform;
    vt[4] = limitedPos.x;
    vt[5] = limitedPos.y;
    this.requestRenderAll();
    this.setActiveTrackItemCoords();
    this.onScrollChange();

    this.onScroll?.({
      scrollTop: limitedPos.y,
      scrollLeft: limitedPos.x - this.spacing.left,
    });
  }

  // Dynamic throttling based on playhead dragging state
  private scrollChangeTimeoutRef: NodeJS.Timeout | null = null;
  private lastScrollChangeTime = 0;

  public onScrollChange = () => {
    const now = Date.now();
    const isPlayheadDragging = useStore.getState().isPlayheadDragging;

    // Use more aggressive throttling during playhead dragging
    const throttleMs = isPlayheadDragging ? 500 : 250;

    // Clear any pending scroll change
    if (this.scrollChangeTimeoutRef) {
      clearTimeout(this.scrollChangeTimeoutRef);
      this.scrollChangeTimeoutRef = null;
    }

    // If enough time has passed, execute immediately
    if (now - this.lastScrollChangeTime >= throttleMs) {
      this.lastScrollChangeTime = now;
      this.executeScrollChange();
    } else {
      // Otherwise, schedule execution
      const delay = throttleMs - (now - this.lastScrollChangeTime);
      this.scrollChangeTimeoutRef = setTimeout(() => {
        this.lastScrollChangeTime = Date.now();
        this.executeScrollChange();
        this.scrollChangeTimeoutRef = null;
      }, delay);
    }
  };

  private executeScrollChange = async () => {
    const objects = this.getObjects();
    const viewportTransform = this.viewportTransform;
    const scrollLeft = viewportTransform[4];
    for (const object of objects) {
      if (object instanceof Video || object instanceof Audio) {
        object.onScrollChange({ scrollLeft });
      }
    }
  };

  public scrollTo({
    scrollLeft,
    scrollTop,
  }: {
    scrollLeft?: number;
    scrollTop?: number;
  }): void {
    const vt = this.viewportTransform; // Create a shallow copy
    let hasChanged = false;

    if (typeof scrollLeft === "number") {
      vt[4] = -scrollLeft + this.spacing.left;
      hasChanged = true;
    }
    if (typeof scrollTop === "number") {
      vt[5] = -scrollTop;
      hasChanged = true;
    }

    if (hasChanged) {
      this.viewportTransform = vt;
      this.getActiveObject()?.setCoords();
      this.onScrollChange();
      this.requestRenderAll();
    }
  }
}

export default Timeline;
