<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dynamic Scene Sizing</title>
</head>
<body>
    <h1>Test Dynamic Scene Sizing</h1>
    <p>This is a simple test to verify the video metadata extraction functionality.</p>
    
    <input type="file" id="videoInput" accept="video/*" />
    <div id="output"></div>

    <script>
        // Simulate the getVideoMetadata function
        function getVideoMetadata(file) {
            return new Promise((resolve, reject) => {
                const video = document.createElement("video");
                
                video.onloadedmetadata = () => {
                    const width = video.videoWidth;
                    const height = video.videoHeight;
                    const duration = video.duration * 1000; // Convert to milliseconds
                    const aspectRatio = width / height;
                    
                    resolve({
                        width,
                        height,
                        duration,
                        aspectRatio,
                    });
                    
                    URL.revokeObjectURL(video.src);
                };
                
                video.onerror = () => {
                    reject(new Error("Could not load video metadata"));
                    URL.revokeObjectURL(video.src);
                };
                
                video.src = URL.createObjectURL(file);
                video.load();
            });
        }

        document.getElementById('videoInput').addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (file && file.type.startsWith('video/')) {
                try {
                    const metadata = await getVideoMetadata(file);
                    document.getElementById('output').innerHTML = `
                        <h2>Video Metadata:</h2>
                        <p><strong>File:</strong> ${file.name}</p>
                        <p><strong>Width:</strong> ${metadata.width}px</p>
                        <p><strong>Height:</strong> ${metadata.height}px</p>
                        <p><strong>Aspect Ratio:</strong> ${metadata.aspectRatio.toFixed(2)}</p>
                        <p><strong>Duration:</strong> ${(metadata.duration / 1000).toFixed(2)}s</p>
                        <p><strong>Scene Size:</strong> ${metadata.width}x${metadata.height}</p>
                    `;
                    console.log('Video metadata extracted:', metadata);
                } catch (error) {
                    document.getElementById('output').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
                    console.error('Error extracting video metadata:', error);
                }
            }
        });
    </script>
</body>
</html>
