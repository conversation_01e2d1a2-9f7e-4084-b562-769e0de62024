import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Download, X } from 'lucide-react';

export interface ExportOptionsConfig {
  format: 'mp4' | 'webm';
  quality: 'low' | 'medium' | 'high';
}

interface ExportOptionsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: ExportOptionsConfig) => void;
  isExporting?: boolean;
}

export const ExportOptionsDialog: React.FC<ExportOptionsDialogProps> = ({
  isOpen,
  onClose,
  onExport,
  isExporting = false,
}) => {
  const [format, setFormat] = useState<'mp4' | 'webm'>('mp4');
  const [quality, setQuality] = useState<'low' | 'medium' | 'high'>('high');

  const handleExport = () => {
    onExport({ format, quality });
  };

  const getQualityDescription = (quality: string) => {
    switch (quality) {
      case 'low':
        return 'Smaller file size, faster export';
      case 'medium':
        return 'Balanced quality and file size';
      case 'high':
        return 'Best quality, larger file size';
      default:
        return '';
    }
  };

  const getFormatDescription = (format: string) => {
    switch (format) {
      case 'mp4':
        return 'Most compatible format, works everywhere';
      case 'webm':
        return 'Modern format, smaller file size';
      default:
        return '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Video
          </DialogTitle>
          <DialogDescription>
            Choose your export settings. The video will include all visual effects currently visible in the canvas.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-2">
            <Label htmlFor="format">Format</Label>
            <Select value={format} onValueChange={(value: 'mp4' | 'webm') => setFormat(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mp4">MP4</SelectItem>
                <SelectItem value="webm">WebM</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {getFormatDescription(format)}
            </p>
          </div>

          {/* Quality Selection */}
          <div className="space-y-2">
            <Label htmlFor="quality">Quality</Label>
            <Select value={quality} onValueChange={(value: 'low' | 'medium' | 'high') => setQuality(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select quality" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {getQualityDescription(quality)}
            </p>
          </div>

          {/* Export Preview Info */}
          <div className="rounded-md bg-muted p-3">
            <h4 className="text-sm font-medium mb-2">Export Preview</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>• Background: {format.toUpperCase()} format</div>
              <div>• Quality: {quality} compression</div>
              <div>• Effects: All canvas effects included</div>
              <div>• Output: What you see is what you get</div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose} disabled={isExporting}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Start Export'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ExportOptionsDialog;
