import { create } from "zustand";
import { generateId } from "@designcombo/timeline";

export interface LocalImage {
  id: string;
  file?: File;
  name: string;
  objectUrl: string;
  width: number;
  height: number;
  type: "image";
  isPreloaded?: boolean; // For images from public folder
}

interface LocalImagesState {
  images: LocalImage[];
  isLoading: boolean;
  actions: {
    addImage: (file: File) => Promise<LocalImage>;
    addPreloadedImage: (imagePath: string, name: string) => LocalImage;
    removeImage: (id: string) => void;
    clearAll: () => void;
    getImageById: (id: string) => LocalImage | undefined;
    initializePreloadedImages: () => void;
  };
}

// Preloaded images from public folder
// Use absolute URLs that work in both development and render contexts
const RENDER_SERVER_URL = 'http://localhost:3001';
const PRELOADED_IMAGES = [
  { path: `${RENDER_SERVER_URL}/image_20250707_012001_0.png`, name: "Image 1" },
  { path: `${RENDER_SERVER_URL}/image_20250707_012112_0.png`, name: "Image 2" },
  { path: `${RENDER_SERVER_URL}/image_20250707_012127_0.png`, name: "Image 3" },
  { path: `${RENDER_SERVER_URL}/image_20250707_012144_0.png`, name: "Image 4" },
  { path: `${RENDER_SERVER_URL}/image_20250707_012230_0.png`, name: "Image 5" },
  { path: `${RENDER_SERVER_URL}/image_20250707_012317_0.png`, name: "Image 6" },
  { path: `${RENDER_SERVER_URL}/image_20250707_012613_0.png`, name: "Image 7" },
  { path: `${RENDER_SERVER_URL}/image_20250707_013638_0.png`, name: "Image 8" },
  { path: `${RENDER_SERVER_URL}/image_20250707_013943_0.png`, name: "Image 9" },
  { path: `${RENDER_SERVER_URL}/image_20250707_015835_0.png`, name: "Image 10" },
  { path: `${RENDER_SERVER_URL}/image_20250707_015934_0.png`, name: "Image 11" },
  { path: `${RENDER_SERVER_URL}/image_20250707_012532_0.png`, name: "Image 12" },
];

const getImageDimensions = (src: string): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = reject;
    img.src = src;
  });
};

export const useLocalImagesStore = create<LocalImagesState>((set, get) => ({
  images: [],
  isLoading: false,

  actions: {
    addImage: async (file: File): Promise<LocalImage> => {
      set({ isLoading: true });
      
      try {
        const objectUrl = URL.createObjectURL(file);
        const dimensions = await getImageDimensions(objectUrl);
        
        const localImage: LocalImage = {
          id: generateId(),
          file,
          name: file.name,
          objectUrl,
          width: dimensions.width,
          height: dimensions.height,
          type: "image",
          isPreloaded: false,
        };

        set((state) => ({
          images: [...state.images, localImage],
          isLoading: false,
        }));

        return localImage;
      } catch (error) {
        set({ isLoading: false });
        throw new Error(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    addPreloadedImage: (imagePath: string, name: string): LocalImage => {
      const localImage: LocalImage = {
        id: generateId(),
        name,
        objectUrl: imagePath,
        width: 0, // Will be set when image loads
        height: 0, // Will be set when image loads
        type: "image",
        isPreloaded: true,
      };

      // Get dimensions asynchronously and update
      getImageDimensions(imagePath).then((dimensions) => {
        set((state) => ({
          images: state.images.map((img) =>
            img.id === localImage.id
              ? { ...img, width: dimensions.width, height: dimensions.height }
              : img
          ),
        }));
      }).catch(console.error);

      set((state) => ({
        images: [...state.images, localImage],
      }));

      return localImage;
    },

    removeImage: (id: string) => {
      const image = get().images.find((img) => img.id === id);
      if (image && !image.isPreloaded && image.objectUrl) {
        try {
          URL.revokeObjectURL(image.objectUrl);
        } catch (error) {
          console.warn("Failed to revoke object URL:", error);
        }
      }

      set((state) => ({
        images: state.images.filter((img) => img.id !== id),
      }));
    },

    clearAll: () => {
      const { images } = get();
      // Clean up object URLs for uploaded images
      images.forEach((image) => {
        if (!image.isPreloaded && image.objectUrl) {
          try {
            URL.revokeObjectURL(image.objectUrl);
          } catch (error) {
            console.warn("Failed to revoke object URL:", error);
          }
        }
      });

      set({ images: [] });
    },

    getImageById: (id: string) => {
      return get().images.find((img) => img.id === id);
    },

    initializePreloadedImages: () => {
      const { images, actions } = get();
      
      // Only add preloaded images if they haven't been added yet
      const hasPreloadedImages = images.some(img => img.isPreloaded);
      if (!hasPreloadedImages) {
        PRELOADED_IMAGES.forEach(({ path, name }) => {
          actions.addPreloadedImage(path, name);
        });
      }
    },
  },
}));
