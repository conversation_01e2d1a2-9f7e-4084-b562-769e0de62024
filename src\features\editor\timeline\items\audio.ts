import {
  Control,
  Video as VideoBase,
  VideoProps as VideoPropsBase,
  timeMsToUnits,
  unitsToTimeMs,
} from "@designcombo/timeline";
import { IDisplay, IMetadata, ITrim } from "@designcombo/types";
import { useLocalAudiosStore } from "../../store/use-local-audios-store";
import { createAudioControls } from "../controls";

interface AudioProps extends VideoPropsBase {
  metadata: Partial<IMetadata> & {
    localAudioId?: string;
    fileName?: string;
    waveformData?: number[];
  };
}

class Audio extends VideoBase {
  static type = "Audio";
  declare id: string;
  public resourceId: string = "";
  declare tScale: number;
  public isSelected = false;
  declare display: IDisplay;
  declare trim: ITrim;
  declare playbackRate: number;
  declare duration: number;
  public prevDuration: number;
  public itemType = "audio";
  public metadata?: Partial<IMetadata>;
  declare src: string;

  public scrollLeft = 0;
  public waveformData: number[] = [];
  
  private offscreenCanvas: OffscreenCanvas | null = null;
  private offscreenCtx: OffscreenCanvasRenderingContext2D | null = null;
  private isDirty: boolean = true;

  static createControls(): { controls: Record<string, Control> } {
    return { controls: createAudioControls() };
  }

  constructor(props: AudioProps) {
    super(props);
    this.id = props.id;
    this.tScale = props.tScale;
    this.objectCaching = false;
    this.rx = 4;
    this.ry = 4;
    this.display = props.display;
    this.trim = props.trim;
    this.duration = props.duration;
    this.prevDuration = props.duration;
    this.fill = "#e5e7eb"; // Slightly darker grey background for audio tracks
    this.borderOpacityWhenMoving = 1;
    this.metadata = props.metadata;
    this.src = props.src;
    this.strokeWidth = 0;
    this.transparentCorners = false;
    this.hasBorders = false;

    // Load waveform data
    this.waveformData = props.metadata?.waveformData || [];

    // If no waveform data, try to find it in the local store by matching src URL
    if (!this.waveformData.length) {
      this.loadWaveformFromLocalStore();
    }

    this.initOffscreenCanvas();
    this.initialize();
  }

  private initOffscreenCanvas() {
    if (typeof window === "undefined") return;
    
    this.offscreenCanvas = new OffscreenCanvas(800, 36);
    this.offscreenCtx = this.offscreenCanvas.getContext("2d");
  }

  public async initialize() {
    this.initDimensions();
    this.onScrollChange({ scrollLeft: 0 });
    this.canvas?.requestRenderAll();
  }

  private initDimensions() {
    const duration = this.duration;
    const width = timeMsToUnits(duration, this.tScale, this.playbackRate);
    this.set({ width, height: 36 });
  }

  public onScrollChange({ scrollLeft }: { scrollLeft: number }) {
    this.scrollLeft = scrollLeft;
    this.isDirty = true;
    this.canvas?.requestRenderAll();
  }

  public onScale() {
    this.initDimensions();
    this.isDirty = true;
    // Force re-render when scale changes to update waveform resolution
    this.renderToOffscreen(true);
    this.canvas?.requestRenderAll();
  }

  private renderToOffscreen(forceRender = false) {
    if (!this.offscreenCtx || !this.offscreenCanvas) return;
    if (!this.isDirty && !forceRender) return;

    const ctx = this.offscreenCtx;
    const canvas = this.offscreenCanvas;
    
    // Resize canvas if needed
    if (canvas.width !== this.width || canvas.height !== this.height) {
      canvas.width = this.width;
      canvas.height = this.height;
    }

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw waveform
    this.drawWaveform(ctx);

    this.isDirty = false;
  }

  private drawWaveform(ctx: OffscreenCanvasRenderingContext2D) {
    if (!this.waveformData.length) {
      // Draw placeholder bars if no waveform data
      this.drawPlaceholderWaveform(ctx);
      return;
    }

    const width = this.width;
    const height = this.height;
    const centerY = height / 2;
    const maxAmplitude = height * 0.4; // Use 40% of height for max amplitude

    ctx.fillStyle = "#4b5563"; // Dark grey color for waveform

    // Use the same approach as local audio preview - sample the waveform data across the width
    const samplesPerPixel = Math.max(1, Math.floor(this.waveformData.length / width));

    for (let x = 0; x < width; x++) {
      // Calculate which sample(s) to use for this pixel
      const sampleStart = Math.floor((x / width) * this.waveformData.length);
      const sampleEnd = Math.min(sampleStart + samplesPerPixel, this.waveformData.length);

      // Get the maximum amplitude in this range (for better visual representation)
      let maxAmplitudeInRange = 0;
      for (let i = sampleStart; i < sampleEnd; i++) {
        maxAmplitudeInRange = Math.max(maxAmplitudeInRange, this.waveformData[i] || 0);
      }

      const barHeight = maxAmplitudeInRange * maxAmplitude;
      const topY = centerY - barHeight;

      // Draw a 1-pixel wide bar
      ctx.fillRect(x, topY, 1, barHeight * 2);
    }

    // Add a subtle center line
    ctx.strokeStyle = "#6b7280";
    ctx.lineWidth = 0.3;
    ctx.globalAlpha = 0.7;
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
    ctx.globalAlpha = 1;
  }

  private drawPlaceholderWaveform(ctx: OffscreenCanvasRenderingContext2D) {
    const width = this.width;
    const height = this.height;
    const centerY = height / 2;
    const maxAmplitude = height * 0.3;

    ctx.fillStyle = "#4b5563"; // Dark grey color for placeholder waveform

    // Create placeholder bars similar to real waveform style
    const barCount = Math.min(Math.floor(width / 2), 100);
    const barWidth = width / barCount;

    for (let i = 0; i < barCount; i++) {
      // Use sine waves with some randomness for more realistic appearance
      const baseWave = Math.sin((i / barCount) * Math.PI * 6) * 0.5;
      const noise = (Math.random() - 0.5) * 0.4;
      const amplitude = Math.abs(baseWave + noise) * maxAmplitude;

      const x = i * barWidth;
      const topY = centerY - amplitude;
      const actualBarWidth = Math.max(barWidth - 0.5, 0.5);

      ctx.fillRect(x, topY, actualBarWidth, amplitude * 2);
    }

    // Add center line
    ctx.strokeStyle = "#6b7280";
    ctx.lineWidth = 0.5;
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
  }

  public _render(ctx: CanvasRenderingContext2D) {
    super._render(ctx);

    ctx.save();
    ctx.translate(-this.width / 2, -this.height / 2);

    // Clip the area to prevent drawing outside
    ctx.beginPath();
    ctx.rect(0, 0, this.width, this.height);
    ctx.clip();

    this.renderToOffscreen();

    if (this.offscreenCanvas) {
      ctx.drawImage(this.offscreenCanvas, 0, 0);
    }

    ctx.restore();
    this.updateSelected(ctx);
  }

  public updateSelected(ctx: CanvasRenderingContext2D) {
    if (this.isSelected) {
      ctx.save();
      ctx.translate(-this.width / 2, -this.height / 2);
      ctx.strokeStyle = "#00d8d6";
      ctx.lineWidth = 2;
      ctx.strokeRect(0, 0, this.width, this.height);
      ctx.restore();
    }
  }

  public setDuration(duration: number) {
    this.duration = duration;
    this.prevDuration = duration;
    this.initDimensions();
    this.isDirty = true;
    this.canvas?.requestRenderAll();
  }

  public setTrim(trim: ITrim) {
    this.trim = trim;
    this.isDirty = true;
    this.canvas?.requestRenderAll();
  }

  public updateWaveformData(waveformData: number[]) {
    this.waveformData = waveformData;
    this.isDirty = true;
    this.canvas?.requestRenderAll();
  }

  public async setSrc(src: string) {
    super.setSrc(src);
    await this.initialize();
  }

  public onResizeSnap() {
    this.renderToOffscreen(true);
  }

  private loadWaveformFromLocalStore() {
    // Access the local audio store to get waveform data
    const localAudiosStore = useLocalAudiosStore.getState();
    const localAudio = localAudiosStore.audios.find(audio => audio.objectUrl === this.src);

    if (localAudio?.waveformData) {
      this.waveformData = localAudio.waveformData;
      this.isDirty = true;
      this.canvas?.requestRenderAll();
    }
  }
}

export default Audio;
