import { CallbackListener, PlayerRef } from "@remotion/player";
import { useCallback, useSyncExternalStore, useRef } from "react";
import useStore from "../store/use-store";
import PerformanceMonitor from "../debug/performance-monitor";
import CPUMonitor from "../debug/cpu-monitor";
import RemotionProfiler from "../debug/remotion-profiler";

export const useCurrentPlayerFrame = (ref: React.RefObject<PlayerRef>) => {
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateTimeRef = useRef(0);
  const THROTTLE_MS = 16; // ~60fps max update rate

  const subscribe = useCallback(
    (onStoreChange: () => void) => {
      const { current } = ref;
      if (!current) {
        return () => undefined;
      }

      const updater: CallbackListener<"frameupdate"> = () => {
        const frameUpdateStart = performance.now();

        // Track frame update for performance monitoring
        const currentFrame = current.getCurrentFrame();
        PerformanceMonitor.getInstance().trackFrameUpdate(currentFrame);
        CPUMonitor.getInstance().trackFrameUpdate(currentFrame);
        RemotionProfiler.getInstance().startFrameUpdate(currentFrame);

        // Log detailed frame update info for debugging
        console.log(`🎬 Frame Update: ${currentFrame} (${(performance.now() - frameUpdateStart).toFixed(2)}ms)`);

        // Check if playhead is being dragged
        const isPlayheadDragging = useStore.getState().isPlayheadDragging;

        if (isPlayheadDragging) {
          // During dragging, throttle updates heavily to reduce CPU usage
          const now = Date.now();
          if (now - lastUpdateTimeRef.current < THROTTLE_MS) {
            // Clear any pending update
            if (throttleTimeoutRef.current) {
              clearTimeout(throttleTimeoutRef.current);
            }

            // Schedule a throttled update
            throttleTimeoutRef.current = setTimeout(() => {
              lastUpdateTimeRef.current = Date.now();
              onStoreChange();
              throttleTimeoutRef.current = null;
            }, THROTTLE_MS);
            return;
          }
          lastUpdateTimeRef.current = now;
        }

        // Normal update (not dragging or throttle time passed)
        const storeChangeStart = performance.now();
        onStoreChange();
        const storeChangeDuration = performance.now() - storeChangeStart;

        // End frame update tracking
        RemotionProfiler.getInstance().endFrameUpdate(currentFrame, storeChangeDuration);

        if (storeChangeDuration > 5) {
          console.warn(`⚠️ Slow store change: ${storeChangeDuration.toFixed(2)}ms for frame ${currentFrame}`);
        }
      };

      current.addEventListener("frameupdate", updater);
      return () => {
        current.removeEventListener("frameupdate", updater);
        // Clean up any pending throttled update
        if (throttleTimeoutRef.current) {
          clearTimeout(throttleTimeoutRef.current);
          throttleTimeoutRef.current = null;
        }
      };
    },
    [ref],
  );

  const data = useSyncExternalStore<number>(
    subscribe,
    () => ref.current?.getCurrentFrame() ?? 0,
    () => 0,
  );
  return data;
};
