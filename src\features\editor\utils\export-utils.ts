import useStore from '../store/use-store';
import { useCanvasStore } from '../store/use-canvas-store';
import { RemotionRoot } from '../../../remotion/Root';
import { ITrackItem, ITransition } from '@designcombo/types';
import { CanvasSettings } from '../store/use-canvas-store';

export interface ExportCompositionData {
  trackItemIds: string[];
  trackItemsMap: Record<string, ITrackItem>;
  trackItemDetailsMap: Record<string, any>;
  transitionsMap: Record<string, ITransition>;
  canvasSettings: CanvasSettings;
  duration: number;
  fps: number;
  width: number;
  height: number;
}

/**
 * Gets the current timeline state for export
 */
export function getCurrentTimelineState(): ExportCompositionData {
  const {
    trackItemIds,
    trackItemsMap,
    trackItemDetailsMap,
    transitionsMap,
    duration,
    fps,
    size,
  } = useStore.getState();

  const { settings } = useCanvasStore.getState();

  return {
    trackItemIds,
    trackItemsMap,
    trackItemDetailsMap,
    transitionsMap,
    canvasSettings: settings,
    duration,
    fps,
    width: size.width,
    height: size.height,
  };
}

/**
 * Creates a RemotionRoot component with current timeline data
 */
export function createExportComposition() {
  const timelineState = getCurrentTimelineState();
  
  return RemotionRoot({
    trackItemIds: timelineState.trackItemIds,
    trackItemsMap: timelineState.trackItemsMap,
    trackItemDetailsMap: timelineState.trackItemDetailsMap,
    transitionsMap: timelineState.transitionsMap,
    canvasSettings: timelineState.canvasSettings,
    duration: timelineState.duration,
    fps: timelineState.fps,
    width: timelineState.width,
    height: timelineState.height,
  });
}

/**
 * Hook to get current timeline state that updates when store changes
 */
export function useCurrentTimelineState(): ExportCompositionData {
  const {
    trackItemIds,
    trackItemsMap,
    trackItemDetailsMap,
    transitionsMap,
    duration,
    fps,
    size,
  } = useStore();

  const { settings } = useCanvasStore();

  return {
    trackItemIds,
    trackItemsMap,
    trackItemDetailsMap,
    transitionsMap,
    canvasSettings: settings,
    duration,
    fps,
    width: size.width,
    height: size.height,
  };
}
