import React, { useState, useCallback } from "react";
import { <PERSON>rollA<PERSON> } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileUploader } from "@/components/ui/file-uploader";
import ColorPicker from "@/components/color-picker";
import { useCanvasStore, DEFAULT_COLORS, DEFAULT_GRADIENTS, BackgroundType } from "../store/use-canvas-store";
import { useLocalImagesStore, LocalImage } from "../store/use-local-images-store";
import { Palette, Image, Blend, RotateCcw } from "lucide-react";
import { Icons } from "@/components/shared/icons";

export const Canvas = () => {
  const [shadowSettingsExpanded, setShadowSettingsExpanded] = useState<boolean>(false);
  const [blurSettingsExpanded, setBlurSettingsExpanded] = useState<boolean>(false);

  const {
    settings,
    setBackgroundType,
    setSolidColor,
    setGradient,
    setBackgroundImage,
    setPadding,
    setBlurEnabled,
    setBlurIntensity,
    setVideoBorderRadius,

    setVideoBackgroundShadowEnabled,
    setVideoBackgroundShadowX,
    setVideoBackgroundShadowY,
    setVideoBackgroundShadowBlur,
    setVideoBackgroundShadowSpread,
    setVideoBackgroundShadowColor,
    resetSettings,
  } = useCanvasStore();

  const { images } = useLocalImagesStore();

  const handleAddImageToTimeline = useCallback(
    (localImage: LocalImage) => {
      setBackgroundImage(localImage.file || null, localImage.objectUrl);
      setBackgroundType("image");
    },
    [setBackgroundImage, setBackgroundType]
  );

  // Auto-select first image when images are loaded and background type is image but no image is selected
  React.useEffect(() => {
    if (
      settings.background.type === "image" &&
      images.length > 0 &&
      !settings.background.imageObjectUrl
    ) {
      const firstImage = images[0];
      handleAddImageToTimeline(firstImage);
    }
  }, [images, settings.background.type, settings.background.imageObjectUrl, handleAddImageToTimeline]);

  const handleImageUpload = (files: File[]) => {
    if (files.length > 0) {
      const file = files[0];
      const objectUrl = URL.createObjectURL(file);
      setBackgroundImage(file, objectUrl);
      setBackgroundType("image");
    }
  };



  const handlePaddingChange = (value: number[]) => {
    setPadding(value[0]);
  };

  const handleBlurChange = (value: number[]) => {
    setBlurIntensity(value[0]);
  };

  const handleBorderRadiusChange = (value: number[]) => {
    setVideoBorderRadius(value[0]);
  };



  // Video Background Shadow Handlers
  const handleShadowXChange = (value: number[]) => {
    setVideoBackgroundShadowX(value[0]);
  };

  const handleShadowYChange = (value: number[]) => {
    setVideoBackgroundShadowY(value[0]);
  };

  const handleShadowBlurChange = (value: number[]) => {
    setVideoBackgroundShadowBlur(value[0]);
  };

  const handleShadowSpreadChange = (value: number[]) => {
    setVideoBackgroundShadowSpread(value[0]);
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="text-text-primary flex h-12 flex-none items-center px-4 text-sm font-medium">
        Canvas Background
      </div>
      
      <ScrollArea className="flex-1">
        <div className="space-y-6 p-4">
          {/* Canvas settings are always enabled */}
          {/* Background Type Tabs */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Background Type</Label>
                <Tabs
                  value={settings.background.type}
                  onValueChange={(value) => {
                    const backgroundType = value as BackgroundType;
                    setBackgroundType(backgroundType);

                    // Auto-select first image when image tab is clicked
                    if (backgroundType === "image" && images.length > 0 && !settings.background.imageObjectUrl) {
                      const firstImage = images[0];
                      handleAddImageToTimeline(firstImage);
                    }
                  }}
                  className="w-full"
                >
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="gradient" className="flex items-center gap-1">
                      <Blend className="h-3 w-3" />
                      Gradient
                    </TabsTrigger>
                    <TabsTrigger value="solid" className="flex items-center gap-1">
                      <Palette className="h-3 w-3" />
                      Solid
                    </TabsTrigger>
                    <TabsTrigger value="image" className="flex items-center gap-1">
                      <Image className="h-3 w-3" />
                      Image
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="solid" className="space-y-3 mt-4">
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Color Picker</Label>
                      <ColorPicker
                        value={settings.background.solidColor}
                        onChange={setSolidColor}
                        solid={true}
                        gradient={false}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Quick Colors</Label>
                      <div className="grid grid-cols-5 gap-2">
                        {DEFAULT_COLORS.map((color) => (
                          <button
                            key={color}
                            onClick={() => setSolidColor(color)}
                            className={`h-8 w-8 rounded border-2 transition-all hover:scale-110 ${
                              settings.background.solidColor === color 
                                ? "border-primary ring-2 ring-primary/20" 
                                : "border-border"
                            }`}
                            style={{ backgroundColor: color }}
                            title={color}
                          />
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="gradient" className="space-y-3 mt-4">
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Gradient Presets</Label>
                      <div className="grid grid-cols-4 gap-2">
                        {DEFAULT_GRADIENTS.map((gradient, index) => {
                          const gradientStyle = gradient.type === "linear"
                            ? `linear-gradient(${gradient.angle}deg, ${gradient.stops.map(stop => `${stop.color} ${stop.position}%`).join(", ")})`
                            : `radial-gradient(circle, ${gradient.stops.map(stop => `${stop.color} ${stop.position}%`).join(", ")})`;

                          return (
                            <button
                              key={index}
                              onClick={() => setGradient(gradient)}
                              className={`aspect-square w-full rounded border-2 transition-all hover:scale-105 ${
                                JSON.stringify(settings.background.gradient) === JSON.stringify(gradient)
                                  ? "border-primary ring-2 ring-primary/20"
                                  : "border-border"
                              }`}
                              style={{ background: gradientStyle }}
                            />
                          );
                        })}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="image" className="space-y-3 mt-4">
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Upload Background Image</Label>
                      <FileUploader
                        onValueChange={handleImageUpload}
                        accept={{
                          "image/*": [".jpg", ".jpeg", ".png", ".gif", ".webp"],
                        }}
                        maxSize={10 * 1024 * 1024} // 10MB
                        maxFileCount={1}
                        multiple={false}
                        className="h-24"
                      />
                    </div>

                  </TabsContent>
                </Tabs>
              </div>

              {/* Background Blur Controls - Available for all background types */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="blur-enabled" className="text-sm font-medium">
                    Background Blur
                  </Label>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setBlurSettingsExpanded(!blurSettingsExpanded)}
                      disabled={!settings.blur.enabled}
                    >
                      <Icons.settings className="h-4 w-4" />
                    </Button>
                    <Switch
                      id="blur-enabled"
                      checked={settings.blur.enabled}
                      onCheckedChange={setBlurEnabled}
                    />
                  </div>
                </div>

                {settings.blur.enabled && blurSettingsExpanded && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs text-muted-foreground">
                        Blur Intensity
                      </Label>
                      <span className="text-xs text-muted-foreground">
                        {settings.blur.intensity}px
                      </span>
                    </div>
                    <Slider
                      value={[settings.blur.intensity]}
                      onValueChange={handleBlurChange}
                      max={20}
                      min={0}
                      step={1}
                      className="w-full"
                    />
                    <Input
                      type="number"
                      value={settings.blur.intensity}
                      onChange={(e) => setBlurIntensity(Number(e.target.value))}
                      className="h-8 text-xs"
                      min={0}
                      max={20}
                    />
                  </div>
                )}
              </div>

              {/* Padding Controls */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Padding</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs text-muted-foreground">
                      Padding Amount
                    </Label>
                    <span className="text-xs text-muted-foreground">
                      {settings.padding.value}{settings.padding.unit}
                    </span>
                  </div>
                  <Slider
                    value={[settings.padding.value]}
                    onValueChange={handlePaddingChange}
                    max={200}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      value={settings.padding.value}
                      onChange={(e) => setPadding(Number(e.target.value))}
                      className="flex-1 h-8 text-xs"
                      min={0}
                      max={200}
                    />
                    <span className="text-xs text-muted-foreground self-center">px</span>
                  </div>
                </div>
              </div>

              {/* Video Border Radius Controls */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Video Border Radius</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs text-muted-foreground">
                      Corner Roundness
                    </Label>
                    <span className="text-xs text-muted-foreground">
                      {settings.videoBorderRadius.value}px
                    </span>
                  </div>
                  <Slider
                    value={[settings.videoBorderRadius.value]}
                    onValueChange={handleBorderRadiusChange}
                    max={50}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      value={settings.videoBorderRadius.value}
                      onChange={(e) => setVideoBorderRadius(Number(e.target.value))}
                      className="flex-1 h-8 text-xs"
                      min={0}
                      max={50}
                    />
                    <span className="text-xs text-muted-foreground self-center">px</span>
                  </div>
                </div>
              </div>



              {/* Video Background Shadow Controls */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="shadow-enabled" className="text-sm font-medium">
                    Video Background Shadow
                  </Label>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setShadowSettingsExpanded(!shadowSettingsExpanded)}
                      disabled={!settings.videoBackgroundShadow.enabled}
                    >
                      <Icons.settings className="h-4 w-4" />
                    </Button>
                    <Switch
                      id="shadow-enabled"
                      checked={settings.videoBackgroundShadow.enabled}
                      onCheckedChange={setVideoBackgroundShadowEnabled}
                    />
                  </div>
                </div>

                {settings.videoBackgroundShadow.enabled && shadowSettingsExpanded && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs text-muted-foreground">
                            X Offset
                          </Label>
                          <span className="text-xs text-muted-foreground">
                            {settings.videoBackgroundShadow.x}px
                          </span>
                        </div>
                        <Slider
                          value={[settings.videoBackgroundShadow.x]}
                          onValueChange={handleShadowXChange}
                          max={50}
                          min={-50}
                          step={1}
                          className="w-full"
                        />
                        <Input
                          type="number"
                          value={settings.videoBackgroundShadow.x}
                          onChange={(e) => setVideoBackgroundShadowX(Number(e.target.value))}
                          className="h-8 text-xs"
                          min={-50}
                          max={50}
                        />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs text-muted-foreground">
                            Y Offset
                          </Label>
                          <span className="text-xs text-muted-foreground">
                            {settings.videoBackgroundShadow.y}px
                          </span>
                        </div>
                        <Slider
                          value={[settings.videoBackgroundShadow.y]}
                          onValueChange={handleShadowYChange}
                          max={50}
                          min={-50}
                          step={1}
                          className="w-full"
                        />
                        <Input
                          type="number"
                          value={settings.videoBackgroundShadow.y}
                          onChange={(e) => setVideoBackgroundShadowY(Number(e.target.value))}
                          className="h-8 text-xs"
                          min={-50}
                          max={50}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs text-muted-foreground">
                            Blur Radius
                          </Label>
                          <span className="text-xs text-muted-foreground">
                            {settings.videoBackgroundShadow.blur}px
                          </span>
                        </div>
                        <Slider
                          value={[settings.videoBackgroundShadow.blur]}
                          onValueChange={handleShadowBlurChange}
                          max={50}
                          min={0}
                          step={1}
                          className="w-full"
                        />
                        <Input
                          type="number"
                          value={settings.videoBackgroundShadow.blur}
                          onChange={(e) => setVideoBackgroundShadowBlur(Number(e.target.value))}
                          className="h-8 text-xs"
                          min={0}
                          max={50}
                        />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs text-muted-foreground">
                            Spread
                          </Label>
                          <span className="text-xs text-muted-foreground">
                            {settings.videoBackgroundShadow.spread}px
                          </span>
                        </div>
                        <Slider
                          value={[settings.videoBackgroundShadow.spread]}
                          onValueChange={handleShadowSpreadChange}
                          max={20}
                          min={0}
                          step={1}
                          className="w-full"
                        />
                        <Input
                          type="number"
                          value={settings.videoBackgroundShadow.spread}
                          onChange={(e) => setVideoBackgroundShadowSpread(Number(e.target.value))}
                          className="h-8 text-xs"
                          min={0}
                          max={20}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">
                        Shadow Color
                      </Label>
                      <ColorPicker
                        value={settings.videoBackgroundShadow.color}
                        onChange={setVideoBackgroundShadowColor}
                        solid={true}
                        gradient={false}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Reset Button */}
              <div className="pt-2 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetSettings}
                  className="w-full flex items-center gap-2"
                >
                  <RotateCcw className="h-3 w-3" />
                  Reset to Default
                </Button>
              </div>
        </div>
      </ScrollArea>
    </div>
  );
};
