import useStore from "../store/use-store";
import { SequenceItem } from "./sequence-item";
import { merge } from "lodash";
import { groupTrackItems } from "../utils/track-items";
import { CanvasContainer } from "./canvas-container";
import { memo, useMemo } from "react";
import { shallow } from "zustand/shallow";

const Composition = memo(() => {
  // Use a selector to exclude activeIds and other UI-only state to prevent unnecessary re-renders
  const {
    trackItemIds,
    trackItemsMap,
    fps,
    trackItemDetailsMap,
    transitionsMap,
    isZoomPositioningActive,
    setIsZoomPositioningActive,
  } = useStore((state) => ({
    trackItemIds: state.trackItemIds,
    trackItemsMap: state.trackItemsMap,
    fps: state.fps,
    trackItemDetailsMap: state.trackItemDetailsMap,
    transitionsMap: state.transitionsMap,
    isZoomPositioningActive: state.isZoomPositioningActive,
    setIsZoomPositioningActive: state.setIsZoomPositioningActive,
  }), shallow);

  // Memoize expensive operations
  const mergedTrackItemsDeatilsMap = useMemo(() =>
    merge(trackItemsMap, trackItemDetailsMap),
    [trackItemsMap, trackItemDetailsMap]
  );

  const groupedItems = useMemo(() => groupTrackItems({
    trackItemIds,
    transitionsMap,
    trackItemsMap: mergedTrackItemsDeatilsMap,
  }), [trackItemIds, transitionsMap, mergedTrackItemsDeatilsMap]);

  return (
    <CanvasContainer
      isZoomPositioningActive={isZoomPositioningActive}
      onCloseZoomPositioning={() => setIsZoomPositioningActive(false)}
    >
      <div
        style={{
          pointerEvents: isZoomPositioningActive ? 'none' : 'auto',
          width: '100%',
          height: '100%'
        }}
      >
        {groupedItems.map((group) => {
          if (group.length === 1) {
            const item = mergedTrackItemsDeatilsMap[group[0].id];
            return SequenceItem[item.type](item, {
              fps,
            });
          }
          return null;
        })}
      </div>
    </CanvasContainer>
  );
});

export default Composition;
