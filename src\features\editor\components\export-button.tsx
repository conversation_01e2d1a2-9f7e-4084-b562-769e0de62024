import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, Loader2, AlertCircle } from 'lucide-react';
import { ExportDialog } from './export-dialog';
import { useVideoExport } from '../hooks/use-video-export';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ExportButtonProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  showText?: boolean;
}

export function ExportButton({ 
  variant = 'default', 
  size = 'default', 
  className,
  showText = true 
}: ExportButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const { isExporting, serverStatus } = useVideoExport();

  const getButtonContent = () => {
    if (isExporting) {
      return (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          {showText && <span className="ml-2">Exporting...</span>}
        </>
      );
    }

    if (serverStatus === 'offline') {
      return (
        <>
          <AlertCircle className="h-4 w-4" />
          {showText && <span className="ml-2">Server Offline</span>}
        </>
      );
    }

    return (
      <>
        <Download className="h-4 w-4" />
        {showText && <span className="ml-2">Export Video</span>}
      </>
    );
  };

  const getTooltipContent = () => {
    if (isExporting) return 'Export in progress...';
    if (serverStatus === 'offline') return 'Render server is offline. Please start the server first.';
    if (serverStatus === 'checking') return 'Checking server status...';
    return 'Export your video project';
  };

  const isDisabled = isExporting || serverStatus === 'offline';

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className={className}
            onClick={() => setDialogOpen(true)}
            disabled={isDisabled}
          >
            {getButtonContent()}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipContent()}</p>
        </TooltipContent>
      </Tooltip>

      <ExportDialog 
        open={dialogOpen} 
        onOpenChange={setDialogOpen} 
      />
    </TooltipProvider>
  );
}
