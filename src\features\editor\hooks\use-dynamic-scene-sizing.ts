import { useEffect, useRef } from "react";
import { filter, subject, dispatch } from "@designcombo/events";
import { ADD_VIDEO, EDIT_OBJECT } from "@designcombo/state";
import useStore from "../store/use-store";
import { useLocalVideosStore } from "../store/use-local-videos-store";

/**
 * Calculate fit scaling for a video to fit within scene dimensions
 */
const calculateFitScale = (
  videoWidth: number,
  videoHeight: number,
  sceneWidth: number,
  sceneHeight: number
) => {
  console.log('🧮 Calculating fit scale:');
  console.log(`   Video: ${videoWidth}x${videoHeight}`);
  console.log(`   Scene: ${sceneWidth}x${sceneHeight}`);

  const scaleX = sceneWidth / videoWidth;
  const scaleY = sceneHeight / videoHeight;
  const scale = Math.min(scaleX, scaleY); // Use the smaller scale to ensure it fits

  console.log(`   ScaleX: ${scaleX}, ScaleY: ${scaleY}, Final scale: ${scale}`);

  const result = {
    scale,
    width: videoWidth * scale,
    height: videoHeight * scale,
    left: (sceneWidth - videoWidth * scale) / 2,
    top: (sceneHeight - videoHeight * scale) / 2,
  };

  console.log('   Result:', result);

  return result;
};

/**
 * Hook to handle dynamic scene sizing and video scaling based on videos added to the timeline
 */
export const useDynamicSceneSizing = (stateManager: any) => {
  const { setSize, size } = useStore();
  const hasSetInitialSize = useRef(false);

  useEffect(() => {
    const addVideoEvents = subject.pipe(
      filter(({ key }) => key === ADD_VIDEO),
    );

    const subscription = addVideoEvents.subscribe(async (obj) => {
      console.log('🎬 ADD_VIDEO event received:', obj);

      const payload = obj.value?.payload;
      const options = obj.value?.options;

      console.log('📦 Payload:', payload);
      console.log('⚙️ Options:', options);

      if (payload?.metadata?.localVideoId) {
        // Get the local video to extract dimensions
        const localVideo = useLocalVideosStore.getState().actions.getVideoById(payload.metadata.localVideoId);

        console.log('🎥 Local video found:', localVideo);

        if (localVideo && localVideo.width && localVideo.height) {
          console.log(`📐 Video dimensions: ${localVideo.width}x${localVideo.height}`);

          // Handle scene sizing for the first video
          if (!hasSetInitialSize.current) {
            const newSize = {
              width: localVideo.width,
              height: localVideo.height,
            };

            console.log('🆕 DYNAMIC SCENE SIZING DEBUG:');
            console.log(`📐 First video dimensions: ${localVideo.width}x${localVideo.height}`);
            console.log(`🎯 Setting canvas size to:`, newSize);

            // Update the store size
            setSize(newSize);

            // Update the state manager size
            stateManager.updateState({ size: newSize });

            // Mark that we've set the initial size
            hasSetInitialSize.current = true;

            console.log(`✅ Canvas size updated to match first video: ${newSize.width}x${newSize.height}`);
            console.log(`🔄 This will affect all subsequent exports and previews`);
          } else {
            console.log('⏭️ Not first video - scene size already set');
          }

          // Handle video scaling if scaleMode is "fit"
          if (options?.scaleMode === "fit") {
            console.log('🎯 ScaleMode is "fit" - applying fit scaling');

            // Use a small delay to ensure the video object is created and scene size is updated
            setTimeout(() => {
              const currentSize = useStore.getState().size;
              console.log('📏 Current scene size:', currentSize);

              const fitCalculation = calculateFitScale(
                localVideo.width,
                localVideo.height,
                currentSize.width,
                currentSize.height
              );

              console.log('🧮 Fit calculation result:', fitCalculation);

              // Apply the fit scaling to the video object
              dispatch(EDIT_OBJECT, {
                payload: {
                  [payload.id]: {
                    details: {
                      width: fitCalculation.width,
                      height: fitCalculation.height,
                      left: `${fitCalculation.left}px`,
                      top: `${fitCalculation.top}px`,
                      transform: `scale(1)`, // Reset any existing transform
                    },
                  },
                },
              });

              console.log(`✅ Applied fit scaling to video ${payload.id}:`, fitCalculation);
            }, 100); // Small delay to ensure proper timing
          } else {
            console.log('❌ ScaleMode is not "fit", current scaleMode:', options?.scaleMode);
          }
        } else {
          console.log('❌ Local video not found or missing dimensions');
        }
      } else {
        console.log('❌ No localVideoId in payload metadata');
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [setSize, stateManager, size]);
};
