<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoom-Out Feature Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .video-preview {
            width: 200px;
            height: 356px;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            margin: 20px auto;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: transform 0.1s ease;
            position: relative;
            overflow: hidden;
        }
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #5a67d8;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .timeline {
            width: 100%;
            height: 40px;
            background: #eee;
            border-radius: 4px;
            position: relative;
            margin: 20px 0;
        }
        .timeline-progress {
            height: 100%;
            background: #667eea;
            border-radius: 4px;
            transition: width 0.1s ease;
        }
        .timeline-marker {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #333;
        }
        .info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 20px 0;
            font-size: 14px;
        }
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .phase-zoom-in { background: #e3f2fd; }
        .phase-zoom-out { background: #fff3e0; }
        .phase-inactive { background: #f5f5f5; }
        h1, h2 { color: #333; }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
        }
        .old-behavior {
            background: #ffebee;
            border: 2px solid #f44336;
        }
        .new-behavior {
            background: #e8f5e8;
            border: 2px solid #4caf50;
        }
    </style>
</head>
<body>
    <h1>🎬 Zoom-Out Feature Demo</h1>
    
    <div class="demo-container">
        <h2>Interactive Zoom Preview</h2>
        <p>This demo shows the new smooth zoom-out feature in action. Watch how the video smoothly returns to normal scale instead of snapping back instantly.</p>
        
        <div class="video-preview" id="videoPreview">
            <div>Video Preview<br><span id="scaleDisplay">Scale: 1.00x</span></div>
        </div>
        
        <div class="timeline">
            <div class="timeline-progress" id="timelineProgress"></div>
            <div class="timeline-marker" style="left: 16.67%;" title="Zoom Start (1s)"></div>
            <div class="timeline-marker" style="left: 66.67%;" title="Zoom End (4s)"></div>
            <div class="timeline-marker" style="left: 83.33%;" title="Zoom-Out End (5s)"></div>
        </div>
        
        <div class="controls">
            <button onclick="startDemo()">▶️ Start Demo</button>
            <button onclick="pauseDemo()">⏸️ Pause</button>
            <button onclick="resetDemo()">🔄 Reset</button>
        </div>
        
        <div class="info">
            <div class="info-item" id="timeInfo">Time: 0.0s</div>
            <div class="info-item" id="phaseInfo">Phase: Inactive</div>
            <div class="info-item" id="progressInfo">Progress: 0%</div>
        </div>
    </div>
    
    <div class="demo-container">
        <h2>Before vs After Comparison</h2>
        <div class="comparison">
            <div class="comparison-item old-behavior">
                <h3>❌ Old Behavior</h3>
                <p><strong>Instant Return</strong></p>
                <p>Zoom ends at 4s and immediately snaps back to 1.0x scale, creating a jarring visual transition.</p>
            </div>
            <div class="comparison-item new-behavior">
                <h3>✅ New Behavior</h3>
                <p><strong>Smooth Zoom-Out</strong></p>
                <p>Zoom ends at 4s, then smoothly transitions back to 1.0x over 1 second (4s-5s), creating a professional feel.</p>
            </div>
        </div>
    </div>

    <script>
        // Zoom configuration
        const zoomConfig = {
            maxZoomScale: 1.5,
            zoomOut: {
                duration: 1000,
                enabled: false, // Disabled since main cycle now handles complete zoom
                easing: 'ease-out'
            }
        };
        
        const zoomTiming = {
            startTime: 1000,
            endTime: 4000
        };
        
        // Demo state
        let currentTime = 0;
        let isPlaying = false;
        let animationId = null;
        const totalDuration = 6000; // 6 seconds total
        
        // Easing functions
        function easeOut(t) {
            return 1 - Math.pow(1 - t, 3);
        }
        
        function calculateZoomScale(time) {
            const zoomStartTime = zoomTiming.startTime;
            const zoomEndTime = zoomTiming.endTime;
            const zoomDuration = zoomEndTime - zoomStartTime;
            const zoomOutDuration = zoomConfig.zoomOut.duration;
            const zoomOutEndTime = zoomEndTime + zoomOutDuration;
            
            let scale = 1;
            let phase = 'inactive';
            let progress = 0;
            
            if (time >= zoomStartTime && time <= zoomEndTime) {
                phase = 'zoom-in';
                progress = (time - zoomStartTime) / zoomDuration;
                // Use sine wave to create smooth zoom in and out within the duration
                // sin(0) = 0, sin(π/2) = 1, sin(π) = 0
                // This creates a smooth zoom that peaks in the middle and returns to 1.0 at the end
                const bezierProgress = Math.sin(progress * Math.PI);
                scale = 1 + bezierProgress * zoomConfig.maxZoomScale;
            } else if (zoomConfig.zoomOut.enabled && time > zoomEndTime && time <= zoomOutEndTime) {
                phase = 'zoom-out';
                progress = (time - zoomEndTime) / zoomOutDuration;
                const easedProgress = easeOut(progress);
                const maxScale = 1 + zoomConfig.maxZoomScale;
                scale = maxScale - (easedProgress * zoomConfig.maxZoomScale);
            }
            
            return { scale, phase, progress };
        }
        
        function updateDisplay() {
            const result = calculateZoomScale(currentTime);
            const videoPreview = document.getElementById('videoPreview');
            const scaleDisplay = document.getElementById('scaleDisplay');
            const timelineProgress = document.getElementById('timelineProgress');
            const timeInfo = document.getElementById('timeInfo');
            const phaseInfo = document.getElementById('phaseInfo');
            const progressInfo = document.getElementById('progressInfo');
            
            // Update video scale
            videoPreview.style.transform = `scale(${result.scale})`;
            scaleDisplay.textContent = `Scale: ${result.scale.toFixed(2)}x`;
            
            // Update timeline
            const progressPercent = (currentTime / totalDuration) * 100;
            timelineProgress.style.width = `${progressPercent}%`;
            
            // Update info
            timeInfo.textContent = `Time: ${(currentTime / 1000).toFixed(1)}s`;
            phaseInfo.textContent = `Phase: ${result.phase}`;
            phaseInfo.className = `info-item phase-${result.phase}`;
            progressInfo.textContent = `Progress: ${(result.progress * 100).toFixed(0)}%`;
        }
        
        function animate() {
            if (!isPlaying) return;
            
            currentTime += 50; // 50ms increments
            
            if (currentTime >= totalDuration) {
                currentTime = totalDuration;
                isPlaying = false;
            }
            
            updateDisplay();
            
            if (isPlaying) {
                animationId = requestAnimationFrame(animate);
            }
        }
        
        function startDemo() {
            isPlaying = true;
            animate();
        }
        
        function pauseDemo() {
            isPlaying = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        }
        
        function resetDemo() {
            pauseDemo();
            currentTime = 0;
            updateDisplay();
        }
        
        // Initialize display
        updateDisplay();
    </script>
</body>
</html>
