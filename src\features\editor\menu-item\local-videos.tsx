import React, { useCallback, useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ProgressiveFileUploader } from "@/components/ui/progressive-file-uploader";
import { useGlobalFileDrag } from "../hooks/use-global-file-drag";
import { Button } from "@/components/ui/button";
import { dispatch } from "@designcombo/events";
import { ADD_VIDEO } from "@designcombo/state";
import { generateId } from "@designcombo/timeline";
import { IVideo } from "@designcombo/types";
import { useLocalVideosStore, LocalVideo } from "../store/use-local-videos-store";
import Draggable from "@/components/shared/draggable";
import { Trash2, AlertCircle, Plus } from "lucide-react";

export const LocalVideos = () => {
  const { videos, isLoading, actions } = useLocalVideosStore();
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const { isDraggingFiles, hasVideoFiles, resetDragState } = useGlobalFileDrag();

  const handleFileUpload = useCallback(
    async (files: File[]) => {
      // Clear previous errors
      setUploadErrors([]);

      let hasSuccessfulUploads = false;

      for (const file of files) {
        if (file.type.startsWith("video/")) {
          try {
            await actions.addVideo(file);
            hasSuccessfulUploads = true;
          } catch (error) {
            console.error("Failed to add video:", error);
            // Enhanced error handling for better user feedback
            const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
            const fullErrorMessage = `Failed to upload "${file.name}": ${errorMessage}`;
            console.error(fullErrorMessage);

            // Add error to state for UI display
            setUploadErrors(prev => [...prev, fullErrorMessage]);
          }
        }
      }

      // Reset drag state after successful uploads
      if (hasSuccessfulUploads) {
        resetDragState();
      }
    },
    [actions, resetDragState]
  );

  const handleAddVideoToTimeline = useCallback(
    (localVideo: LocalVideo) => {
      const videoData: Partial<IVideo> = {
        id: generateId(),
        details: {
          src: localVideo.objectUrl,
          width: localVideo.width,
          height: localVideo.height,
          blur: 0,
          brightness: 100,
          flipX: false,
          flipY: false,
          rotate: "0",
          visibility: "visible",
        },
        type: "video",
        metadata: {
          previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
          localVideoId: localVideo.id,
          fileName: localVideo.name,
        },
        duration: localVideo.duration,
      };

      dispatch(ADD_VIDEO, {
        payload: videoData,
        options: {
          resourceId: "main",
          scaleMode: "fit",
        },
      });
    },
    []
  );

  const handleRemoveVideo = useCallback(
    (id: string, event: React.MouseEvent) => {
      event.stopPropagation();
      actions.removeVideo(id);
    },
    [actions]
  );

  const clearErrors = useCallback(() => {
    setUploadErrors([]);
  }, []);

  const handlePlusButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        handleFileUpload(Array.from(files));
      }
      // Reset the input value so the same file can be selected again
      event.target.value = '';
    },
    [handleFileUpload]
  );

  return (
    <div className="flex flex-1 flex-col">
      <div className="text-text-primary flex h-12 flex-none items-center justify-between px-4 text-sm font-medium">
        <span>Local Videos</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePlusButtonClick}
          disabled={isLoading}
          className="h-6 w-6 p-0 hover:bg-muted/50"
          title="Add video files"
        >
          <Plus className="h-4 w-4" />
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*,.mp4,.mov,.avi,.mkv,.webm"
          multiple
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>

      <ScrollArea className="flex-1">
        <div className="px-4">
          <ProgressiveFileUploader
            onValueChange={handleFileUpload}
            accept={{
              "video/*": [".mp4", ".mov", ".avi", ".mkv", ".webm"],
            }}
            maxSize={500 * 1024 * 1024} // 500MB - increased for longer videos
            maxFileCount={10}
            multiple={true}
            disabled={isLoading}
            emptyMessage="No videos uploaded yet - Upload video files to get started"
            showUploaderWhenHasFiles={false}
            hasFiles={videos.length > 0}
          >
            {/* Error Display */}
            {uploadErrors.length > 0 && (
              <div className="mt-3 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-destructive">Upload Errors:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearErrors}
                    className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
                  >
                    Clear
                  </Button>
                </div>
                {uploadErrors.map((error, index) => (
                  <div key={index} className="flex items-start gap-2 p-2 bg-destructive/10 border border-destructive/20 rounded-md">
                    <AlertCircle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-destructive">{error}</p>
                  </div>
                ))}
              </div>
            )}



            <div className="grid grid-cols-2 gap-2">
              {videos.map((video) => (
                <LocalVideoItem
                  key={video.id}
                  video={video}
                  onAddToTimeline={handleAddVideoToTimeline}
                  onRemove={handleRemoveVideo}
                />
              ))}
              {isDraggingFiles && hasVideoFiles && (
                <DropZoneGridItem onDrop={handleFileUpload} />
              )}
            </div>
          </ProgressiveFileUploader>
        </div>
      </ScrollArea>
    </div>
  );
};

interface LocalVideoItemProps {
  video: LocalVideo;
  onAddToTimeline: (video: LocalVideo) => void;
  onRemove: (id: string, event: React.MouseEvent) => void;
}

const LocalVideoItem: React.FC<LocalVideoItemProps> = ({
  video,
  onAddToTimeline,
  onRemove,
}) => {
  const thumbnailUrl = video.thumbnailUrl || video.objectUrl;

  const dragPreviewStyle = React.useMemo(
    () => ({
      backgroundImage: `url(${thumbnailUrl})`,
      backgroundSize: "cover",
      backgroundPosition: "center",
      width: "120px",
      height: `${120 / video.aspectRatio}px`,
    }),
    [thumbnailUrl, video.aspectRatio]
  );

  return (
    <div className="relative group">
      <Draggable
        data={{
          ...video,
          src: video.objectUrl,
          details: {
            src: video.objectUrl,
            width: video.width,
            height: video.height,
            blur: 0,
            brightness: 100,
            flipX: false,
            flipY: false,
            rotate: "0",
            visibility: "visible",
          },
          metadata: {
            previewUrl: thumbnailUrl,
            localVideoId: video.id,
            fileName: video.name,
          },
        }}
        renderCustomPreview={
          <div
            style={dragPreviewStyle}
            className="draggable rounded-md shadow-lg border border-border"
          />
        }
        shouldDisplayPreview={true}
      >
        <div
          onClick={() => onAddToTimeline(video)}
          className="flex flex-col items-center justify-center overflow-hidden bg-background pb-2 cursor-pointer hover:bg-muted/50 transition-colors rounded-md"
        >
          <div className="relative w-full aspect-video">
            <img
              draggable={false}
              src={thumbnailUrl}
              className="h-full w-full rounded-md object-cover"
              alt={video.name}
            />
            <Button
              size="sm"
              variant="destructive"
              className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => onRemove(video.id, e)}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
          <div className="w-full px-1 mt-1">
            <p className="text-xs text-muted-foreground truncate" title={video.name}>
              {video.name}
            </p>
            <p className="text-xs text-muted-foreground">
              {Math.round(video.duration / 1000)}s
            </p>
          </div>
        </div>
      </Draggable>
    </div>
  );
};

interface DropZoneGridItemProps {
  onDrop: (files: File[]) => void;
}

const DropZoneGridItem: React.FC<DropZoneGridItemProps> = ({ onDrop }) => {
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    // Don't stop propagation on drop so the global drag state can be cleared
    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('video/')
    );
    if (files.length > 0) {
      onDrop(files);
    }
  };

  return (
    <div
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      className="flex flex-col items-center justify-center aspect-video bg-background border-2 border-dashed border-muted-foreground/50 rounded-md p-4 text-center hover:border-primary/50 hover:bg-primary/5 transition-colors"
    >
      <div className="rounded-full border border-dashed border-muted-foreground/50 p-3 mb-2">
        <Plus className="h-6 w-6 text-muted-foreground" />
      </div>
      <p className="text-xs text-muted-foreground font-medium">
        Drop the video files
      </p>
      <p className="text-xs text-muted-foreground">
        here
      </p>
    </div>
  );
};
