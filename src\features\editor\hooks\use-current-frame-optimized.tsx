import { CallbackListener, PlayerRef } from "@remotion/player";
import { useCallback, useSyncExternalStore, useRef } from "react";
import useStore from "../store/use-store";

/**
 * Optimized version of useCurrentPlayerFrame that completely pauses updates during playhead dragging
 * Use this for components that don't need real-time updates during dragging (like time displays, canvas calculations)
 */
export const useCurrentPlayerFrameOptimized = (ref: React.RefObject<PlayerRef>) => {
  const lastFrameRef = useRef(0);
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateTimeRef = useRef(0);
  const THROTTLE_MS = 16; // ~60fps max update rate for zoom operations (was 33ms/30fps)

  const subscribe = useCallback(
    (onStoreChange: () => void) => {
      const { current } = ref;
      if (!current) {
        return () => undefined;
      }

      const updater: CallbackListener<"frameupdate"> = () => {
        const currentFrame = current.getCurrentFrame();
        const { isPlayheadDragging, isVideoSelecting } = useStore.getState();

        if (isPlayheadDragging || isVideoSelecting) {
          // Store the current frame but don't trigger re-render during dragging or selecting
          lastFrameRef.current = currentFrame;
          return;
        }

        // Optimized throttling for zoom operations - use performance.now() for better precision
        const now = performance.now();
        const timeSinceLastUpdate = now - lastUpdateTimeRef.current;

        if (timeSinceLastUpdate < THROTTLE_MS) {
          // Clear any pending update
          if (throttleTimeoutRef.current) {
            clearTimeout(throttleTimeoutRef.current);
          }

          // Schedule a throttled update with remaining time for smoother animation
          const remainingTime = THROTTLE_MS - timeSinceLastUpdate;
          throttleTimeoutRef.current = setTimeout(() => {
            lastUpdateTimeRef.current = performance.now();
            lastFrameRef.current = current.getCurrentFrame();
            onStoreChange();
            throttleTimeoutRef.current = null;
          }, remainingTime);
          return;
        }

        // Normal update when throttle time has passed
        lastUpdateTimeRef.current = now;
        lastFrameRef.current = currentFrame;
        onStoreChange();
      };

      current.addEventListener("frameupdate", updater);
      return () => {
        current.removeEventListener("frameupdate", updater);
        // Clean up any pending throttled update
        if (throttleTimeoutRef.current) {
          clearTimeout(throttleTimeoutRef.current);
          throttleTimeoutRef.current = null;
        }
      };
    },
    [ref],
  );
  
  const data = useSyncExternalStore<number>(
    subscribe,
    () => {
      const { isPlayheadDragging, isVideoSelecting } = useStore.getState();
      if (isPlayheadDragging || isVideoSelecting) {
        // Return cached frame during dragging or selecting to avoid expensive getCurrentFrame() calls
        return lastFrameRef.current;
      }
      return ref.current?.getCurrentFrame() ?? 0;
    },
    () => 0,
  );
  return data;
};
