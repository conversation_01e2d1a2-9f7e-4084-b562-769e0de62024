import React, { useCallback, useState } from "react";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { ProgressiveFileUploader } from "@/components/ui/progressive-file-uploader";
import { Button } from "@/components/ui/button";
import { dispatch } from "@designcombo/events";
import { ADD_AUDIO } from "@designcombo/state";
import { generateId } from "@designcombo/timeline";
import { IAudio } from "@designcombo/types";
import { useLocalAudiosStore, LocalAudio } from "../store/use-local-audios-store";
import { useGlobalFileDrag } from "../hooks/use-global-file-drag";
import Draggable from "@/components/shared/draggable";
import { Trash2, Upload, Play, Pause, Volume2, Plus } from "lucide-react";

export const LocalAudios = () => {
  const { audios, isLoading, actions } = useLocalAudiosStore();
  const { isDraggingFiles, hasAudioFiles, resetDragState } = useGlobalFileDrag();
  const [playingAudioId, setPlayingAudioId] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileUpload = useCallback(
    async (files: File[]) => {
      let hasSuccessfulUploads = false;

      for (const file of files) {
        if (file.type.startsWith("audio/")) {
          try {
            await actions.addAudio(file);
            hasSuccessfulUploads = true;
          } catch (error) {
            console.error("Failed to add audio:", error);
            // You could add toast notification here
          }
        }
      }

      // Reset drag state after successful uploads
      if (hasSuccessfulUploads) {
        resetDragState();
      }
    },
    [actions, resetDragState]
  );

  const handleAddAudioToTimeline = useCallback(
    (localAudio: LocalAudio) => {
      const audioData: Partial<IAudio> = {
        id: generateId(),
        details: {
          src: localAudio.objectUrl,
          volume: 100,
        },
        type: "audio",
        metadata: {
          localAudioId: localAudio.id,
          fileName: localAudio.name,
          waveformData: localAudio.waveformData,
        },
        duration: localAudio.duration,
      };

      dispatch(ADD_AUDIO, {
        payload: audioData,
        options: {
          resourceId: "audio",
        },
      });
    },
    []
  );

  const handleRemoveAudio = useCallback(
    (id: string, event: React.MouseEvent) => {
      event.stopPropagation();
      // Stop playing if this audio is currently playing
      if (playingAudioId === id && currentAudio) {
        currentAudio.pause();
        setCurrentAudio(null);
        setPlayingAudioId(null);
      }
      actions.removeAudio(id);
    },
    [actions, playingAudioId, currentAudio]
  );

  const handlePlayPause = useCallback(
    (localAudio: LocalAudio, event: React.MouseEvent) => {
      event.stopPropagation();
      
      if (playingAudioId === localAudio.id && currentAudio) {
        // Pause current audio
        currentAudio.pause();
        setCurrentAudio(null);
        setPlayingAudioId(null);
      } else {
        // Stop any currently playing audio
        if (currentAudio) {
          currentAudio.pause();
        }
        
        // Play new audio
        const audio = new Audio(localAudio.objectUrl);
        audio.onended = () => {
          setCurrentAudio(null);
          setPlayingAudioId(null);
        };
        
        audio.play().then(() => {
          setCurrentAudio(audio);
          setPlayingAudioId(localAudio.id);
        }).catch((error) => {
          console.error("Error playing audio:", error);
        });
      }
    },
    [playingAudioId, currentAudio]
  );

  const handlePlusButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        handleFileUpload(Array.from(files));
      }
      // Reset the input value so the same file can be selected again
      event.target.value = '';
    },
    [handleFileUpload]
  );

  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="text-text-primary flex h-12 flex-none items-center justify-between px-4 text-sm font-medium">
        <span>Local Audios</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePlusButtonClick}
          disabled={isLoading}
          className="h-6 w-6 p-0 hover:bg-muted/50"
          title="Add audio files"
        >
          <Plus className="h-4 w-4" />
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          accept="audio/*,.mp3,.wav,.ogg,.m4a,.aac,.flac"
          multiple
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>

      <ScrollArea className="flex-1">
        <div className="px-4">
          <ProgressiveFileUploader
            onValueChange={handleFileUpload}
            accept={{
              "audio/*": [".mp3", ".wav", ".ogg", ".m4a", ".aac", ".flac"],
            }}
            maxSize={50 * 1024 * 1024} // 50MB
            maxFileCount={10}
            multiple={true}
            disabled={isLoading}
            emptyMessage="No audio files uploaded yet - Upload audio files to get started"
            showUploaderWhenHasFiles={false}
            hasFiles={audios.length > 0}
          >
            <div className="space-y-2">
              {audios.map((audio) => (
                <Draggable
                  key={audio.id}
                  data={{
                    type: "audio",
                    ...audio,
                    src: audio.objectUrl,
                    details: {
                      src: audio.objectUrl,
                      volume: 100,
                    },
                    metadata: {
                      localAudioId: audio.id,
                      fileName: audio.name,
                      waveformData: audio.waveformData,
                    },
                  }}
                  renderCustomPreview={
                    <div className="bg-primary/20 border border-primary/40 rounded-lg p-2 shadow-lg flex items-center justify-center w-16 h-16">
                      <Volume2 className="h-8 w-8 text-primary" />
                    </div>
                  }
                  shouldDisplayPreview={true}
                >
                  <div className="group relative cursor-pointer rounded-lg border border-border bg-card p-3 transition-colors hover:bg-accent">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={(e) => handlePlayPause(audio, e)}
                      >
                        {playingAudioId === audio.id ? (
                          <Pause className="h-4 w-4" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </Button>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <Volume2 className="h-4 w-4 text-muted-foreground" />
                          <p className="truncate text-sm font-medium">
                            {audio.name}
                          </p>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {formatDuration(audio.duration)}
                        </p>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100"
                          onClick={(e) => handleAddAudioToTimeline(audio)}
                        >
                          <Upload className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-destructive opacity-0 group-hover:opacity-100"
                          onClick={(e) => handleRemoveAudio(audio.id, e)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Simple waveform visualization */}
                    {audio.waveformData && (
                      <div className="mt-2 flex h-8 items-center gap-px overflow-hidden">
                        {audio.waveformData.slice(0, 50).map((value, index) => (
                          <div
                            key={index}
                            className="bg-primary/60 rounded-sm min-w-[2px] flex-shrink-0"
                            style={{
                              height: `${Math.max(value * 100, 2)}%`,
                              width: `calc((100% - ${49}px) / 50)`,
                            }}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </Draggable>
              ))}
          
              {isDraggingFiles && hasAudioFiles && (
                <AudioDropZoneGridItem onDrop={handleFileUpload} />
              )}
            </div>
          </ProgressiveFileUploader>
        </div>
      </ScrollArea>
    </div>
  );
};

interface AudioDropZoneGridItemProps {
  onDrop: (files: File[]) => void;
}

const AudioDropZoneGridItem: React.FC<AudioDropZoneGridItemProps> = ({ onDrop }) => {
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    // Don't stop propagation on drop so the global drag state can be cleared
    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('audio/')
    );
    if (files.length > 0) {
      onDrop(files);
    }
  };

  return (
    <div
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      className="flex flex-col items-center justify-center bg-background border-2 border-dashed border-muted-foreground/50 rounded-md p-4 text-center hover:border-primary/50 hover:bg-primary/5 transition-colors min-h-[80px]"
    >
      <div className="rounded-full border border-dashed border-muted-foreground/50 p-3 mb-2">
        <Volume2 className="h-6 w-6 text-muted-foreground" />
      </div>
      <p className="text-xs text-muted-foreground font-medium">
        Drop the audio files
      </p>
      <p className="text-xs text-muted-foreground">
        here
      </p>
    </div>
  );
};
