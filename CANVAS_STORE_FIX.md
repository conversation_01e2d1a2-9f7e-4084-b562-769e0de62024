# Canvas Store Fix - Correct Resolution Reading

## Problem Identified

The export dialog was showing incorrect canvas dimensions (1080x1920) instead of the actual canvas size (1920x910) because we were reading from the wrong store.

## Root Cause

**Canvas dimensions are stored in TWO different places:**

1. **Main Store (`use-store.ts`)**: Contains the actual canvas dimensions
   ```typescript
   size: {
     width: 1920,  // Actual canvas width
     height: 910,  // Actual canvas height
   }
   ```

2. **Canvas Store (`use-canvas-store.ts`)**: Contains canvas styling settings
   ```typescript
   settings: {
     background: { ... },
     padding: { ... },
     blur: { ... },
     // NO width/height here!
   }
   ```

## The Fix

### Before (Incorrect):
```typescript
// ❌ Wrong - canvasSettings doesn't have width/height
const { canvasSettings } = useCanvasStore();
const canvasWidth = canvasSettings?.width || 1080;  // Always fallback
const canvasHeight = canvasSettings?.height || 1920; // Always fallback
```

### After (Correct):
```typescript
// ✅ Correct - size contains actual canvas dimensions
const { size } = useStore();
const canvasWidth = size.width;   // Actual: 1920
const canvasHeight = size.height; // Actual: 910
```

## Files Updated

### 1. Export Hook (`src/features/editor/hooks/use-video-export.ts`)

**Changed import:**
```typescript
// Added size to destructuring
const {
  trackItemIds,
  trackItemsMap,
  trackItemDetailsMap,
  transitionsMap,
  duration,
  size, // ← Added this
} = useStore();

// Fixed canvas settings reference
const { settings: canvasSettings } = useCanvasStore();
```

**Fixed canvas dimensions:**
```typescript
// Get actual canvas dimensions from main store
const canvasWidth = size.width;   // Now reads 1920
const canvasHeight = size.height; // Now reads 910
```

### 2. Export Dialog (`src/features/editor/components/export-dialog.tsx`)

**Changed import:**
```typescript
// Changed from useCanvasStore to useStore
import useStore from '../store/use-store';
```

**Fixed canvas dimensions:**
```typescript
const { size } = useStore();

// Get canvas dimensions from main store
const canvasWidth = size.width;   // Now shows 1920
const canvasHeight = size.height; // Now shows 910
```

## Result

### Before:
- Export dialog showed: "Canvas Resolution: 1080 × 1920"
- Exported video: Wrong dimensions (1080x1920)

### After:
- Export dialog shows: "Canvas Resolution: 1920 × 910" ✅
- Exported video: Correct dimensions (1920x910) ✅

## Data Flow (Corrected)

```
Editor Canvas Size (1920x910)
    ↓
Main Store (size.width, size.height)
    ↓
Export Hook (reads from size)
    ↓
Export Dialog (displays correct dimensions)
    ↓
Render Server (receives correct dimensions)
    ↓
Final Video (1920x910) ✅
```

## Testing

To verify the fix:

1. **Check current canvas size**: Look at the video preview area
2. **Open export dialog**: Should show the correct canvas dimensions
3. **Check console logs**: Should log the correct dimensions during export
4. **Export video**: Final video should match canvas size exactly

## Key Takeaway

Always check where data is actually stored in the application state! The canvas **styling** settings (background, padding, etc.) are separate from the canvas **dimensions** (width, height).
