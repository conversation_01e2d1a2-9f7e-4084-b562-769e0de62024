// Performance monitoring utility for render operations

export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
  }

  startTimer(operationId, operationName) {
    this.metrics.set(operationId, {
      name: operationName,
      startTime: process.hrtime.bigint(),
      endTime: null,
      duration: null,
      memoryStart: process.memoryUsage(),
      memoryEnd: null
    });
  }

  endTimer(operationId) {
    const metric = this.metrics.get(operationId);
    if (!metric) {
      console.warn(`No timer found for operation: ${operationId}`);
      return null;
    }

    metric.endTime = process.hrtime.bigint();
    metric.duration = Number(metric.endTime - metric.startTime) / 1000000; // Convert to milliseconds
    metric.memoryEnd = process.memoryUsage();

    const result = {
      operationId,
      name: metric.name,
      duration: metric.duration,
      memoryUsed: metric.memoryEnd.heapUsed - metric.memoryStart.heapUsed,
      peakMemory: metric.memoryEnd.heapUsed
    };

    console.log(`📊 Performance: ${metric.name} completed in ${result.duration.toFixed(2)}ms, Memory: ${(result.memoryUsed / 1024 / 1024).toFixed(2)}MB`);
    
    return result;
  }

  getMetrics(operationId) {
    return this.metrics.get(operationId);
  }

  getAllMetrics() {
    const results = [];
    for (const [id, metric] of this.metrics.entries()) {
      if (metric.endTime) {
        results.push({
          operationId: id,
          name: metric.name,
          duration: metric.duration,
          memoryUsed: metric.memoryEnd.heapUsed - metric.memoryStart.heapUsed,
          peakMemory: metric.memoryEnd.heapUsed
        });
      }
    }
    return results;
  }

  clearMetrics() {
    this.metrics.clear();
  }

  // Get system performance info
  getSystemInfo() {
    const cpus = require('os').cpus();
    const memory = process.memoryUsage();
    const uptime = process.uptime();

    return {
      cpuCount: cpus.length,
      cpuModel: cpus[0]?.model || 'Unknown',
      memoryUsage: {
        rss: (memory.rss / 1024 / 1024).toFixed(2) + 'MB',
        heapTotal: (memory.heapTotal / 1024 / 1024).toFixed(2) + 'MB',
        heapUsed: (memory.heapUsed / 1024 / 1024).toFixed(2) + 'MB',
        external: (memory.external / 1024 / 1024).toFixed(2) + 'MB'
      },
      uptime: uptime.toFixed(2) + 's',
      nodeVersion: process.version
    };
  }
}

// Global instance
export const performanceMonitor = new PerformanceMonitor();
