import { create } from "zustand";
import { generateId } from "@designcombo/timeline";
import { getAudioMetadata, generateWaveformData, AudioMetadata } from "../utils/file";

export interface LocalAudio {
  id: string;
  file: File;
  name: string;
  duration: number;
  objectUrl: string;
  waveformData?: number[];
  type: "audio";
}

interface LocalAudiosState {
  audios: LocalAudio[];
  isLoading: boolean;
  actions: {
    addAudio: (file: File) => Promise<LocalAudio>;
    removeAudio: (id: string) => void;
    clearAll: () => void;
    getAudioById: (id: string) => LocalAudio | undefined;
  };
}

export const useLocalAudiosStore = create<LocalAudiosState>((set, get) => ({
  audios: [],
  isLoading: false,
  
  actions: {
    addAudio: async (file: File) => {
      set({ isLoading: true });

      try {
        const id = generateId();
        const objectUrl = URL.createObjectURL(file);
        const metadata = await getAudioMetadata(file);
        let waveformData: number[] | undefined;

        try {
          waveformData = await generateWaveformData(file);
        } catch (error) {
          console.warn("Could not generate waveform data:", error);
        }

        const localAudio: LocalAudio = {
          id,
          file,
          name: file.name,
          duration: metadata.duration,
          objectUrl,
          waveformData,
          type: "audio",
        };

        set((state) => ({
          audios: [...state.audios, localAudio],
          isLoading: false,
        }));

        return localAudio;
      } catch (error) {
        set({ isLoading: false });
        throw error;
      }
    },

    removeAudio: (id: string) => {
      set((state) => {
        const audioToRemove = state.audios.find(audio => audio.id === id);
        if (audioToRemove) {
          URL.revokeObjectURL(audioToRemove.objectUrl);
        }
        return {
          audios: state.audios.filter(audio => audio.id !== id),
        };
      });
    },

    clearAll: () => {
      set((state) => {
        // Clean up object URLs
        state.audios.forEach(audio => {
          URL.revokeObjectURL(audio.objectUrl);
        });
        return {
          audios: [],
        };
      });
    },

    getAudioById: (id: string) => {
      return get().audios.find(audio => audio.id === id);
    },
  },
}));
