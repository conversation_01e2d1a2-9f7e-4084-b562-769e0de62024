/**
 * Zoom Performance Optimization Utilities
 * 
 * This module provides utilities to optimize zoom operations and reduce lag
 * during zoom animations in the React video editor.
 */

// Cache for zoom calculations to avoid repeated computations
const zoomCalculationCache = new Map<string, number>();
const CACHE_SIZE_LIMIT = 100;

/**
 * Creates a cache key for zoom calculations
 */
function createZoomCacheKey(
  currentTime: number,
  startTime: number,
  endTime: number,
  maxZoomScale: number
): string {
  return `${currentTime}-${startTime}-${endTime}-${maxZoomScale}`;
}

/**
 * Cached zoom scale calculation for better performance
 */
export function getCachedZoomScale(
  currentTime: number,
  startTime: number,
  endTime: number,
  maxZoomScale: number,
  calculateFn: () => number
): number {
  const cacheKey = createZoomCacheKey(currentTime, startTime, endTime, maxZoomScale);
  
  // Check cache first
  if (zoomCalculationCache.has(cacheKey)) {
    return zoomCalculationCache.get(cacheKey)!;
  }
  
  // Calculate and cache result
  const result = calculateFn();
  
  // Manage cache size
  if (zoomCalculationCache.size >= CACHE_SIZE_LIMIT) {
    // Remove oldest entries (simple FIFO)
    const firstKey = zoomCalculationCache.keys().next().value;
    zoomCalculationCache.delete(firstKey);
  }
  
  zoomCalculationCache.set(cacheKey, result);
  return result;
}

/**
 * Clear zoom calculation cache (useful when zoom config changes)
 */
export function clearZoomCache(): void {
  zoomCalculationCache.clear();
}

/**
 * Debounced function creator for zoom operations
 */
export function createZoomDebouncer<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 16 // ~60fps
): T {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastArgs: Parameters<T>;
  
  return ((...args: Parameters<T>) => {
    lastArgs = args;
    
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      fn(...lastArgs);
      timeoutId = null;
    }, delay);
  }) as T;
}

/**
 * RAF-based throttle for smooth zoom animations
 */
export function createRAFThrottle<T extends (...args: any[]) => any>(fn: T): T {
  let rafId: number | null = null;
  let lastArgs: Parameters<T>;
  
  return ((...args: Parameters<T>) => {
    lastArgs = args;
    
    if (rafId) {
      return;
    }
    
    rafId = requestAnimationFrame(() => {
      fn(...lastArgs);
      rafId = null;
    });
  }) as T;
}

/**
 * Performance monitor for zoom operations
 */
export class ZoomPerformanceMonitor {
  private static instance: ZoomPerformanceMonitor;
  private isMonitoring = false;
  private zoomCalculations = 0;
  private totalZoomTime = 0;
  private slowZoomOperations = 0;
  private startTime = 0;
  
  static getInstance(): ZoomPerformanceMonitor {
    if (!ZoomPerformanceMonitor.instance) {
      ZoomPerformanceMonitor.instance = new ZoomPerformanceMonitor();
    }
    return ZoomPerformanceMonitor.instance;
  }
  
  startMonitoring(): void {
    this.isMonitoring = true;
    this.zoomCalculations = 0;
    this.totalZoomTime = 0;
    this.slowZoomOperations = 0;
    this.startTime = performance.now();
    console.log('🔍 Zoom Performance Monitor started');
  }
  
  stopMonitoring(): void {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    const duration = performance.now() - this.startTime;
    const avgZoomTime = this.zoomCalculations > 0 ? this.totalZoomTime / this.zoomCalculations : 0;
    
    console.log('📊 Zoom Performance Report:');
    console.log(`Total zoom calculations: ${this.zoomCalculations}`);
    console.log(`Average zoom calculation time: ${avgZoomTime.toFixed(2)}ms`);
    console.log(`Slow zoom operations (>1ms): ${this.slowZoomOperations}`);
    console.log(`Zoom calculations per second: ${(this.zoomCalculations / (duration / 1000)).toFixed(1)}`);
    
    if (avgZoomTime > 1) {
      console.warn('⚠️ Slow zoom calculations detected - consider optimization');
    }
    
    if (this.slowZoomOperations > this.zoomCalculations * 0.1) {
      console.warn('⚠️ High percentage of slow zoom operations');
    }
  }
  
  trackZoomCalculation<T>(operation: () => T): T {
    if (!this.isMonitoring) {
      return operation();
    }
    
    const start = performance.now();
    const result = operation();
    const duration = performance.now() - start;
    
    this.zoomCalculations++;
    this.totalZoomTime += duration;
    
    if (duration > 1) {
      this.slowZoomOperations++;
      console.warn(`🐌 Slow zoom calculation: ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }
}

/**
 * Optimized CSS transform string for zoom operations
 * Avoids string concatenation on every frame
 */
export class ZoomTransformCache {
  private cache = new Map<number, string>();
  private readonly precision = 3; // Decimal places for scale values
  
  getTransform(scale: number): string {
    // Round to avoid cache misses from floating point precision
    const roundedScale = Math.round(scale * Math.pow(10, this.precision)) / Math.pow(10, this.precision);
    
    if (this.cache.has(roundedScale)) {
      return this.cache.get(roundedScale)!;
    }
    
    const transform = `scale(${roundedScale})`;
    
    // Limit cache size
    if (this.cache.size > 50) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(roundedScale, transform);
    return transform;
  }
  
  clear(): void {
    this.cache.clear();
  }
}

// Global instances for easy access
export const zoomPerformanceMonitor = ZoomPerformanceMonitor.getInstance();
export const zoomTransformCache = new ZoomTransformCache();

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).zoomPerfMonitor = zoomPerformanceMonitor;
  (window as any).clearZoomCache = clearZoomCache;
}
