import { create } from "zustand";

/**
 * Zoom configuration interface for centralized zoom settings
 */
export interface IZoomConfig {
  /** Maximum zoom scale multiplier (e.g., 1.5 means 2.5x total zoom) */
  maxZoomScale: number;
  /** Cubic bezier control points for smooth zoom animation */
  bezierControlPoints: {
    p1: number;
    p2: number;
    p3: number;
    p4: number;
  };
  /** Default zoom timing values in milliseconds */
  defaultTiming: {
    startTime: number;
    endTime: number;
  };
  /** Zoom-out configuration */
  zoomOut: {
    /** Duration of the zoom-out phase in milliseconds */
    duration: number;
    /** Whether zoom-out is enabled */
    enabled: boolean;
    /** Easing function for zoom-out (linear, ease-out, or bezier) */
    easing: 'linear' | 'ease-out' | 'bezier';
  };
  /** Zoom area configuration - rectangular area to zoom into */
  zoomArea: {
    /** Left edge of zoom area (0 = left edge, 1 = right edge) */
    x: number;
    /** Top edge of zoom area (0 = top edge, 1 = bottom edge) */
    y: number;
    /** Width of zoom area (0-1, where 1 = full width) */
    width: number;
    /** Height of zoom area (0-1, where 1 = full height) */
    height: number;
  };
  /** Calculated zoom scale based on zoom area size */
  calculatedZoomScale: number;
}

/**
 * Zoom timing interface (already exists in use-store.ts but duplicated here for clarity)
 */
export interface IZoomTiming {
  startTime: number; // in milliseconds
  endTime: number;   // in milliseconds
}

/**
 * Zoom store interface
 */
interface IZoomStore {
  /** Zoom configuration settings */
  config: IZoomConfig;
  /** Update zoom configuration */
  setConfig: (config: Partial<IZoomConfig>) => void;
  /** Update maximum zoom scale */
  setMaxZoomScale: (scale: number) => void;
  /** Update bezier control points */
  setBezierControlPoints: (points: Partial<IZoomConfig['bezierControlPoints']>) => void;
  /** Update default timing */
  setDefaultTiming: (timing: Partial<IZoomConfig['defaultTiming']>) => void;
  /** Update zoom-out configuration */
  setZoomOut: (zoomOut: Partial<IZoomConfig['zoomOut']>) => void;
  /** Update zoom area */
  setZoomArea: (area: Partial<IZoomConfig['zoomArea']>) => void;
  /** Set zoom area from rectangle coordinates */
  setZoomAreaFromRect: (startX: number, startY: number, endX: number, endY: number) => void;
  /** Reset zoom area to center with default size */
  resetZoomArea: () => void;
  /** Get zoom center position from current zoom area */
  getZoomCenter: () => { x: number; y: number };
  /** Reset to default configuration */
  resetToDefaults: () => void;
}

/**
 * Default zoom configuration values
 * These values are extracted from the existing implementations to ensure consistency
 */
const DEFAULT_ZOOM_CONFIG: IZoomConfig = {
  // Maximum zoom scale of 1.5 means the total zoom goes from 1.0 to 2.5 (1 + 1.5)
  maxZoomScale: 1.5,

  // Cubic bezier control points that create a smooth bell curve
  // These points create a zoom that peaks at the middle and returns smoothly
  bezierControlPoints: {
    p1: 0.25,
    p2: 0.8,
    p3: 0.75,
    p4: 1.5,
  },

  // Default timing: no automatic zoom effect (user must manually add)
  // The zoom will be applied only when user explicitly adds it at playhead location
  defaultTiming: {
    startTime: 0, // No default start time
    endTime: 0,   // No default end time
  },

  // Zoom-out configuration: optional additional zoom-out phase after main cycle
  zoomOut: {
    duration: 1000,    // 1 second zoom-out duration
    enabled: false,    // Disabled by default since main cycle now handles complete zoom
    easing: 'ease-out' // Smooth ease-out transition
  },

  // Default zoom area: center of the video with moderate size
  zoomArea: {
    x: 0.25,    // Start at 25% from left
    y: 0.25,    // Start at 25% from top
    width: 0.5, // 50% of total width
    height: 0.5, // 50% of total height
  },

  // Calculated zoom scale based on area size (will be calculated dynamically)
  calculatedZoomScale: 2.0, // Default 2x zoom
};

/**
 * Centralized zoom store for managing all zoom-related configuration
 * This eliminates the need to duplicate zoom parameters across multiple files
 */
export const useZoomStore = create<IZoomStore>((set) => ({
  config: DEFAULT_ZOOM_CONFIG,

  setConfig: (newConfig) =>
    set((state) => ({
      config: { ...state.config, ...newConfig }
    })),

  setMaxZoomScale: (scale) =>
    set((state) => ({
      config: { ...state.config, maxZoomScale: scale }
    })),

  setBezierControlPoints: (points) =>
    set((state) => ({
      config: {
        ...state.config,
        bezierControlPoints: { ...state.config.bezierControlPoints, ...points }
      }
    })),

  setDefaultTiming: (timing) =>
    set((state) => ({
      config: {
        ...state.config,
        defaultTiming: { ...state.config.defaultTiming, ...timing }
      }
    })),

  setZoomOut: (zoomOut) =>
    set((state) => ({
      config: {
        ...state.config,
        zoomOut: { ...state.config.zoomOut, ...zoomOut }
      }
    })),

  setZoomArea: (area) =>
    set((state) => {
      const newArea = { ...state.config.zoomArea, ...area };
      // Calculate zoom scale based on area size - use smaller dimension to ensure full area fits
      const minSize = Math.min(newArea.width, newArea.height);
      // Zoom scale is inversely proportional to area size
      // Smaller area = higher zoom, larger area = lower zoom
      const calculatedZoomScale = Math.max(1, Math.min(10, 1 / Math.max(0.05, minSize)));

      return {
        config: {
          ...state.config,
          zoomArea: newArea,
          calculatedZoomScale
        }
      };
    }),

  setZoomAreaFromRect: (startX, startY, endX, endY) =>
    set((state) => {
      // Normalize coordinates and ensure proper order
      const x = Math.min(startX, endX);
      const y = Math.min(startY, endY);
      const width = Math.abs(endX - startX);
      const height = Math.abs(endY - startY);

      // Clamp values to valid range
      const clampedArea = {
        x: Math.max(0, Math.min(1 - width, x)),
        y: Math.max(0, Math.min(1 - height, y)),
        width: Math.max(0.05, Math.min(1, width)), // Minimum 5% size
        height: Math.max(0.05, Math.min(1, height))
      };

      // Calculate zoom scale based on area size - use smaller dimension to ensure full area fits
      const minSize = Math.min(clampedArea.width, clampedArea.height);
      // Zoom scale is inversely proportional to area size
      const calculatedZoomScale = Math.max(1, Math.min(10, 1 / Math.max(0.05, minSize)));

      return {
        config: {
          ...state.config,
          zoomArea: clampedArea,
          calculatedZoomScale
        }
      };
    }),

  resetZoomArea: () =>
    set((state) => ({
      config: {
        ...state.config,
        zoomArea: {
          x: 0.25,
          y: 0.25,
          width: 0.5,
          height: 0.5
        },
        calculatedZoomScale: 2.0
      }
    })),

  getZoomCenter: () => {
    const { config } = useZoomStore.getState();
    return {
      x: config.zoomArea.x + config.zoomArea.width / 2,
      y: config.zoomArea.y + config.zoomArea.height / 2
    };
  },

  resetToDefaults: () =>
    set({ config: DEFAULT_ZOOM_CONFIG }),
}));

export default useZoomStore;
