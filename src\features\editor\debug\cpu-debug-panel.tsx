import React, { useState, useEffect } from 'react';
import CPUMonitor from './cpu-monitor';
import PerformanceMonitor from './performance-monitor';
import RemotionProfiler from './remotion-profiler';
import ReactProfiler from './react-profiler';
import MainThreadMonitor from './main-thread-monitor';

interface CPUDebugPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

const CPUDebugPanel: React.FC<CPUDebugPanelProps> = ({ 
  isVisible = false, 
  onToggle 
}) => {
  const [cpuStatus, setCpuStatus] = useState<any>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      const status = CPUMonitor.getInstance().getStatus();
      setCpuStatus(status);
      setIsMonitoring(status.isMonitoring);
    }, 500);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const handleStartMonitoring = () => {
    console.log('🔥 Starting comprehensive CPU monitoring...');
    CPUMonitor.getInstance().startMonitoring();
    PerformanceMonitor.getInstance().startMonitoring();
    RemotionProfiler.getInstance().startProfiling();
    ReactProfiler.getInstance().startProfiling();
    MainThreadMonitor.getInstance().startMonitoring();
    setIsMonitoring(true);
  };

  const handleStopMonitoring = () => {
    console.log('🔥 Stopping comprehensive CPU monitoring...');
    CPUMonitor.getInstance().stopMonitoring();
    PerformanceMonitor.getInstance().stopMonitoring();
    RemotionProfiler.getInstance().stopProfiling();
    ReactProfiler.getInstance().stopProfiling();
    MainThreadMonitor.getInstance().stopMonitoring();
    setIsMonitoring(false);
  };

  const handleClearLogs = () => {
    console.clear();
    console.log('🧹 Debug logs cleared');
  };

  const handleExportMetrics = () => {
    const status = CPUMonitor.getInstance().getStatus();
    const data = {
      timestamp: new Date().toISOString(),
      cpuStatus: status,
      metrics: (window as any).cpuMetrics || [],
      perfMetrics: (window as any).perfMonitor?.getMetrics?.() || {}
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cpu-debug-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    console.log('📊 Metrics exported to file');
  };

  if (!isVisible) {
    return (
      <div 
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 10000,
          background: '#333',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px',
          cursor: 'pointer',
          border: '1px solid #555'
        }}
        onClick={onToggle}
      >
        🔥 CPU Debug
      </div>
    );
  }

  return (
    <div 
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        width: '320px',
        maxHeight: '400px',
        background: '#1a1a1a',
        color: 'white',
        border: '1px solid #444',
        borderRadius: '8px',
        padding: '15px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 10000,
        overflow: 'auto',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
      }}
    >
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '10px',
        borderBottom: '1px solid #444',
        paddingBottom: '10px'
      }}>
        <h3 style={{ margin: 0, fontSize: '14px' }}>🔥 CPU Debug Panel</h3>
        <button 
          onClick={onToggle}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ✕
        </button>
      </div>

      {/* Monitoring Controls */}
      <div style={{ marginBottom: '15px' }}>
        <div style={{ display: 'flex', gap: '5px', marginBottom: '10px' }}>
          <button 
            onClick={handleStartMonitoring}
            disabled={isMonitoring}
            style={{
              background: isMonitoring ? '#555' : '#007acc',
              border: 'none',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: isMonitoring ? 'not-allowed' : 'pointer',
              fontSize: '11px'
            }}
          >
            Start Monitor
          </button>
          
          <button 
            onClick={handleStopMonitoring}
            disabled={!isMonitoring}
            style={{
              background: !isMonitoring ? '#555' : '#d73a49',
              border: 'none',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: !isMonitoring ? 'not-allowed' : 'pointer',
              fontSize: '11px'
            }}
          >
            Stop Monitor
          </button>
        </div>

        <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap' }}>
          <button
            onClick={handleClearLogs}
            style={{
              background: '#6f42c1',
              border: 'none',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            Clear Logs
          </button>

          <button
            onClick={handleExportMetrics}
            style={{
              background: '#28a745',
              border: 'none',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            Export Data
          </button>

          <button
            onClick={() => {
              console.log('🎬 Remotion Profiler Status:', RemotionProfiler.getInstance().getStatus());
            }}
            style={{
              background: '#fd7e14',
              border: 'none',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            Remotion Status
          </button>

          <button
            onClick={() => {
              console.log('⚛️ React Profiler Status:', ReactProfiler.getInstance().getStatus());
            }}
            style={{
              background: '#61dafb',
              border: 'none',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            React Status
          </button>

          <button
            onClick={() => {
              console.log('🧵 Main Thread Status:', MainThreadMonitor.getInstance().getStatus());
            }}
            style={{
              background: '#9c27b0',
              border: 'none',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            Thread Status
          </button>
        </div>
      </div>

      {/* Status Display */}
      {cpuStatus && (
        <div>
          <div style={{ 
            background: '#2a2a2a', 
            padding: '10px', 
            borderRadius: '4px',
            marginBottom: '10px'
          }}>
            <div style={{ marginBottom: '5px' }}>
              <strong>Status:</strong> {isMonitoring ? '🟢 Monitoring' : '🔴 Stopped'}
            </div>
            <div style={{ marginBottom: '5px' }}>
              <strong>CPU:</strong> {cpuStatus.currentCPU?.toFixed(1) || 0}%
            </div>
            <div style={{ marginBottom: '5px' }}>
              <strong>Metrics:</strong> {cpuStatus.metricsCount || 0} samples
            </div>
          </div>

          {/* Video State */}
          <div style={{ 
            background: '#2a2a2a', 
            padding: '10px', 
            borderRadius: '4px',
            marginBottom: '10px'
          }}>
            <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>
              🎬 Video State:
            </div>
            <div style={{ marginBottom: '3px' }}>
              Playing: {cpuStatus.videoState?.isPlaying ? '▶️' : '⏸️'}
            </div>
            <div style={{ marginBottom: '3px' }}>
              Dragging: {cpuStatus.videoState?.isDragging ? '🔄' : '✋'}
            </div>
            <div style={{ marginBottom: '3px' }}>
              Frame: {cpuStatus.videoState?.currentFrame || 0}
            </div>
            <div style={{ marginBottom: '3px' }}>
              Seeks: {cpuStatus.videoState?.seekOperations || 0}
            </div>
            <div>
              Updates: {cpuStatus.videoState?.frameUpdates || 0}
            </div>
          </div>

          {/* Active Operations */}
          {cpuStatus.activeOperations && cpuStatus.activeOperations.length > 0 && (
            <div style={{ 
              background: '#2a2a2a', 
              padding: '10px', 
              borderRadius: '4px',
              marginBottom: '10px'
            }}>
              <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>
                🔧 Active Operations:
              </div>
              {cpuStatus.activeOperations.map((op: string, index: number) => (
                <div key={index} style={{ marginBottom: '2px' }}>
                  • {op}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div style={{ 
        background: '#2a2a2a', 
        padding: '10px', 
        borderRadius: '4px',
        fontSize: '10px',
        color: '#ccc'
      }}>
        <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>
          💡 Usage:
        </div>
        <div>1. Click "Start Monitor"</div>
        <div>2. Play/drag video to test</div>
        <div>3. Check console for detailed logs</div>
        <div>4. Click "Remotion Status" for frame data</div>
        <div>5. Export data for analysis</div>
      </div>

      {/* Auto-refresh toggle */}
      <div style={{ 
        marginTop: '10px',
        display: 'flex',
        alignItems: 'center',
        gap: '5px'
      }}>
        <input 
          type="checkbox" 
          checked={autoRefresh}
          onChange={(e) => setAutoRefresh(e.target.checked)}
          id="auto-refresh"
        />
        <label htmlFor="auto-refresh" style={{ fontSize: '10px' }}>
          Auto-refresh
        </label>
      </div>
    </div>
  );
};

export default CPUDebugPanel;
