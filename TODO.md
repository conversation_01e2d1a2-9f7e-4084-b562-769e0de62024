# Zoom Follow Cursor — Architecture & Implementation Plan

This document outlines the design, API, phases, and detailed tasks to implement a “Zoom Follow Cursor” feature that integrates with existing zoom configuration and cursor overlay data. It also includes styling/typography guidance for UI controls and designer notes. All filenames and language constructs are referenced as clickable links with line numbers for traceability.

## Current Context Summary

- Centralized zoom configuration and derived scale:
  - [`useZoomStore`](src/features/editor/store/use-zoom-store.ts:128) exposes config: `maxZoomScale`, `bezierControlPoints`, `defaultTiming`, `zoomOut`, `zoomArea`, `calculatedZoomScale`. Computed center via [`getZoomCenter()`](src/features/editor/store/use-zoom-store.ts:227).
- Timeline zoom items exist (duration-based):
  - [`ZoomEffectItem`](src/features/editor/timeline/items/zoom-effect.ts:10) represents discrete zoom effects with `startTime`, `endTime`, `maxZoomScale`, `zoomArea`.
- Canvas preview (Remotion Player context):
  - [`CanvasContainer`](src/features/editor/player/canvas-container.tsx:19) applies computed scale via `transform` and chooses `transformOrigin` from active zoom area/effects.
  - Uses `calculateZoomScaleFromTime` (centralized), `zoomTransformCache`, `zoomPerformanceMonitor`.
- Remotion export composition:
  - [`VideoEditorComposition`](src/remotion/VideoEditorComposition.tsx:47) supports `zoomConfig`, `cursorOverlayData`, `showCursorOverlay`. It renders a separate [`CursorOverlay`](src/remotion/CursorOverlay.tsx:36) for cursor visuals. Deterministic frame-based behavior is expected here.
- Frame tracking:
  - [`useCurrentPlayerFrame`](src/features/editor/hooks/use-current-frame.tsx:8) and `useCurrentPlayerFrameOptimized` drive current time.

- Cursor data sources:
  - Extension ingestion and pre-processing: [`extension-api.ts`](src/services/extension-api.ts:84) with methods to process and create `cursorOverlayData` frame-bucketed.
  - Composition props and render API support: [`render-api.ts`](src/services/render-api.ts:31) and [`VideoEditorComposition`](src/remotion/VideoEditorComposition.tsx:97) accept `cursorOverlayData`, `showCursorOverlay`.

Conclusion: We have cursor path data per frame and a robust zoom pipeline. Missing piece is a follow mode that drives transform origin and zoom level based on cursor, with configurable timings and easing, and deterministic behavior for both preview and export.

---

## Feature Goals

1) Add a flag to enable Zoom Follow Cursor.
2) When enabled:
   - Quickly zoom into the cursor position at effect start.
   - Maintain a user-configurable follow zoom level/distance while tracking cursor movement.
   - Smoothly zoom out at the end.
3) Provide parameters for:
   - Initial zoom-in speed/easing.
   - Follow zoom level (or distance).
   - Follow smoothing/easing.
   - Boundary constraints.
   - Final zoom-out timing/easing.
4) Effect must be visible/accurate in Remotion Player/canvas preview, support scrubbing, be deterministic.
5) Edge cases: cursor near edges, rapid movement, frame drops, missing cursor data.
6) Expose a simple API on composition props and store.
7) Include example usage and tests: timing, easing, and synchronization with cursor data.

---

## Data & Determinism Strategy

- Source of truth per frame: cursor position at current frame retrieved from `cursorOverlayData.frameData[frame]`. If missing, derive from nearest previous known point or the last known position within a time window, else fall back to `zoomArea` center.
- Deterministic easing:
  - Use pure functions of frame/time, not transient React state. For preview, use `currentFrame` → time (ms) mapping identical to export logic.
  - Provide a “smoothing” function that takes current cursor position and previous frame’s position strictly derived from data (no stateful EMA in preview). In export, always recompute from data sequence, e.g., via fixed-kernel smoothing or windowed averaging of past N frames.
- Boundary constraints:
  - Clamp final transform-origin target to stay within canvas normalized [0..1], accounting for zoom level so visible viewport stays inside content bounds.

---

## Proposed API

A) Composition props (Remotion/export side) in [`VideoEditorComposition`](src/remotion/VideoEditorComposition.tsx:47) and render API:
- `zoomFollowCursor?: boolean`
- `zoomFollowParams?: {`
  - `initial: { durationMs: number; easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bezier'; bezier?: [number, number, number, number] }`
  - `follow: { level: number; // e.g., 1.0=none, 2.0=2x zoom; smoothingWindowFrames: number; easing?: 'linear' | 'bezier'; bezier?: [number, number, number, number] }`
  - `constraints?: { clampToBounds: boolean; margin?: number }`
  - `final: { durationMs: number; easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bezier'; bezier?: [number, number, number, number] }`
  - `timeline?: { startTimeMs: number; endTimeMs: number } // optional override; otherwise derive from timeline zoom effect`
`}`

B) Editor store (preview side) augmentations in [`useZoomStore`](src/features/editor/store/use-zoom-store.ts:56):
- Add config keys:
  - `followCursorEnabled: boolean`
  - `followParams` mirroring the composition-side params.
  - `fallbackBehavior: 'hold-last' | 'snap-center'`
- Add setters:
  - `setFollowCursorEnabled(enabled: boolean)`
  - `setFollowParams(update: Partial<FollowParams>)`

C) Canvas behavior switch:
- If `followCursorEnabled` (preview) or composition prop `zoomFollowCursor` (export) is true:
  - Derive transformOrigin from cursor at frame with smoothing applied.
  - Derive zoom scale: During initial: ease from 1 → follow.level; During follow: hold follow.level; During final: ease from follow.level → 1.
  - Respect constraints.

D) Timeline integration:
- We can re-use [`ZoomEffectItem`](src/features/editor/timeline/items/zoom-effect.ts:10) intervals for timeline windows.
- Optional: Introduce a new item type in the future, but initially tie follow mode to existing zoom effect intervals for simplicity.

---

## Algorithms

1) Windowed smoothing per frame:
   - For frame F, compute window [F - W + 1 … F], collect cursor points; if none, fallback to last known ≤ F; if still none, fallback to default center.
   - Smoothed position = average(x), average(y) of available points in window.
   - Deterministic since depends only on finite window of frame-indexed data.

2) Transform origin clamping:
   - Given normalized target center (cx, cy) and zoom scale s, ensure the scaled viewport remains within [0..1]. If needed, clamp target so that the visible extents do not exceed boundaries. Use margin if provided.

3) Phase progression:
   - Given effect window [t0, t1], and params initial.durationMs = di, final.durationMs = df:
     - Initial phase: [t0, t0+di): zoom from 1 → follow.level; origin follows cursor already (for responsiveness), but can ease origin using same or separate easing.
     - Follow phase: [t0+di, t1-df]: maintain zoom at follow.level; origin follows smoothed cursor.
     - Final phase: [t1-df, t1]: zoom from follow.level → 1; origin continues to follow until end, or optionally ease back to center. Default: continue follow; option: `final.returnTo: 'last-cursor' | 'center'`.

4) Easing:
   - Provide small set of easings plus custom cubic-bezier. Implement via deterministic helpers.

---

## Files To Add/Modify

- Add utility for follow computation:
  - `src/features/editor/utils/zoom-follow.ts`:
    - `computeFollowCursorState(currentTimeMs, frame, fps, cursorData, effectWindow, params, fallback, canvasBounds, baseZoomConfig) → { originX, originY, scale }`
    - Pure, deterministic. Reused by both preview and export.
- Modify preview:
  - [`CanvasContainer`](src/features/editor/player/canvas-container.tsx:19): Before computing `transformOrigin` and `getCanvasZoomScale`, check follow mode. If active and there is an active effect window, call `computeFollowCursorState` to obtain `origin` and `scale`. Use `zoomTransformCache` to cache scale and consider caching origin for style string.
- Modify export composition:
  - [`VideoEditorComposition`](src/remotion/VideoEditorComposition.tsx:47): Mirror follow logic using same utility; wire `zoomFollowCursor` and `zoomFollowParams` from props or inputProps. Ensure scrubbing/determinism.
- Extend store:
  - [`useZoomStore`](src/features/editor/store/use-zoom-store.ts:56): Add fields and setters for follow mode and params.
- Example:
  - `src/remotion/examples/ZoomFollowCursorExample.tsx`: Composition demonstrating usage with included `cursorOverlayData`.
- Tests:
  - `src/features/editor/utils/__tests__/zoom-follow.test.ts` for algorithmic unit tests (timing/easing/clamping/fallback).
  - `src/remotion/__tests__/zoom-follow-composition.test.tsx` for composition-level timing and sync with cursor frame buckets.

---

## UI & Controls

Add controls within the existing zoom configuration panel:
- [`zoom-config-panel.tsx`](src/features/editor/components/zoom-config-panel.tsx:10)
  - Toggle: “Enable Zoom Follow Cursor”
  - Sliders/inputs:
    - Initial duration (ms), easing (select + cubic-bezier advanced).
    - Follow zoom level (scale), smoothing window (frames).
    - Constraints: clamp to bounds (switch), margin (px).
    - Final duration (ms), easing, optional “Return to center at end”.
  - Help text clarifies that follow uses imported/linked cursor data.

Designer notes:
- Group options into three subsections: “Initial Zoom In”, “Follow”, “Final Zoom Out”.
- Use consistent spacing, help text beneath labels, and show “Advanced” accordion for bezier points.

---

## Styling Guide

Typography:
- Title: Inter/Segoe UI, 16px/600
- Section Headings: 14px/600
- Labels: 12px/500
- Help text: 12px/400, muted-foreground
- Inputs: 12–14px
- Spacing: 12px between fields, 20px between sections

Color tokens (tailwind-ish semantic):
- Foreground: text-foreground
- Muted: text-muted-foreground
- Borders: border-border
- Backgrounds: bg-background, bg-accent on hover

Controls:
- Switch/toggle uses existing UI primitives in [`src/components/ui`](src/components/ui/switch.ts:11), [`input.tsx`](src/components/ui/input.tsx:12), [`slider.tsx`](src/components/ui/slider.tsx:20), [`select.tsx`](src/components/ui/select.tsx:19).

---

## Edge Cases Handling

- Cursor near edges: Clamp origin to keep viewport within bounds with margin.
- Rapid movement: Smoothing window reduces jitter; easing applies to scale transitions only to maintain readability. Optionally apply an origin easing factor (light).
- Frame drops: Deterministic since computation uses current frame and fixed history window; no dependency on real-time events.
- Missing cursor data: Fallback per policy. During initial and final phases, scale still transitions; origin falls back to last-known or center.
- Overlapping zoom effects: Last-added active effect wins (consistent with [`CanvasContainer`](src/features/editor/player/canvas-container.tsx:153)).

---

## Example Usage

In export (Remotion root or external render API):

```tsx
// VideoEditorComposition props
<VideoEditorComposition
  canvasSettings={...}
  trackItems={...}
  cursorOverlayData={cursorData}
  showCursorOverlay={true}
  zoomConfig={{
    ...defaultZoomConfig,
    zoomArea: { x: 0.3, y: 0.3, width: 0.4, height: 0.4 },
  }}
  zoomFollowCursor={true}
  zoomFollowParams={{
    initial: { durationMs: 300, easing: 'ease-out' },
    follow: { level: 2.0, smoothingWindowFrames: 6, easing: 'linear' },
    constraints: { clampToBounds: true, margin: 8 },
    final: { durationMs: 300, easing: 'ease-in' },
  }}
/>
```

In preview (editor UI), user toggles “Enable Zoom Follow Cursor” and sets params in the zoom panel; values propagate via [`useZoomStore`](src/features/editor/store/use-zoom-store.ts:128).

---

## Phased Plan

### Phase 1: Setup & Configuration
- [ ] Extend store to support follow mode and params
  - Add `followCursorEnabled`, `followParams`, `fallbackBehavior` to [`IZoomConfig`](src/features/editor/store/use-zoom-store.ts:6).
  - Add setters and defaults in [`DEFAULT_ZOOM_CONFIG`](src/features/editor/store/use-zoom-store.ts:85).
- [ ] Define types for `FollowParams`.
- [ ] Update render API typings to pass through `zoomFollowCursor`, `zoomFollowParams` (optional, if needed for CLI/server).

### Phase 2: Core Algorithm Utility
- [ ] Create [`zoom-follow.ts`](src/features/editor/utils/zoom-follow.ts:1)
  - Implement:
    - `getCursorPointAtFrame(frame, cursorData, fallbackPolicy)`
    - `smoothCursor(frame, windowFrames, cursorData, fallbackPolicy)`
    - `clampTransformOrigin(cx, cy, scale, margin)`
    - `ease(t, type, bezier?)`
    - `computeFollowCursorState(...)`
  - Ensure all pure functions, no side effects.

### Phase 3: Player/Preview Integration
- [ ] Modify [`CanvasContainer`](src/features/editor/player/canvas-container.tsx:19)
  - Detect active zoom effect time window.
  - If follow enabled, use `computeFollowCursorState` to get `{originX, originY, scale}` per frame.
  - Replace `transformOrigin` string and `getCanvasZoomScale` path for follow mode.
  - Keep caching strategy; avoid extra re-renders.

### Phase 4: Remotion Composition Integration
- [ ] Update [`VideoEditorComposition`](src/remotion/VideoEditorComposition.tsx:47)
  - Accept `zoomFollowCursor`, `zoomFollowParams`.
  - Reuse `computeFollowCursorState`.
  - Ensure scrubbing deterministic behavior (no React state for smoothing).
  - Keep compatibility with `cursorOverlayData` overlay.

### Phase 5: UI Controls
- [ ] Update [`zoom-config-panel.tsx`](src/features/editor/components/zoom-config-panel.tsx:10)
  - Add toggle and grouped controls for initial/follow/final/constraints.
  - Validation and helpful hints.
- [ ] Optional: Attach a mini preview indicator for follow window (e.g., shows smoothed point).

### Phase 6: Examples & Docs
- [ ] Add example composition: `ZoomFollowCursorExample.tsx`
- [ ] README section in `TODO.md` or a separate MD explaining parameters and best practices.

### Phase 7: Tests
- [ ] Unit tests `zoom-follow.test.ts`
  - Initial easing curve correctness.
  - Follow smoothing window correctness on synthetic cursor tracks.
  - Final easing back to 1.
  - Boundary clamping.
  - Missing data fallback.
- [ ] Composition tests `zoom-follow-composition.test.tsx`
  - Deterministic positions at key frames across phases.
  - Correlation with provided `cursorOverlayData`.

### Phase 8: Performance & Edge Verification
- [ ] Measure in existing debug panels:
  - Confirm negligible extra cost versus current zoom path.
- [ ] Validate with long tracks and sparse cursor data.

---

## Detailed Tasks

- Phase 1
  - [ ] Update [`IZoomConfig`](src/features/editor/store/use-zoom-store.ts:6) with:
    - `followCursorEnabled?: boolean`
    - `followParams?: { initial: {...}; follow: {...}; constraints?: {...}; final: {...} }`
    - `fallbackBehavior?: 'hold-last' | 'snap-center'`
  - [ ] Add defaults in [`DEFAULT_ZOOM_CONFIG`](src/features/editor/store/use-zoom-store.ts:85)
  - [ ] Add setters in store API.

- Phase 2
  - [ ] Implement `ease()` with named presets and cubic-bezier.
  - [ ] Implement frame-based smoothing using only past frames up to current.
  - [ ] Implement clamping respecting zoom scale and margin.

- Phase 3
  - [ ] Identify active effect window from `zoomEffects` as in current logic.
  - [ ] Route to follow path when enabled; keep legacy path otherwise.
  - [ ] Keep `zoomTransformCache.getTransform(scale)` and extend with origin string caching, if needed.

- Phase 4
  - [ ] Mirror selection of active window by props or fallback to config timing.
  - [ ] Ensure fallback when `cursorOverlayData` is absent: keep classic zoom behavior.

- Phase 5
  - [ ] Add UI toggles and inputs (Switch, Slider, Input, Select) consistent with UI library.
  - [ ] Tooltips/help for easing and smoothing.

- Phase 6
  - [ ] Example: show a video composition with cursor overlay and follow enabled.

- Phase 7
  - [ ] Tests for each helper, mock cursor data sets: straight line, jumps, gaps.
  - [ ] Snapshot test of computed `{origin, scale}` at specified frames.

- Phase 8
  - [ ] Bench via `zoom-performance-test` panel; inspect logs only when DEV flag set.

---

## Risks & Mitigations

- Risk: Overhead in per-frame calculations.
  - Mitigation: Use windowed averaging O(W). Keep W small (e.g., 6–10). Cache bezier lookups.
- Risk: Conflicts with existing zoom effects rendering.
  - Mitigation: Feature guarded by flag; reuse existing timing windows; choose last-active effect consistent with current behavior.
- Risk: Export mismatch vs preview.
  - Mitigation: Share the exact same pure utility and time/frame mapping.

---

## Acceptance Criteria

- Toggle enables/disables follow mode.
- Initial zoom-in and final zoom-out timings/easings behave as configured.
- During follow phase, origin tracks cursor smoothly; edges are clamped.
- Works in Player preview and Remotion export with identical results for given frame index.
- Tests pass for timing/easing/smoothing/clamping/fallback.
- Example renders visibly demonstrating the effect.