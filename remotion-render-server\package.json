{"name": "remotion-render-server", "version": "1.0.0", "type": "module", "description": "Remotion render server for video export functionality", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "node --test"}, "dependencies": {"@remotion/bundler": "^4.0.322", "@remotion/renderer": "^4.0.322", "express": "^5.1.0", "cors": "^2.8.5", "multer": "^2.0.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["remotion", "video", "render", "server", "export"]}