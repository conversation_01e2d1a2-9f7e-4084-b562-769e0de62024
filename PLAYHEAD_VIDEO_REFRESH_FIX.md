# Playhead Video Refresh Issue - Investigation & Solution

## Problem Description

When interacting with the playhead in the video timeline, the video canvas would refresh/flicker, causing a jarring user experience:

- **Clicking the playhead**: Video would refresh even without moving
- **Dragging the playhead**: Video would refresh when starting to drag and when dropping
- **Expected behavior**: Video should remain stable during playhead interactions, only updating the frame content

## Root Cause Analysis

Through systematic investigation, we identified that the video refresh was caused by **global state changes** in the `isPlayheadDragging` state, not the video seeking operations themselves.

### Investigation Process

1. **Eliminated obvious suspects**:
   - ❌ `seekTo()` calls - Video refresh persisted even with all seeking disabled
   - ❌ Frame update events - Video refresh persisted with `frameupdate` events blocked
   - ❌ Thumbnail loading - Video refresh persisted with thumbnail refresh disabled
   - ❌ Performance monitoring - Video refresh persisted with monitoring disabled

2. **Identified the actual cause**:
   - ✅ **Global state changes** - `setIsPlayheadDragging(true/false)` was triggering video refresh
   - The state change caused other components to suddenly re-enable expensive operations
   - Both setting the state to `true` (on drag start) and `false` (on drag end) caused refresh

## Solution Implementation

### Approach: Local State Override

Instead of using the global `isPlayheadDragging` state that triggers video refresh, we implemented a local state solution:

```typescript
// Added local dragging state
const [isLocallyDragging, setIsLocallyDragging] = useState(false);

// Use local state for position calculation
left: 40 + TIMELINE_OFFSET_CANVAS_LEFT + (isLocallyDragging ? dragPosition : position)
```

### Key Changes Made

1. **Disabled global state changes**:
   ```typescript
   // REMOVED: These were causing video refresh
   // setIsPlayheadDragging(true);  // On drag start
   // setIsPlayheadDragging(false); // On drag end
   ```

2. **Added local dragging state**:
   - `setIsLocallyDragging(true)` on mouse down
   - `setIsLocallyDragging(false)` on mouse up (with delay)

3. **Maintained seeking functionality**:
   ```typescript
   // Still perform seek to update video position
   if (hasMoved) {
     const time = unitsToTimeMs(dragPosition, scale.zoom);
     const targetFrame = Math.round((time * fps) / 1000);
     playerRef?.current?.seekTo(targetFrame);
   }
   ```

4. **Added delayed state reset**:
   ```typescript
   // Delay disabling local override to let seek complete
   setTimeout(() => {
     setIsLocallyDragging(false);
   }, 50);
   ```

## Files Modified

- `src/features/editor/timeline/playhead.tsx` - Main playhead component logic

## Results

✅ **Fixed clicking playhead**: No video refresh on simple clicks  
✅ **Fixed dragging playhead**: No video refresh during drag operations  
✅ **Maintained functionality**: Playhead still seeks correctly to new positions  
✅ **Smooth interactions**: Playhead moves smoothly and stays where dropped  

## Technical Details

### Why Global State Caused Issues

The `isPlayheadDragging` global state is used by multiple components for performance optimization:

- **Frame hooks**: Pause expensive `getCurrentFrame()` calls during dragging
- **Thumbnail loading**: Throttle thumbnail generation during interactions
- **Canvas rendering**: Reduce render frequency during dragging

When this state changed, it caused these components to suddenly resume expensive operations, triggering a cascade of updates that refreshed the video canvas.

### Why Local State Works

By using local state (`isLocallyDragging`) that only affects the playhead component's position calculation:

- No other components are notified of the state change
- No expensive operations are suddenly resumed
- Video canvas remains stable during interactions
- Seeking still works to update the actual video position

## Memory Note

For future reference: When dealing with playhead dragging, disabling global state changes during drag prevents video refresh while maintaining good UX through local state management.
