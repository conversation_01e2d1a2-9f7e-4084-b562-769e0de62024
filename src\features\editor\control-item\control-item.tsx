import React from "react";
import { LassoSelect } from "lucide-react";

// Simplified control item - no video editing controls
const ActiveControlItem = () => {
  return (
    <div className="mb-32 flex flex-1 flex-col items-center justify-center gap-4 text-muted-foreground">
      <LassoSelect />
      <span className="text-zinc-500">Timeline-only editor</span>
      <span className="text-xs text-center px-4">
        Use the timeline to arrange and trim your videos
      </span>
    </div>
  );
};

export const ControlItem = () => {
  return (
    <div className="flex w-[272px] flex-none border-l border-border/80 bg-sidebar">
      <ActiveControlItem />
    </div>
  );
};
