{"name": "remotion-captions", "private": true, "version": "0.0.72", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "render-server": "cd remotion-render-server && npm start", "render-server:dev": "cd remotion-render-server && npm run dev", "render-server:install": "cd remotion-render-server && npm install", "test-export": "node test-export.js"}, "dependencies": {"@designcombo/events": "^1.0.2", "@designcombo/frames": "^0.0.3", "@designcombo/state": "3.1.13", "@designcombo/timeline": "3.1.13", "@designcombo/types": "3.1.13", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.4.2", "@interactify/moveable": "0.0.2", "@interactify/selection": "^0.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.7", "@remotion/bundler": "^4.0.322", "@remotion/media-utils": "4.0.221", "@remotion/paths": "4.0.221", "@remotion/player": "4.0.221", "@remotion/renderer": "^4.0.322", "@remotion/shapes": "4.0.221", "@remotion/transitions": "4.0.221", "@tabler/icons-react": "^3.5.0", "@types/tinycolor2": "^1.4.6", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.5.6", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.441.0", "non.geist": "^1.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.5", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "remotion": "4.0.221", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tinycolor2": "^1.6.0", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/html2canvas": "^0.5.35", "@types/lodash": "^4.17.9", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "node-fetch": "^3.3.2", "postcss": "^8.4.47", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.12", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}