import { create } from "zustand";
import { generateId } from "@designcombo/timeline";
import { getVideoMetadata, VideoMetadata } from "../utils/file";
import { getEnhancedVideoMetadata, sanitizeDurationForRemotion } from "../utils/webm-metadata";

export interface LocalVideo {
  id: string;
  file: File;
  name: string;
  duration: number;
  width: number;
  height: number;
  aspectRatio: number;
  objectUrl: string;
  thumbnailUrl?: string;
  type: "video";
}

interface LocalVideosState {
  videos: LocalVideo[];
  isLoading: boolean;
  actions: {
    addVideo: (file: File) => Promise<LocalVideo>;
    removeVideo: (id: string) => void;
    clearAll: () => void;
    getVideoById: (id: string) => LocalVideo | undefined;
  };
}

const createVideoThumbnail = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    // Add timeout for longer videos
    const timeout = setTimeout(() => {
      reject(new Error("Thumbnail generation timed out after 30 seconds"));
      URL.revokeObjectURL(video.src);
    }, 30000); // 30 second timeout

    video.onloadedmetadata = () => {
      canvas.width = 160;
      canvas.height = 90;
      // For longer videos, capture frame at 2 seconds or 10% of duration, whichever is smaller
      const captureTime = Math.min(2, video.duration * 0.1);
      video.currentTime = captureTime;
    };

    video.onseeked = () => {
      clearTimeout(timeout);
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const thumbnailUrl = canvas.toDataURL("image/jpeg", 0.8);
        resolve(thumbnailUrl);
      } else {
        reject(new Error("Could not get canvas context"));
      }
      URL.revokeObjectURL(video.src);
    };

    video.onerror = () => {
      clearTimeout(timeout);
      reject(new Error(`Could not load video: ${file.name}`));
      URL.revokeObjectURL(video.src);
    };

    video.src = URL.createObjectURL(file);
    video.load();
  });
};

// Enhanced WebM duration detection for local videos store
const getWebMDurationForStore = async (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    let durationFound = false;

    // Method 1: Try to get duration from loadedmetadata
    video.onloadedmetadata = () => {
      if (!durationFound && isFinite(video.duration) && video.duration > 0) {
        durationFound = true;
        resolve(video.duration * 1000);
        URL.revokeObjectURL(video.src);
        return;
      }
    };

    // Method 2: Try to get duration from durationchange event
    video.ondurationchange = () => {
      if (!durationFound && isFinite(video.duration) && video.duration > 0) {
        durationFound = true;
        resolve(video.duration * 1000);
        URL.revokeObjectURL(video.src);
        return;
      }
    };

    // Method 3: Try to get duration by seeking to end
    video.onloadeddata = () => {
      if (!durationFound) {
        // Try seeking to a large time value to force duration calculation
        video.currentTime = Number.MAX_SAFE_INTEGER;
      }
    };

    video.onseeked = () => {
      if (!durationFound && isFinite(video.duration) && video.duration > 0) {
        durationFound = true;
        resolve(video.duration * 1000);
        URL.revokeObjectURL(video.src);
        return;
      }
    };

    video.onerror = () => {
      if (!durationFound) {
        reject(new Error(`Could not load WebM metadata for file: ${file.name}`));
        URL.revokeObjectURL(video.src);
      }
    };

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!durationFound) {
        reject(new Error(`WebM duration detection timed out for file: ${file.name}`));
        URL.revokeObjectURL(video.src);
      }
    }, 30000);

    video.src = URL.createObjectURL(file);
    video.load();
  });
};

const getVideoDuration = (file: File): Promise<number> => {
  return new Promise(async (resolve, reject) => {
    const video = document.createElement("video");
    const isWebM = file.type.includes('webm') || file.name.toLowerCase().endsWith('.webm');

    // Add timeout for longer videos
    const timeout = setTimeout(() => {
      reject(new Error("Video metadata loading timed out after 30 seconds"));
      URL.revokeObjectURL(video.src);
    }, 30000); // 30 second timeout

    video.onloadedmetadata = async () => {
      clearTimeout(timeout);
      let duration = video.duration * 1000; // Convert to milliseconds

      // Special handling for WebM files with infinite duration
      if (isWebM && (!isFinite(duration) || duration <= 0)) {
        try {
          URL.revokeObjectURL(video.src);
          duration = await getWebMDurationForStore(file);
        } catch (webmError) {
          console.warn(`WebM duration detection failed for ${file.name}, using fallback duration:`, webmError);
          // Fallback: Use a default duration of 10 seconds for WebM files
          duration = 10000; // 10 seconds in milliseconds
        }
      }

      // Validate duration
      if (!isFinite(duration) || duration <= 0) {
        reject(new Error(`Invalid video duration for file: ${file.name}. Duration: ${duration}`));
        URL.revokeObjectURL(video.src);
        return;
      }

      resolve(duration);
      URL.revokeObjectURL(video.src);
    };

    video.onerror = () => {
      clearTimeout(timeout);
      reject(new Error(`Could not load video metadata: ${file.name}`));
      URL.revokeObjectURL(video.src);
    };

    video.src = URL.createObjectURL(file);
    video.load();
  });
};

export const useLocalVideosStore = create<LocalVideosState>((set, get) => ({
  videos: [],
  isLoading: false,
  
  actions: {
    addVideo: async (file: File) => {
      set({ isLoading: true });

      try {
        const id = generateId();
        const objectUrl = URL.createObjectURL(file);

        // Use enhanced metadata detection for better WebM support
        let metadata;
        try {
          metadata = await getEnhancedVideoMetadata(file);
          console.log(`Enhanced metadata detected for ${file.name}:`, metadata);
        } catch (enhancedError) {
          console.warn(`Enhanced metadata failed for ${file.name}, falling back to standard detection:`, enhancedError);
          // Fallback to standard metadata detection
          const standardMetadata = await getVideoMetadata(file);
          metadata = {
            ...standardMetadata,
            isWebM: file.type.includes('webm') || file.name.toLowerCase().endsWith('.webm'),
            detectionMethod: 'standard' as const,
          };
        }

        // Sanitize duration for Remotion compatibility
        const sanitizedDuration = sanitizeDurationForRemotion(metadata.duration, file.name);

        let thumbnailUrl: string | undefined;
        try {
          thumbnailUrl = await createVideoThumbnail(file);
        } catch (error) {
          console.warn("Could not generate thumbnail:", error);
        }

        const localVideo: LocalVideo = {
          id,
          file,
          name: file.name,
          duration: sanitizedDuration,
          width: metadata.width,
          height: metadata.height,
          aspectRatio: metadata.aspectRatio,
          objectUrl,
          thumbnailUrl,
          type: "video",
        };

        set((state) => ({
          videos: [...state.videos, localVideo],
          isLoading: false,
        }));

        return localVideo;
      } catch (error) {
        set({ isLoading: false });
        throw error;
      }
    },
    
    removeVideo: (id: string) => {
      const video = get().videos.find((v) => v.id === id);
      if (video) {
        URL.revokeObjectURL(video.objectUrl);
        if (video.thumbnailUrl) {
          URL.revokeObjectURL(video.thumbnailUrl);
        }
      }
      
      set((state) => ({
        videos: state.videos.filter((v) => v.id !== id),
      }));
    },
    
    clearAll: () => {
      const { videos } = get();
      videos.forEach((video) => {
        URL.revokeObjectURL(video.objectUrl);
        if (video.thumbnailUrl) {
          URL.revokeObjectURL(video.thumbnailUrl);
        }
      });
      
      set({ videos: [] });
    },
    
    getVideoById: (id: string) => {
      return get().videos.find((v) => v.id === id);
    },
  },
}));
