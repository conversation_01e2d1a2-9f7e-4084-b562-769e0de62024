class MouseTrackerRecorder {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.mouseData = [];
        this.currentTab = null;
        this.sessionId = null;
        this.serverUrl = 'http://localhost:3001'; // Remotion render server URL
        this.editorUrl = 'http://localhost:5173'; // React video editor URL

        this.initializeElements();
        this.setupEventListeners();
        this.loadState();

        // Start debug state monitoring
        this.startDebugStateMonitoring();
    }

    createDebugStatePanel() {
        // Create debug state panel
        const debugStatePanel = document.createElement('div');
        debugStatePanel.id = 'debugStatePanel';
        debugStatePanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            display: none;
        `;

        debugStatePanel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">🔍 Debug State</div>
            <div id="debugStateContent"></div>
            <button id="toggleDebugState" style="margin-top: 5px; font-size: 10px;">Hide</button>
        `;

        document.body.appendChild(debugStatePanel);

        // Add toggle functionality
        document.getElementById('toggleDebugState').addEventListener('click', () => {
            const panel = document.getElementById('debugStatePanel');
            const button = document.getElementById('toggleDebugState');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                button.textContent = 'Hide';
            } else {
                panel.style.display = 'none';
                button.textContent = 'Show Debug';
            }
        });

        // Show panel initially if there are issues
        this.updateDebugStatePanel();
    }

    startDebugStateMonitoring() {
        // Check for stuck states every 5 seconds
        setInterval(() => {
            const now = Date.now();
            const timeSinceLastUpdate = now - this.debugState.lastUpdate;

            // If more than 30 seconds since last update and we're in a processing state
            if (timeSinceLastUpdate > 30000) {
                const processingStates = [
                    'recordingStopped',
                    'videoUploadStarted',
                    'cursorUploadStarted'
                ];

                const isProcessing = processingStates.some(state =>
                    this.debugState[state] && !this.debugState[state + 'Completed']
                );

                if (isProcessing) {
                    console.warn('⚠️ [DEBUG] Potential stuck state detected!');
                    document.getElementById('debugStatePanel').style.display = 'block';
                    this.updateDebugStatePanel();
                }
            }
        }, 5000);
    }

    updateDebugState(state, value = true) {
        this.debugState[state] = value;
        this.debugState.lastUpdate = Date.now();
        this.updateDebugStatePanel();
        console.log('🔍 [DEBUG] State updated:', state, '=', value);
    }

    updateDebugStatePanel() {
        const content = document.getElementById('debugStateContent');
        if (!content) return;

        const now = Date.now();
        const timeSinceLastUpdate = now - this.debugState.lastUpdate;

        let html = `<div style="color: #666;">Last update: ${Math.round(timeSinceLastUpdate / 1000)}s ago</div>`;

        Object.entries(this.debugState).forEach(([key, value]) => {
            if (key === 'lastUpdate') return;

            const color = value ? '#4CAF50' : '#ccc';
            const icon = value ? '✅' : '⏳';
            html += `<div style="color: ${color};">${icon} ${key}</div>`;
        });

        content.innerHTML = html;
    }

    initializeElements() {
        this.startBtn = document.getElementById('startRecording');
        this.stopBtn = document.getElementById('stopRecording');
        this.downloadBtn = document.getElementById('downloadRecording');
        this.openVideoEditorBtn = document.getElementById('openVideoEditor');
        this.refreshTargetBtn = document.getElementById('refreshTarget');
        this.testMouseTrackingBtn = document.getElementById('testMouseTracking');
        this.openTestPageBtn = document.getElementById('openTestPage');
        this.statusText = document.getElementById('recording-status');
        this.statusDot = document.getElementById('status-dot');
        this.resultsSection = document.getElementById('resultsSection');
        this.coordCount = document.getElementById('coordCount');
        this.clickCount = document.getElementById('clickCount');
        this.recordedVideo = document.getElementById('recordedVideo');
        this.mouseVisualization = document.getElementById('mouseVisualization');
        this.copyMouseDataBtn = document.getElementById('copyMouseData');
        this.tabInfoSection = document.getElementById('tabInfoSection');
        this.targetTabTitle = document.getElementById('targetTabTitle');
        this.targetTabUrl = document.getElementById('targetTabUrl');
        this.tabOptionTitle = document.getElementById('tabOptionTitle');
        this.debugInfo = document.getElementById('debugInfo');
        this.debugText = document.getElementById('debugText');

        // Initialize debug state tracking
        this.debugState = {
            recordingStarted: false,
            recordingStopped: false,
            mediaRecorderStopped: false,
            mouseDataReceived: false,
            videoUploadStarted: false,
            videoUploadCompleted: false,
            cursorUploadStarted: false,
            cursorUploadCompleted: false,
            editorLaunched: false,
            lastUpdate: Date.now()
        };

        this.createDebugStatePanel();
    }

    setupEventListeners() {
        this.startBtn.addEventListener('click', () => this.startRecording());
        this.stopBtn.addEventListener('click', () => this.stopRecording());
        this.downloadBtn.addEventListener('click', () => this.downloadRecording());
        this.openVideoEditorBtn.addEventListener('click', () => this.openVideoEditor());
        this.refreshTargetBtn.addEventListener('click', () => this.updateTargetTabInfo());
        this.testMouseTrackingBtn.addEventListener('click', () => this.testMouseTracking());
        this.openTestPageBtn.addEventListener('click', () => this.openTestPage());
        this.copyMouseDataBtn.addEventListener('click', () => this.copyMouseData());

        // Listen for recording type changes
        document.querySelectorAll('input[name="recordingType"]').forEach(radio => {
            radio.addEventListener('change', () => this.updateTargetTabInfo());
        });

        // Listen for mouse tracking checkbox changes
        document.getElementById('enableMouseTracking').addEventListener('change', () => this.updateTargetTabInfo());

        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
            console.log('📨 [DEBUG] Control page received message:', {
                origin: msg.origin,
                contentLength: msg.content?.length,
                sender: sender
            });

            if (msg.origin === 'background') {
                console.log('📨 [DEBUG] Processing mouse data from background script');
                this.handleMouseData(msg.content);
                sendResponse({ received: true, processedPoints: msg.content?.length || 0 });
            } else {
                console.log('📨 [DEBUG] Ignoring message from unknown origin:', msg.origin);
            }
        });
    }

    async loadState() {
        const result = await chrome.storage.sync.get(['isRecording']);
        if (result.isRecording) {
            this.updateUIState(true);
        }

        // Update target tab info on load
        await this.updateTargetTabInfo();
    }

    async getTargetTab() {
        // Get all tabs
        const tabs = await chrome.tabs.query({});

        // Find a suitable tab (not extension pages, not chrome:// pages)
        const suitableTabs = tabs.filter(tab =>
            (tab.url.startsWith('http://') || tab.url.startsWith('https://')) &&
            !tab.url.includes('chrome-extension://')
        );

        if (suitableTabs.length === 0) {
            return null;
        }

        // Don't use the active tab if it's the control page
        const controlUrl = chrome.runtime.getURL('control.html');
        const nonControlTabs = suitableTabs.filter(tab => tab.url !== controlUrl);

        if (nonControlTabs.length === 0) {
            return null;
        }

        // Prefer the most recently used suitable tab
        const sortedTabs = nonControlTabs.sort((a, b) => b.lastAccessed - a.lastAccessed);
        return sortedTabs[0];
    }

    async updateTargetTabInfo() {
        // Always get target tab info to update the option title
        const targetTab = await this.getTargetTab();

        // Update the tab option title
        if (targetTab) {
            this.tabOptionTitle.textContent = targetTab.title || 'Untitled';

            // Update debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = `Target tab ready: ${targetTab.title}`;
        } else {
            this.tabOptionTitle.textContent = 'No suitable tab found';

            // Update debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'No suitable tab found - open a web page first';
        }

        // Hide the target tab info section since we're showing the name in the option card
        this.tabInfoSection.style.display = 'none';
    }

    async testMouseTracking() {
        try {
            const targetTab = await this.getTargetTab();
            if (!targetTab) {
                alert('Please open a web page (http:// or https://) in another tab for mouse tracking.');
                return;
            }

            this.currentTab = targetTab;
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'Starting mouse tracking test...';

            await this.startMouseTracking();

            // Set a timeout to stop tracking and show results
            setTimeout(async () => {
                await this.stopMouseTracking();

                // Request mouse data from background
                chrome.runtime.sendMessage({
                    origin: 'control',
                    content: { action: 'stop' }
                });

                this.debugText.textContent = 'Test completed. Check console for results.';
            }, 10000); // Test for 10 seconds

        } catch (error) {
            console.error('Error testing mouse tracking:', error);
            this.debugText.textContent = `Test error: ${error.message}`;
        }
    }

    async openTestPage() {
        try {
            // Open a simple web page for testing
            // Using httpbin.org which provides a simple HTML page
            const testUrl = 'https://httpbin.org/html';
            await chrome.tabs.create({ url: testUrl });

            // Update target tab info after a short delay
            setTimeout(() => {
                this.updateTargetTabInfo();
            }, 1000);

            this.debugText.textContent = 'Test page opened. You can now start recording and mouse tracking.';

        } catch (error) {
            console.error('Error opening test page:', error);
            this.debugText.textContent = `Error opening test page: ${error.message}`;
        }
    }

    async startRecording() {
        try {
            const recordingType = document.querySelector('input[name="recordingType"]:checked').value;
            const enableMouseTracking = document.getElementById('enableMouseTracking').checked;

            // For tab recording and mouse tracking, we need a valid web page
            if (recordingType === 'tab' || enableMouseTracking) {
                // Get a suitable tab for recording/tracking
                const targetTab = await this.getTargetTab();
                if (!targetTab) {
                    const message = recordingType === 'tab'
                        ? 'Please open a web page (http:// or https://) in another tab for tab recording.'
                        : 'Please open a web page (http:// or https://) in another tab for mouse tracking.';
                    alert(message + '\n\nNote: The extension cannot record its own control page or Chrome internal pages.');
                    return;
                }
                this.currentTab = targetTab;

                // Update debug info
                this.debugText.textContent = `Using target tab: ${targetTab.title}`;
            }

            // Start screen recording
            await this.startScreenRecording(recordingType);

            // Start mouse tracking if enabled and we have a valid tab
            if (enableMouseTracking && this.currentTab) {
                await this.startMouseTracking();
            }

            this.isRecording = true;
            await chrome.storage.sync.set({ isRecording: true });
            this.updateUIState(true);
            this.updateDebugState('recordingStarted');

        } catch (error) {
            console.error('Error starting recording:', error);

            // Provide more helpful error messages
            let errorMessage = error.message;
            if (errorMessage.includes('activeTab permission')) {
                errorMessage = 'Cannot record this page. Please switch to a regular web page (http:// or https://) and try again.';
            } else if (errorMessage.includes('Chrome pages cannot be captured')) {
                errorMessage = 'Cannot record Chrome internal pages. Please open a regular web page and try again.';
            }

            alert(`Failed to start recording: ${errorMessage}`);
            this.debugText.textContent = `Error: ${errorMessage}`;
        }
    }

    async startScreenRecording(type) {
        try {
            let stream;

            if (type === 'tab') {
                // Make sure we have a valid tab and switch to it
                if (!this.currentTab) {
                    throw new Error('No suitable tab found for recording');
                }

                // Switch to the target tab first
                await chrome.tabs.update(this.currentTab.id, { active: true });
                await chrome.windows.update(this.currentTab.windowId, { focused: true });

                // Wait a moment for the tab to become active
                await new Promise(resolve => setTimeout(resolve, 500));

                // Record the tab using tabCapture
                stream = await new Promise((resolve, reject) => {
                    chrome.tabCapture.capture({
                        audio: true,
                        video: true
                    }, (capturedStream) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else if (capturedStream) {
                            resolve(capturedStream);
                        } else {
                            reject(new Error('Failed to capture tab'));
                        }
                    });
                });
            } else {
                // Record window or screen using desktopCapture
                const sources = type === 'screen' ? ['screen'] : ['window'];
                const streamId = await new Promise((resolve, reject) => {
                    chrome.desktopCapture.chooseDesktopMedia(sources, (streamId) => {
                        if (streamId) {
                            resolve(streamId);
                        } else {
                            reject(new Error('User cancelled screen selection'));
                        }
                    });
                });

                // Get media stream using the streamId
                stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: streamId
                        }
                    },
                    video: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: streamId
                        }
                    }
                });
            }

            // Setup MediaRecorder
            this.recordedChunks = [];

            // Try different codecs based on browser support
            let mimeType = 'video/webm;codecs=vp9';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'video/webm;codecs=vp8';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'video/webm';
                }
            }

            this.mediaRecorder = new MediaRecorder(stream, { mimeType });

            this.mediaRecorder.ondataavailable = (event) => {
                console.log('📹 [DEBUG] MediaRecorder data available, size:', event.data.size);
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                    console.log('📹 [DEBUG] Added chunk, total chunks:', this.recordedChunks.length);
                }
            };

            this.mediaRecorder.onstop = () => {
                console.log('📹 [DEBUG] MediaRecorder stopped, creating blob from', this.recordedChunks.length, 'chunks');
                const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
                console.log('📹 [DEBUG] Created video blob, size:', (blob.size / 1024 / 1024).toFixed(2), 'MB');

                const url = URL.createObjectURL(blob);
                this.recordedVideo.src = url;
                this.downloadBtn.disabled = false;

                // Stop all tracks to free up resources
                stream.getTracks().forEach(track => {
                    console.log('📹 [DEBUG] Stopping track:', track.kind, track.label);
                    track.stop();
                });

                console.log('📹 [DEBUG] MediaRecorder onstop completed');

                // Check if we can now upload (both video and mouse data ready)
                this.checkAndTriggerUpload();
            };

            this.mediaRecorder.onerror = (event) => {
                console.error('❌ [DEBUG] MediaRecorder error:', event.error);
                this.debugText.textContent = `Recording error: ${event.error?.message || 'Unknown error'}`;
            };

            this.mediaRecorder.onstart = () => {
                console.log('▶️ [DEBUG] MediaRecorder started');
            };

            this.mediaRecorder.onpause = () => {
                console.log('⏸️ [DEBUG] MediaRecorder paused');
            };

            this.mediaRecorder.onresume = () => {
                console.log('▶️ [DEBUG] MediaRecorder resumed');
            };

            this.mediaRecorder.start();

        } catch (error) {
            console.error('Screen recording error:', error);
            throw error;
        }
    }

    async startMouseTracking() {
        try {
            // Clear previous mouse data
            this.mouseData = [];

            // Show debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'Injecting mouse tracker...';

            console.log('Starting mouse tracking for tab:', this.currentTab.id, this.currentTab.url);

            // Inject the content script file
            await chrome.scripting.executeScript({
                target: { tabId: this.currentTab.id },
                files: ['content-script.js']
            });

            console.log('Content script injected successfully');
            this.debugText.textContent = 'Content script injected, starting tracking...';

            // Wait a moment for the script to initialize
            await new Promise(resolve => setTimeout(resolve, 200));

            // Send message to start tracking
            const response = await chrome.tabs.sendMessage(this.currentTab.id, { action: 'startMouseTracking' });
            console.log('Start tracking response:', response);

            this.debugText.textContent = 'Mouse tracker active! Move your mouse on the target tab.';

            // Set badge to indicate recording
            await chrome.action.setBadgeText({ text: 'REC' });
            await chrome.action.setBadgeBackgroundColor({ color: '#F00' });

        } catch (error) {
            console.error('Error starting mouse tracking:', error);
            this.debugText.textContent = `Error: ${error.message}`;

            // Try to provide more helpful error messages
            if (error.message.includes('Cannot access contents of url')) {
                this.debugText.textContent = 'Error: Cannot access this page. Try a regular website (http/https).';
            } else if (error.message.includes('No tab with id')) {
                this.debugText.textContent = 'Error: Target tab not found. Please refresh and try again.';
            }

            throw error;
        }
    }



    async stopRecording() {
        try {
            console.log('🛑 [DEBUG] Starting stop recording process...');
            this.debugText.textContent = 'Stopping recording...';

            // Stop screen recording
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                console.log('🛑 [DEBUG] Stopping MediaRecorder, current state:', this.mediaRecorder.state);
                this.mediaRecorder.stop();

                // Add timeout for MediaRecorder stop
                const stopTimeout = setTimeout(() => {
                    console.warn('⚠️ [DEBUG] MediaRecorder stop timeout - may be stuck');
                    this.debugText.textContent = 'Warning: Video recording stop timeout';
                }, 10000); // 10 second timeout

                // Clear timeout when onstop fires
                const originalOnStop = this.mediaRecorder.onstop;
                this.mediaRecorder.onstop = () => {
                    clearTimeout(stopTimeout);
                    console.log('✅ [DEBUG] MediaRecorder stopped successfully');
                    this.updateDebugState('mediaRecorderStopped');
                    if (originalOnStop) originalOnStop();
                };
            } else {
                console.log('🛑 [DEBUG] MediaRecorder already inactive or null');
            }

            // Stop mouse tracking
            console.log('🛑 [DEBUG] Stopping mouse tracking...');
            await this.stopMouseTracking();

            this.isRecording = false;
            await chrome.storage.sync.set({ isRecording: false });
            this.updateUIState(false);
            this.updateDebugState('recordingStopped');

            // Request mouse data from background with timeout
            console.log('🛑 [DEBUG] Requesting mouse data from background script...');
            const messageTimeout = setTimeout(() => {
                console.warn('⚠️ [DEBUG] Background script message timeout - may be stuck');
                this.debugText.textContent = 'Warning: Mouse data request timeout';
            }, 5000); // 5 second timeout

            chrome.runtime.sendMessage({
                origin: 'control',
                content: { action: 'stop' }
            }, (response) => {
                clearTimeout(messageTimeout);
                console.log('✅ [DEBUG] Background script response:', response);
            });

            // Show processing status
            this.statusText.textContent = 'Processing recording...';
            this.debugText.textContent = 'Preparing to upload video and cursor data...';
            console.log('🛑 [DEBUG] Stop recording process completed');

        } catch (error) {
            console.error('❌ [DEBUG] Error stopping recording:', error);
            this.debugText.textContent = `Stop recording error: ${error.message}`;
        }
    }

    async stopMouseTracking() {
        try {
            if (this.currentTab) {
                // Send message to stop tracking
                await chrome.tabs.sendMessage(this.currentTab.id, { action: 'stopMouseTracking' });
            }

            // Clear badge
            await chrome.action.setBadgeText({ text: '' });
            await chrome.action.setBadgeBackgroundColor({ color: '#FFF' });

        } catch (error) {
            console.error('Error stopping mouse tracking:', error);
            // Don't throw here as this is cleanup
        }
    }

    updateUIState(recording) {
        if (recording) {
            this.statusText.textContent = 'Recording...';
            this.statusDot.classList.add('recording');
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            this.openVideoEditorBtn.disabled = true;
        } else {
            this.statusText.textContent = 'Ready';
            this.statusDot.classList.remove('recording');
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.resultsSection.style.display = 'block';

            // Enable video editor button if we have recording data
            if (this.recordedChunks.length > 0) {
                this.openVideoEditorBtn.disabled = false;
            }
        }
    }

    handleMouseData(data) {
        console.log('🖱️ [DEBUG] Handling mouse data:', data.length, 'points');
        this.mouseData = data;
        this.debugText.textContent = `Received ${data.length} mouse points`;
        this.updateMouseStats();
        this.visualizeMouseData();
        this.updateDebugState('mouseDataReceived');

        console.log('🖱️ [DEBUG] Video chunks available:', this.recordedChunks.length > 0);
        console.log('🖱️ [DEBUG] Mouse data available:', this.mouseData.length > 0);

        // Check if we can now upload
        this.checkAndTriggerUpload();
    }

    checkAndTriggerUpload() {
        console.log('🔍 [DEBUG] Checking if upload can be triggered...');
        console.log('🖱️ [DEBUG] Video chunks available:', this.recordedChunks.length > 0);
        console.log('🖱️ [DEBUG] Mouse data available:', this.mouseData.length > 0);

        // Automatically upload data and launch video editor if we have both video and mouse data
        if (this.recordedChunks.length > 0 && this.mouseData.length > 0) {
            console.log('🚀 [DEBUG] Both video and mouse data available, starting upload process...');
            this.uploadDataAndLaunchEditor();
        } else {
            console.log('⏳ [DEBUG] Waiting for both video and mouse data before upload');
            if (this.recordedChunks.length === 0) {
                console.log('⏳ [DEBUG] Missing: Video chunks');
            }
            if (this.mouseData.length === 0) {
                console.log('⏳ [DEBUG] Missing: Mouse data');
            }
        }
    }

    updateMouseStats() {
        const clicks = this.mouseData.filter(item => item.action === 'click').length;
        this.coordCount.textContent = this.mouseData.length;
        this.clickCount.textContent = clicks;
    }

    async visualizeMouseData() {
        if (this.mouseData.length === 0) return;

        // Create canvas for visualization
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');

        // Clear previous visualization
        this.mouseVisualization.innerHTML = '';

        // Get dimensions from the tracked tab if available
        let maxX = 1920, maxY = 1080; // Default dimensions

        try {
            if (this.currentTab) {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: this.currentTab.id },
                    function: () => ({
                        width: window.innerWidth,
                        height: window.innerHeight
                    })
                });
                if (results && results[0] && results[0].result) {
                    maxX = results[0].result.width;
                    maxY = results[0].result.height;
                }
            }
        } catch (error) {
            console.warn('Could not get tab dimensions, using defaults');
        }

        // Draw mouse path
        ctx.strokeStyle = '#667eea';
        ctx.lineWidth = 2;
        ctx.beginPath();

        this.mouseData.forEach((item, index) => {
            const x = (item.coords.x / maxX) * canvas.width;
            const y = (item.coords.y / maxY) * canvas.height;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else if (item.action === 'click') {
                // Draw click indicator
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(x - 3, y - 3, 6, 6);
            } else {
                ctx.lineTo(x, y);
                ctx.stroke();
            }
        });

        this.mouseVisualization.appendChild(canvas);
    }

    downloadRecording() {
        if (this.recordedChunks.length === 0) return;

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `recording-${new Date().toISOString().slice(0, 19)}.webm`;
        a.click();
        URL.revokeObjectURL(url);
    }

    copyMouseData() {
        const data = {
            mouseActions: this.mouseData,
            recordingInfo: {
                totalCoordinates: this.mouseData.length,
                totalClicks: this.mouseData.filter(item => item.action === 'click').length,
                timestamp: new Date().toISOString()
            }
        };

        navigator.clipboard.writeText(JSON.stringify(data, null, 2)).then(() => {
            // Show feedback
            const originalText = this.copyMouseDataBtn.textContent;
            this.copyMouseDataBtn.textContent = 'Copied!';
            setTimeout(() => {
                this.copyMouseDataBtn.textContent = originalText;
            }, 2000);
        });
    }

    async uploadDataAndLaunchEditor() {
        try {
            console.log('📤 [DEBUG] Starting upload and launch process...');
            this.statusText.textContent = 'Uploading to video editor...';
            this.debugText.textContent = 'Starting upload process...';

            // Generate session ID
            this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            console.log('📤 [DEBUG] Generated session ID:', this.sessionId);

            // Upload video first
            console.log('📤 [DEBUG] Creating video blob from', this.recordedChunks.length, 'chunks');
            const videoBlob = new Blob(this.recordedChunks, { type: 'video/webm' });
            console.log('📤 [DEBUG] Video blob size:', (videoBlob.size / 1024 / 1024).toFixed(2), 'MB');

            console.log('📤 [DEBUG] Starting video upload...');
            const videoUploaded = await this.uploadVideo(videoBlob);

            if (!videoUploaded) {
                console.error('❌ [DEBUG] Video upload failed');
                throw new Error('Failed to upload video');
            }
            console.log('✅ [DEBUG] Video upload completed successfully');

            // Upload cursor data
            console.log('📤 [DEBUG] Starting cursor data upload...');
            const cursorUploaded = await this.uploadCursorData();

            if (!cursorUploaded) {
                console.error('❌ [DEBUG] Cursor data upload failed');
                throw new Error('Failed to upload cursor data');
            }
            console.log('✅ [DEBUG] Cursor data upload completed successfully');

            // Generate editor URL and launch
            const editorUrl = await this.generateEditorUrl();
            if (editorUrl) {
                this.showNotification('✅ Upload complete! Launching video editor...', 'success');
                await this.launchVideoEditor(editorUrl);
            }

        } catch (error) {
            console.error('Error uploading data and launching editor:', error);
            this.statusText.textContent = 'Upload failed';
            this.debugText.textContent = `Error: ${error.message}`;
        }
    }

    async uploadVideo(videoBlob) {
        const uploadStartTime = Date.now();
        try {
            console.log('📹 [DEBUG] Starting video upload...');
            this.debugText.textContent = 'Uploading video file...';
            this.updateDebugState('videoUploadStarted');

            const formData = new FormData();
            const filename = `recording-${Date.now()}.webm`;
            formData.append('video', videoBlob, filename);
            formData.append('sessionId', this.sessionId);

            console.log('📹 [DEBUG] Upload details:', {
                filename,
                sessionId: this.sessionId,
                blobSize: videoBlob.size,
                serverUrl: this.serverUrl
            });

            // Add timeout for upload
            const uploadTimeout = setTimeout(() => {
                console.warn('⚠️ [DEBUG] Video upload timeout (30s) - may be stuck');
                this.debugText.textContent = 'Warning: Video upload taking longer than expected...';
            }, 30000); // 30 second timeout warning

            const response = await fetch(`${this.serverUrl}/extension/upload-video`, {
                method: 'POST',
                body: formData
            });

            clearTimeout(uploadTimeout);
            const uploadDuration = Date.now() - uploadStartTime;
            console.log('📹 [DEBUG] Upload request completed in', uploadDuration, 'ms');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('📹 [DEBUG] Upload response:', result);

            if (result.success) {
                console.log('✅ [DEBUG] Video uploaded successfully:', result.filename);
                this.debugText.textContent = `Video uploaded successfully (${result.filename})`;
                this.updateDebugState('videoUploadCompleted');
                return true;
            } else {
                throw new Error(result.error || 'Video upload failed');
            }

        } catch (error) {
            const uploadDuration = Date.now() - uploadStartTime;
            console.error('❌ [DEBUG] Video upload error after', uploadDuration, 'ms:', error);
            this.debugText.textContent = `Video upload failed: ${error.message}`;

            // Additional error context
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                console.error('❌ [DEBUG] Network error - server may be down or unreachable');
                this.debugText.textContent = 'Video upload failed: Server unreachable';
            }

            return false;
        }
    }

    async uploadCursorData() {
        const uploadStartTime = Date.now();
        try {
            console.log('🖱️ [DEBUG] Starting cursor data upload...');
            this.debugText.textContent = 'Uploading cursor data...';
            this.updateDebugState('cursorUploadStarted');

            // Get recording metadata
            const recordingMetadata = {
                recordingType: document.querySelector('input[name="recordingType"]:checked')?.value || 'tab',
                timestamp: new Date().toISOString(),
                tabInfo: this.currentTab ? {
                    title: this.currentTab.title,
                    url: this.currentTab.url
                } : null
            };

            console.log('🖱️ [DEBUG] Cursor upload details:', {
                sessionId: this.sessionId,
                cursorDataLength: this.mouseData.length,
                recordingMetadata,
                serverUrl: this.serverUrl
            });

            // Add timeout for cursor data upload
            const uploadTimeout = setTimeout(() => {
                console.warn('⚠️ [DEBUG] Cursor data upload timeout (15s) - may be stuck');
                this.debugText.textContent = 'Warning: Cursor data upload taking longer than expected...';
            }, 15000); // 15 second timeout warning

            const response = await fetch(`${this.serverUrl}/extension/upload-cursor-data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    cursorData: this.mouseData,
                    recordingMetadata: recordingMetadata
                })
            });

            clearTimeout(uploadTimeout);
            const uploadDuration = Date.now() - uploadStartTime;
            console.log('🖱️ [DEBUG] Cursor upload request completed in', uploadDuration, 'ms');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('🖱️ [DEBUG] Cursor upload response:', result);

            if (result.success) {
                console.log('✅ [DEBUG] Cursor data uploaded successfully:', result.cursorPointsCount, 'points');
                this.debugText.textContent = `Cursor data uploaded (${result.cursorPointsCount} points)`;
                this.updateDebugState('cursorUploadCompleted');
                return true;
            } else {
                throw new Error(result.error || 'Cursor data upload failed');
            }

        } catch (error) {
            const uploadDuration = Date.now() - uploadStartTime;
            console.error('❌ [DEBUG] Cursor data upload error after', uploadDuration, 'ms:', error);
            this.debugText.textContent = `Cursor upload failed: ${error.message}`;

            // Additional error context
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                console.error('❌ [DEBUG] Network error - server may be down or unreachable');
                this.debugText.textContent = 'Cursor upload failed: Server unreachable';
            }

            return false;
        }
    }

    async generateEditorUrl() {
        try {
            this.debugText.textContent = 'Generating editor URL...';

            const response = await fetch(`${this.serverUrl}/extension/generate-editor-url`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.debugText.textContent = 'Editor URL generated successfully';
                return result.editorUrl;
            } else {
                throw new Error(result.error || 'Failed to generate editor URL');
            }

        } catch (error) {
            console.error('Editor URL generation error:', error);
            this.debugText.textContent = `URL generation failed: ${error.message}`;
            return null;
        }
    }

    async launchVideoEditor(editorUrl) {
        try {
            this.debugText.textContent = 'Launching video editor...';

            // Open the video editor in a new tab
            await chrome.tabs.create({ url: editorUrl });

            this.statusText.textContent = 'Video editor launched!';
            this.debugText.textContent = 'Video editor opened - your recording will be automatically added to the timeline';
            this.openVideoEditorBtn.disabled = false;
            this.updateDebugState('editorLaunched');

            // Show success notification
            this.showNotification('🎬 Video editor launched! Your recording will be automatically loaded.', 'success');

        } catch (error) {
            console.error('Error launching video editor:', error);
            this.debugText.textContent = `Failed to launch editor: ${error.message}`;
            this.showNotification('❌ Failed to launch video editor', 'error');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        `;

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(notification);

        // Remove notification after 4 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        document.body.removeChild(notification);
                    }
                    if (style.parentNode) {
                        document.head.removeChild(style);
                    }
                }, 300);
            }
        }, 4000);
    }

    async openVideoEditor() {
        if (this.sessionId) {
            // Re-open the editor with existing session
            const editorUrl = `${this.editorUrl}?extensionSession=${this.sessionId}`;
            await chrome.tabs.create({ url: editorUrl });
        } else if (this.recordedChunks.length > 0 && this.mouseData.length > 0) {
            // Upload data and launch editor
            await this.uploadDataAndLaunchEditor();
        } else {
            alert('No recording data available. Please record a video first.');
        }
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new MouseTrackerRecorder();
});
