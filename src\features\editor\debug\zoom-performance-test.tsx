import React, { useState, useEffect } from 'react';
import { zoomPerformanceMonitor, clearZoomCache } from '../utils/zoom-performance';

/**
 * Zoom Performance Test Component
 * 
 * This component provides a simple UI to test and monitor zoom performance
 * improvements. It can be temporarily added to your app for testing.
 */
export const ZoomPerformanceTest: React.FC = () => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [testResults, setTestResults] = useState<string>('');

  const startMonitoring = () => {
    setIsMonitoring(true);
    setTestResults('');
    zoomPerformanceMonitor.startMonitoring();
    console.log('🔍 Zoom performance monitoring started. Try zooming in your video!');
  };

  const stopMonitoring = () => {
    setIsMonitoring(false);
    zoomPerformanceMonitor.stopMonitoring();
    setTestResults('Check console for detailed performance report');
  };

  const clearCache = () => {
    clearZoomCache();
    console.log('🧹 Zoom cache cleared');
    setTestResults('Zoom cache cleared - performance may be slower until cache rebuilds');
  };

  const runPerformanceTest = () => {
    console.log('🧪 Running zoom performance test...');
    
    // Simulate zoom calculations
    const startTime = performance.now();
    let totalCalculations = 0;
    
    // Simulate 1000 zoom calculations
    for (let i = 0; i < 1000; i++) {
      // Simulate different time values
      const currentTime = i * 10; // 0ms to 10000ms
      const startTime = 1000;
      const endTime = 4000;
      
      // This would normally trigger zoom calculations
      if (currentTime >= startTime && currentTime <= endTime) {
        totalCalculations++;
      }
    }
    
    const duration = performance.now() - startTime;
    const result = `Performance Test Results:
- Total calculations: ${totalCalculations}
- Time taken: ${duration.toFixed(2)}ms
- Average per calculation: ${(duration / totalCalculations).toFixed(3)}ms
- Calculations per second: ${(totalCalculations / (duration / 1000)).toFixed(0)}`;
    
    console.log(result);
    setTestResults(result);
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 9999,
      maxWidth: '300px',
      border: '1px solid #333'
    }}>
      <h3 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>🔍 Zoom Performance Test</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <button
          onClick={isMonitoring ? stopMonitoring : startMonitoring}
          style={{
            background: isMonitoring ? '#dc3545' : '#28a745',
            color: 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '5px',
            fontSize: '11px'
          }}
        >
          {isMonitoring ? 'Stop Monitor' : 'Start Monitor'}
        </button>
        
        <button
          onClick={clearCache}
          style={{
            background: '#ffc107',
            color: 'black',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '5px',
            fontSize: '11px'
          }}
        >
          Clear Cache
        </button>
        
        <button
          onClick={runPerformanceTest}
          style={{
            background: '#17a2b8',
            color: 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '11px'
          }}
        >
          Run Test
        </button>
      </div>
      
      <div style={{ fontSize: '10px', color: '#ccc', marginBottom: '10px' }}>
        Status: {isMonitoring ? '🟢 Monitoring' : '🔴 Stopped'}
      </div>
      
      {testResults && (
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '8px',
          borderRadius: '4px',
          fontSize: '10px',
          whiteSpace: 'pre-line',
          maxHeight: '150px',
          overflow: 'auto'
        }}>
          {testResults}
        </div>
      )}
      
      <div style={{ fontSize: '9px', color: '#888', marginTop: '10px' }}>
        💡 Tips:
        <br />• Start monitoring before testing zoom
        <br />• Check browser console for detailed reports
        <br />• Clear cache to test cold performance
      </div>
    </div>
  );
};

/**
 * Hook to easily add performance testing to any component
 */
export const useZoomPerformanceTest = (enabled: boolean = false) => {
  useEffect(() => {
    if (!enabled) return;
    
    // Add keyboard shortcut for quick testing
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'Z') {
        console.log('🔍 Quick zoom performance test triggered');
        zoomPerformanceMonitor.startMonitoring();
        
        // Auto-stop after 10 seconds
        setTimeout(() => {
          zoomPerformanceMonitor.stopMonitoring();
        }, 10000);
      }
    };
    
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [enabled]);
};

export default ZoomPerformanceTest;
