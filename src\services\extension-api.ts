/**
 * Extension Integration API Service
 * Handles communication with Chrome extension data uploaded to the render server
 */

export interface ExtensionCursorData {
  coords: {
    x: number;
    y: number;
  };
  action: 'move' | 'click';
  timestamp: number;
}

export interface ExtensionRecordingMetadata {
  recordingType: 'tab' | 'window' | 'screen';
  timestamp: string;
  tabInfo?: {
    title: string;
    url: string;
  };
}

export interface ExtensionSessionData {
  videoFile?: {
    filename: string;
    originalName: string;
    path: string;
    url: string;
    uploadTime: string;
  };
  cursorData?: ExtensionCursorData[];
  recordingMetadata?: ExtensionRecordingMetadata;
  cursorUploadTime?: string;
}

export interface ExtensionSessionResponse {
  success: boolean;
  sessionId: string;
  data: ExtensionSessionData;
}

export class ExtensionAPI {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get session data from the extension
   */
  async getSessionData(sessionId: string): Promise<ExtensionSessionResponse | null> {
    try {
      const response = await fetch(`${this.baseUrl}/extension/session/${sessionId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          console.warn(`Extension session ${sessionId} not found`);
          return null;
        }
        throw new Error(`Failed to fetch session data: ${response.statusText}`);
      }

      return response.json();
    } catch (error) {
      console.error('Error fetching extension session data:', error);
      return null;
    }
  }

  /**
   * Check if the server is available
   */
  async isServerAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Process cursor data for timeline integration
   */
  processCursorDataForTimeline(
    cursorData: ExtensionCursorData[],
    videoDuration: number,
    fps: number = 30
  ) {
    if (!cursorData || cursorData.length === 0) {
      return [];
    }

    // Sort by timestamp
    const sortedData = [...cursorData].sort((a, b) => a.timestamp - b.timestamp);
    
    // Get time range
    const startTime = sortedData[0].timestamp;
    const endTime = sortedData[sortedData.length - 1].timestamp;
    const recordingDuration = endTime - startTime;

    // Convert to timeline format
    return sortedData.map((point, index) => {
      const relativeTime = point.timestamp - startTime;
      const normalizedTime = recordingDuration > 0 ? relativeTime / recordingDuration : 0;
      const videoTime = normalizedTime * videoDuration * 1000; // Convert to milliseconds
      const frame = Math.round((videoTime / 1000) * fps);

      return {
        id: `cursor-${index}`,
        frame,
        time: videoTime,
        x: point.coords.x,
        y: point.coords.y,
        action: point.action,
        originalTimestamp: point.timestamp,
      };
    });
  }

  /**
   * Create cursor overlay data for Remotion composition
   */
  createCursorOverlayData(
    cursorData: ExtensionCursorData[],
    canvasWidth: number,
    canvasHeight: number,
    videoDuration: number,
    fps: number = 30
  ) {
    const timelineData = this.processCursorDataForTimeline(cursorData, videoDuration, fps);
    
    if (timelineData.length === 0) {
      return null;
    }

    // Group by frames for efficient rendering
    const frameData: Record<number, typeof timelineData[0][]> = {};
    
    timelineData.forEach(point => {
      if (!frameData[point.frame]) {
        frameData[point.frame] = [];
      }
      frameData[point.frame].push(point);
    });

    return {
      frameData,
      totalFrames: Math.ceil(videoDuration * fps),
      canvasWidth,
      canvasHeight,
      metadata: {
        totalPoints: cursorData.length,
        clicks: cursorData.filter(p => p.action === 'click').length,
        duration: videoDuration,
      }
    };
  }

  /**
   * Extract session ID from URL parameters
   */
  static getSessionIdFromUrl(): string | null {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('extensionSession');
  }

  /**
   * Update URL with session ID without page reload
   */
  static updateUrlWithSession(sessionId: string) {
    const url = new URL(window.location.href);
    url.searchParams.set('extensionSession', sessionId);
    window.history.replaceState({}, '', url.toString());
  }

  /**
   * Remove session ID from URL
   */
  static clearSessionFromUrl() {
    const url = new URL(window.location.href);
    url.searchParams.delete('extensionSession');
    window.history.replaceState({}, '', url.toString());
  }
}

// Default instance
export const extensionApi = new ExtensionAPI();
