import { But<PERSON> } from "@/components/ui/button";
import { dispatch } from "@designcombo/events";
import {
  ACTIVE_SPLIT,
  LAYER_CLONE,
  LAYER_DELETE,
  TIMELINE_SCALE_CHANGED,
  HISTORY_UNDO,
  HISTORY_REDO,
} from "@designcombo/state";
import { PLAYER_PAUSE, PLAYER_PLAY, PLAYER_SEEK_BY } from "../constants/events";
import { frameToTimeString, getCurrentTime, timeToString } from "../utils/time";
import useStore from "../store/use-store";
import {
  SquareSplitHorizontal,
  Trash,
  Undo2,
  Redo2,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  ZoomIn,
  ZoomOut,
  Copy,
  Fullscreen
} from "lucide-react";
import {
  getFitZoomLevel,
  getNextZoomLevel,
  getPreviousZoomLevel,
  getZoomByIndex,
} from "../utils/timeline";
import { useCurrentPlayerFrameOptimized } from "../hooks/use-current-frame-optimized";
import { Slider } from "@/components/ui/slider";
import { useEffect, useState } from "react";
import useUpdateAnsestors from "../hooks/use-update-ansestors";
import { ITimelineScaleState } from "@designcombo/types";


const Header = () => {
  const [playing, setPlaying] = useState(false);
  const {
    duration,
    fps,
    scale,
    playerRef,
    activeIds
  } = useStore();

  useUpdateAnsestors({ playing, playerRef });

  const currentFrame = useCurrentPlayerFrameOptimized(playerRef!);

  const doActiveDelete = () => {
    dispatch(LAYER_DELETE);
  };

  const doActiveSplit = () => {
    dispatch(ACTIVE_SPLIT, {
      payload: {},
      options: {
        time: getCurrentTime(),
      },
    });
  };

  const changeScale = (scale: ITimelineScaleState) => {
    dispatch(TIMELINE_SCALE_CHANGED, {
      payload: {
        scale,
      },
    });
  };

  const handlePlay = () => {
    dispatch(PLAYER_PLAY);
  };

  const handlePause = () => {
    dispatch(PLAYER_PAUSE);
  };

  const handleUndo = () => {
    dispatch(HISTORY_UNDO);
  };

  const handleRedo = () => {
    dispatch(HISTORY_REDO);
  };





  const handleSkipBackward = () => {
    dispatch(PLAYER_SEEK_BY, {
      payload: {
        frames: -1,
      },
    });
  };

  const handleSkipForward = () => {
    dispatch(PLAYER_SEEK_BY, {
      payload: {
        frames: 1,
      },
    });
  };

  useEffect(() => {
    playerRef?.current?.addEventListener("play", () => {
      setPlaying(true);
    });
    playerRef?.current?.addEventListener("pause", () => {
      setPlaying(false);
    });
    return () => {
      playerRef?.current?.removeEventListener("play", () => {
        setPlaying(true);
      });
      playerRef?.current?.removeEventListener("pause", () => {
        setPlaying(false);
      });
    };
  }, [playerRef]);

  return (
    <div className="relative h-12 flex-none border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="absolute inset-0 flex items-center">
        <div className="w-full h-8 grid grid-cols-[1fr_auto_1fr] items-center gap-3 px-3">
          {/* Left Section - History & Layer Actions */}
          <div className="flex items-center gap-1">
            {/* History Controls */}
            <div className="flex items-center bg-muted/50 rounded-md p-0.5">
              <Button
                onClick={handleUndo}
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground"
              >
                <Undo2 size={16} />
              </Button>
              <Button
                onClick={handleRedo}
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground"
              >
                <Redo2 size={16} />
              </Button>
            </div>

            {/* Layer Actions */}
            <div className="flex items-center bg-muted/50 rounded-md p-0.5 ml-1.5">
              <Button
                disabled={!activeIds.length}
                onClick={doActiveDelete}
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground disabled:opacity-50"
              >
                <Trash size={12} className="mr-1" />
                Delete
              </Button>
              <Button
                disabled={!activeIds.length}
                onClick={doActiveSplit}
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground disabled:opacity-50"
              >
                <SquareSplitHorizontal size={14} className="mr-1" />
                Split
              </Button>
              <Button
                disabled={!activeIds.length}
                onClick={() => {
                  dispatch(LAYER_CLONE);
                }}
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground disabled:opacity-50"
              >
                <Copy size={12} className="mr-1" />
                Clone
              </Button>
            </div>
          </div>

          {/* Center Section - Player Controls & Time Display */}
          <div className="flex items-center justify-center">
            {/* Player Controls */}
            <div className="flex items-center bg-muted/50 rounded-md p-0.5 mr-3">
              <Button
                onClick={handleSkipBackward}
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-muted-foreground hover:text-foreground"
              >
                <SkipBack size={14} />
              </Button>
              <Button
                onClick={() => {
                  if (playing) {
                    return handlePause();
                  }
                  handlePlay();
                }}
                variant="ghost"
                size="icon"
                className={`h-8 w-8 mx-0.5 ${playing ? 'bg-transparent text-foreground hover:bg-accent' : 'text-muted-foreground hover:text-foreground hover:bg-accent'}`}
              >
                {playing ? (
                  <Pause size={16} />
                ) : (
                  <Play size={16} />
                )}
              </Button>
              <Button
                onClick={handleSkipForward}
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-muted-foreground hover:text-foreground"
              >
                <SkipForward size={14} />
              </Button>
            </div>

            {/* Time Display */}
            <div className="flex items-center gap-1.5 text-xs font-mono bg-muted/30 rounded-md px-2.5 py-1">
              <div
                className="font-medium text-foreground min-w-[3.5rem] text-center"
                data-current-time={currentFrame / fps}
                id="video-current-time"
              >
                {frameToTimeString({ frame: currentFrame }, { fps })}
              </div>
              <span className="text-muted-foreground">/</span>
              <div className="text-muted-foreground min-w-[3.5rem] text-center">
                {timeToString({ time: duration })}
              </div>
            </div>


          </div>

          {/* Right Section - Zoom Controls */}
          <div className="flex items-center justify-end gap-1.5">
            <ZoomControl
              scale={scale}
              onChangeTimelineScale={changeScale}
              duration={duration}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const ZoomControl = ({
  scale,
  onChangeTimelineScale,
  duration,
}: {
  scale: ITimelineScaleState;
  onChangeTimelineScale: (scale: ITimelineScaleState) => void;
  duration: number;
}) => {
  const [localValue, setLocalValue] = useState(scale.index);

  useEffect(() => {
    setLocalValue(scale.index);
  }, [scale.index]);

  const onZoomOutClick = () => {
    const previousZoom = getPreviousZoomLevel(scale);
    onChangeTimelineScale(previousZoom);
  };

  const onZoomInClick = () => {
    const nextZoom = getNextZoomLevel(scale);
    onChangeTimelineScale(nextZoom);
  };

  const onZoomFitClick = () => {
    const fitZoom = getFitZoomLevel(duration, scale.zoom);
    onChangeTimelineScale(fitZoom);
  };

  return (
    <div className="flex items-center bg-muted/50 rounded-md p-0.5">
      <Button
        size="icon"
        variant="ghost"
        onClick={onZoomOutClick}
        className="h-7 w-7 text-muted-foreground hover:text-foreground"
      >
        <ZoomOut size={12} />
      </Button>
      <Slider
        className="w-20 mx-1.5"
        value={[localValue]}
        min={0}
        max={12}
        step={1}
        onValueChange={(e: number[]) => {
          setLocalValue(e[0]); // Update local state
        }}
        onValueCommit={() => {
          const zoom = getZoomByIndex(localValue);
          onChangeTimelineScale(zoom); // Propagate value to parent when user commits change
        }}
      />
      <Button
        size="icon"
        variant="ghost"
        onClick={onZoomInClick}
        className="h-7 w-7 text-muted-foreground hover:text-foreground"
      >
        <ZoomIn size={12} />
      </Button>
      <div className="w-px h-3 bg-border mx-1" />
      <Button
        onClick={onZoomFitClick}
        variant="ghost"
        size="icon"
        className="h-7 w-7 text-muted-foreground hover:text-foreground"
      >
        <Fullscreen size={18} />
      </Button>
    </div>
  );
};

export default Header;
