/**
 * Performance test utilities for measuring the impact of our optimizations
 */

export const measureFrameUpdatePerformance = () => {
  let frameUpdateCount = 0;
  let renderCount = 0;
  const startTime = performance.now();
  
  const resetCounters = () => {
    frameUpdateCount = 0;
    renderCount = 0;
  };
  
  const trackFrameUpdate = () => {
    frameUpdateCount++;
  };
  
  const trackRender = () => {
    renderCount++;
  };
  
  const getStats = () => {
    const duration = performance.now() - startTime;
    return {
      frameUpdatesPerSecond: (frameUpdateCount / duration) * 1000,
      rendersPerSecond: (renderCount / duration) * 1000,
      totalFrameUpdates: frameUpdateCount,
      totalRenders: renderCount,
      duration: duration
    };
  };
  
  return {
    resetCounters,
    trackFrameUpdate,
    trackRender,
    getStats
  };
};

// Test function to verify our optimizations
export const testPlayheadDraggingPerformance = async (
  playerRef: React.RefObject<any>,
  setIsPlayheadDragging: (dragging: boolean) => void
) => {
  console.log('🧪 Starting playhead dragging performance test...');
  
  const monitor = measureFrameUpdatePerformance();
  
  // Simulate playhead dragging
  setIsPlayheadDragging(true);
  
  // Simulate rapid seeking during dragging
  const testDuration = 2000; // 2 seconds
  const seekInterval = 16; // ~60fps
  let frame = 0;
  
  const seekLoop = setInterval(() => {
    if (playerRef.current) {
      playerRef.current.seekTo(frame);
      frame = (frame + 1) % 300; // Cycle through 300 frames
      monitor.trackFrameUpdate();
    }
  }, seekInterval);
  
  // Stop test after duration
  setTimeout(() => {
    clearInterval(seekLoop);
    setIsPlayheadDragging(false);
    
    const stats = monitor.getStats();
    console.log('🧪 Performance test results:', stats);
    
    // Log performance assessment
    if (stats.frameUpdatesPerSecond < 30) {
      console.log('✅ GOOD: Frame updates are throttled (< 30fps)');
    } else {
      console.log('⚠️ WARNING: Frame updates may be too frequent (> 30fps)');
    }
    
    if (stats.rendersPerSecond < 20) {
      console.log('✅ GOOD: Component re-renders are minimized (< 20/sec)');
    } else {
      console.log('⚠️ WARNING: Too many component re-renders (> 20/sec)');
    }
  }, testDuration);
};
